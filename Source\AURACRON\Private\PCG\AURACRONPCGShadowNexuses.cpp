// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "Components/NiagaraComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGPoint.h"
#include "PCGVolume.h"

void AAURACRONPCGEnvironment::GenerateShadowNexuses()
{
    if (!HasAuthority())
    {
        return;
    }

    // Limpar dados existentes
    ShadowNexusData.Empty();
    
    // Verificar se o componente PCG é válido
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironment::GenerateShadowNexuses - PCGComponent is invalid"));
        return;
    }

    // Obter dimensões do mapa para posicionamento
    float MapRadius = FAURACRONMapDimensions::MAP_RADIUS;
    float InnerRadius = MapRadius * 0.3f;
    float OuterRadius = MapRadius * 0.7f;

    // Determinar número de nexos com base na escala de atividade
    int32 NexusCount = FMath::RandRange(3, 5) + FMath::FloorToInt(ActivityScale * 3.0f);
    
    // Usar amostragem de disco de Poisson para distribuição uniforme
    TArray<FVector> NexusPositions;
    UAURACRONPCGMathLibrary::GeneratePoissonDiscDistribution(
        NexusPositions,
        NexusCount,
        FVector(0, 0, 0),
        FVector(MapRadius, MapRadius, 0),
        1000.0f // Distância mínima entre nexos
    );

    // Criar cada nexo sombrio
    for (int32 i = 0; i < NexusPositions.Num(); ++i)
    {
        FShadowNexusData NewNexus;
        
        // Posicionar o nexo usando a distribuição de Poisson
        FVector Position = NexusPositions[i];
        
        // Ajustar para estar dentro do anel entre raio interno e externo
        float DistanceFromCenter = Position.Size2D();
        if (DistanceFromCenter < InnerRadius || DistanceFromCenter > OuterRadius)
        {
            // Normalizar e reposicionar
            FVector Direction = Position.GetSafeNormal2D();
            float NewDistance = FMath::RandRange(InnerRadius, OuterRadius);
            Position = Direction * NewDistance;
        }
        
        // Ajustar altura com base na topografia
        Position.Z = GetEnvironmentHeightAt(Position) + FMath::RandRange(200.0f, 500.0f);
        
        // Configurar dados do nexo
        NewNexus.Position = Position;
        NewNexus.Rotation = FRotator(0, FMath::RandRange(0.0f, 360.0f), 0);
        NewNexus.Scale = FVector(1.0f) * FMath::RandRange(0.8f, 1.2f);
        NewNexus.Energy = FMath::RandRange(0.7f, 1.0f);
        NewNexus.PulseRate = FMath::RandRange(0.5f, 2.0f);
        NewNexus.TimeOffset = FMath::RandRange(0.0f, 10.0f);
        
        // Determinar tipo de nexo (variações visuais)
        NewNexus.NexusType = FMath::RandRange(0, 2);
        
        // Adicionar à lista
        ShadowNexusData.Add(NewNexus);
        
        // Configurar parâmetros PCG para este nexo
        FString ParameterName = FString::Printf(TEXT("ShadowNexus_%d_Position"), i);
        SetPCGParameterModern(ParameterName, FVector(Position.X, Position.Y, Position.Z), TEXT("PurgatoryRealm"));
        
        ParameterName = FString::Printf(TEXT("ShadowNexus_%d_Scale"), i);
        SetPCGParameterModern(ParameterName, NewNexus.Scale, TEXT("PurgatoryRealm"));
        
        ParameterName = FString::Printf(TEXT("ShadowNexus_%d_Energy"), i);
        SetPCGParameterModern(ParameterName, FVector(NewNexus.Energy, 0, 0), TEXT("PurgatoryRealm"));
        
        ParameterName = FString::Printf(TEXT("ShadowNexus_%d_Type"), i);
        SetPCGParameterModern(ParameterName, FVector(NewNexus.NexusType, 0, 0), TEXT("PurgatoryRealm"));
    }
    
    // Configurar número total de nexos para o PCG
    SetPCGParameterModern(TEXT("ShadowNexusCount"), FVector(ShadowNexusData.Num(), 0, 0), TEXT("PurgatoryRealm"));
    
    // Inicializar efeitos de partículas para cada nexo
    if (ShadowNexusParticleSystem)
    {
        for (int32 i = 0; i < ShadowNexusData.Num(); ++i)
        {
            UNiagaraComponent* NexusEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                ShadowNexusParticleSystem,
                ShadowNexusData[i].Position,
                ShadowNexusData[i].Rotation
            );
            
            if (NexusEffect)
            {
                // Configurar parâmetros do sistema de partículas
                NexusEffect->SetVariableFloat(TEXT("Energy"), ShadowNexusData[i].Energy);
                NexusEffect->SetVariableFloat(TEXT("PulseRate"), ShadowNexusData[i].PulseRate);
                NexusEffect->SetVariableInt(TEXT("NexusType"), ShadowNexusData[i].NexusType);
                
                // Armazenar referência ao componente
                ShadowNexusData[i].ParticleEffect = NexusEffect;
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateShadowNexuses - Generated %d Shadow Nexuses"), ShadowNexusData.Num());
}

void AAURACRONPCGEnvironment::UpdateShadowNexuses(float Time)
{
    // Atualizar efeitos visuais e comportamento dos nexos sombrios
    for (FShadowNexusData& NexusData : ShadowNexusData)
    {
        if (NexusData.ParticleEffect && IsValid(NexusData.ParticleEffect))
        {
            // Calcular pulsação de energia baseada no tempo
            float PulseValue = 0.8f + 0.2f * FMath::Sin((Time + NexusData.TimeOffset) * NexusData.PulseRate);
            
            // Atualizar parâmetros do sistema de partículas
            NexusData.ParticleEffect->SetVariableFloat(TEXT("Energy"), NexusData.Energy * PulseValue);
            
            // Rotação lenta
            FRotator NewRotation = NexusData.Rotation;
            NewRotation.Yaw += 5.0f * NexusData.PulseRate * GetWorld()->GetDeltaSeconds();
            NexusData.Rotation = NewRotation;
            
            // Aplicar rotação ao sistema de partículas
            NexusData.ParticleEffect->SetRelativeRotation(NewRotation);
        }
    }
}

float AAURACRONPCGEnvironment::GetEnvironmentHeightAt(const FVector& Location)
{
    // Função auxiliar para obter a altura do terreno em uma posição
    FHitResult HitResult;
    FVector Start = FVector(Location.X, Location.Y, 10000.0f);
    FVector End = FVector(Location.X, Location.Y, -10000.0f);
    
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(this);
    
    if (GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, ECC_Visibility, QueryParams))
    {
        return HitResult.ImpactPoint.Z;
    }
    
    return 0.0f;
}