// AURACRONCharacter.h
// Sistema de Sígilos AURACRON - Classe Base do Personagem UE 5.6
// Implementação robusta com GAS, Sígilos e sistemas modernos

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "AbilitySystemInterface.h"
#include "GameplayTagContainer.h"
#include "Data/AURACRONEnums.h"
#include "Data/AURACRONStructs.h"

// Forward Declarations
class UAbilitySystemComponent;
class UGameplayAbility;
class UGameplayEffect;

// Includes para classes específicas
#include "GAS/AURACRONAttributeSet.h"
#include "Components/AURACRONSigilComponent.h"
#include "Components/AURACRONMovementComponent.h"
#include "AURACRONCharacter.generated.h"

/**
 * Classe base para todos os personagens do AURACRON
 * Implementa sistema completo de Sígilos, GAS e mecânicas específicas
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API AAURACRONCharacter : public ACharacter, public IAbilitySystemInterface
{
    GENERATED_BODY()

public:
    AAURACRONCharacter(const FObjectInitializer& ObjectInitializer);

    // IAbilitySystemInterface
    virtual UAbilitySystemComponent* GetAbilitySystemComponent() const override;

    // APawn interface
    virtual void PossessedBy(AController* NewController) override;
    virtual void OnRep_PlayerState() override;

    // ACharacter interface
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;
    virtual void SetupPlayerInputComponent(UInputComponent* PlayerInputComponent) override;

    // Network Replication
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    /** Inicializa o sistema de habilidades */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Abilities")
    virtual void InitializeAbilitySystem();

    /** Concede habilidades padrão ao personagem */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Abilities")
    virtual void GiveDefaultAbilities();

    /** Aplica efeitos de gameplay padrão */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Abilities")
    virtual void ApplyDefaultEffects();

    // Sistema de Sígilos
    /** Equipa um Sígilo específico */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Sigilos", Server, Reliable)
    void EquipSigil(EAURACRONSigilType SigilType);

    /** Remove um Sígilo equipado */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Sigilos", Server, Reliable)
    void UnequipSigil(EAURACRONSigilType SigilType);

    /** Verifica se um Sígilo está equipado */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sigilos")
    bool IsSigilEquipped(EAURACRONSigilType SigilType) const;

    /** Obtém todos os Sígilos equipados */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sigilos")
    TArray<EAURACRONSigilType> GetEquippedSigils() const;

    // Sistema de Atributos
    /** Obtém vida atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetHealth() const;

    /** Obtém vida máxima */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetMaxHealth() const;

    /** Obtém mana atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetMana() const;

    /** Obtém mana máxima */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetMaxMana() const;

    /** Obtém dano de ataque */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetAttackDamage() const;

    /** Obtém poder de habilidade */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetAbilityPower() const;

    // Sistema de Equipe
    /** Define a equipe do personagem */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Team", Server, Reliable)
    void SetTeamID(int32 NewTeamID);

    /** Obtém a equipe do personagem */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Team")
    int32 GetTeamID() const;

    /** Verifica se é aliado de outro personagem */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Team")
    bool IsAlly(const AAURACRONCharacter* OtherCharacter) const;

    // Eventos Blueprint
    /** Evento chamado quando um Sígilo é equipado */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnSigilEquipped(EAURACRONSigilType SigilType);

    /** Evento chamado quando um Sígilo é removido */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnSigilUnequipped(EAURACRONSigilType SigilType);

    /** Evento chamado quando a vida muda */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnHealthChanged(float NewHealth, float MaxHealth);

    /** Evento chamado quando a mana muda */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnManaChanged(float NewMana, float MaxMana);

    // Efeitos Temporais
    /** Aplica um efeito temporal ao personagem */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Temporal")
    void ApplyTemporalEffect(EAURACRONTemporalEffectType EffectType, float Duration);

protected:
    // Componentes do Sistema de Habilidades
    /** Componente do sistema de habilidades */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Abilities")
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

    /** Set de atributos do personagem */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Abilities")
    TObjectPtr<UAURACRONAttributeSet> AttributeSet;

    /** Componente de Sígilos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Sigilos")
    TObjectPtr<UAURACRONSigilComponent> SigilComponent;

    /** Componente de movimento customizado */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Movement")
    TObjectPtr<UAURACRONMovementComponent> AURACRONMovementComponent;

    // Configurações de Habilidades
    /** Habilidades padrão concedidas ao personagem */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Abilities")
    TArray<TSubclassOf<UGameplayAbility>> DefaultAbilities;

    /** Efeitos padrão aplicados ao personagem */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Abilities")
    TArray<TSubclassOf<UGameplayEffect>> DefaultEffects;

    /** Tags de gameplay iniciais */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Abilities")
    FGameplayTagContainer InitialGameplayTags;

    // Propriedades de Equipe
    /** ID da equipe (0 = Equipe A, 1 = Equipe B) */
    UPROPERTY(ReplicatedUsing = OnRep_TeamID, BlueprintReadOnly, Category = "AURACRON|Team")
    int32 TeamID;

    /** Sígilos atualmente equipados */
    UPROPERTY(ReplicatedUsing = OnRep_EquippedSigils, BlueprintReadOnly, Category = "AURACRON|Sigilos")
    TArray<EAURACRONSigilType> EquippedSigils;

    // Funções de Replicação
    UFUNCTION()
    void OnRep_TeamID();

    UFUNCTION()
    void OnRep_EquippedSigils();

    // Callbacks de Atributos
    virtual void OnHealthAttributeChanged(const struct FOnAttributeChangeData& Data);
    virtual void OnManaAttributeChanged(const struct FOnAttributeChangeData& Data);

private:
    /** Se o sistema de habilidades foi inicializado */
    bool bAbilitySystemInitialized;

    /** Handles para callbacks de atributos */
    FDelegateHandle HealthChangedDelegateHandle;
    FDelegateHandle ManaChangedDelegateHandle;
};
