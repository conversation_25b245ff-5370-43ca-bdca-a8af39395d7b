// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "Components/NiagaraComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGPoint.h"
#include "PCGVolume.h"

void AAURACRONPCGEnvironment::GenerateTowersOfLamentation()
{
    if (!HasAuthority())
    {
        return;
    }

    // Limpar dados existentes
    TowerOfLamentationData.Empty();
    
    // Verificar se o componente PCG é válido
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironment::GenerateTowersOfLamentation - PCGComponent is invalid"));
        return;
    }

    // Obter dimensões do mapa para posicionamento
    float MapRadius = FAURACRONMapDimensions::MAP_RADIUS;
    float InnerRadius = MapRadius * 0.4f;
    float OuterRadius = MapRadius * 0.9f;

    // Determinar número de torres com base na escala de atividade
    int32 TowerCount = FMath::RandRange(4, 7) + FMath::FloorToInt(ActivityScale * 3.0f);
    
    // Usar distribuição em círculo para posicionar as torres em pontos estratégicos
    for (int32 i = 0; i < TowerCount; ++i)
    {
        FTowerOfLamentationData NewTower;
        
        // Calcular posição em círculo com variação aleatória
        float Angle = (float)i / TowerCount * 2.0f * PI + FMath::RandRange(-0.2f, 0.2f);
        float Distance = FMath::RandRange(InnerRadius, OuterRadius);
        
        FVector Position;
        Position.X = FMath::Cos(Angle) * Distance;
        Position.Y = FMath::Sin(Angle) * Distance;
        
        // Ajustar altura com base na topografia
        Position.Z = GetEnvironmentHeightAt(Position) + FMath::RandRange(100.0f, 300.0f);
        
        // Configurar dados da torre
        NewTower.Position = Position;
        NewTower.Rotation = FRotator(0, FMath::RandRange(0.0f, 360.0f), 0);
        NewTower.Scale = FVector(1.0f) * FMath::RandRange(0.9f, 1.3f);
        NewTower.Energy = FMath::RandRange(0.6f, 1.0f);
        NewTower.PulseRate = FMath::RandRange(0.3f, 1.5f);
        NewTower.TimeOffset = FMath::RandRange(0.0f, 10.0f);
        
        // Determinar tipo de torre (variações visuais)
        NewTower.TowerType = FMath::RandRange(0, 3);
        
        // Adicionar à lista
        TowerOfLamentationData.Add(NewTower);
        
        // Configurar parâmetros PCG para esta torre
        FString ParameterName = FString::Printf(TEXT("TowerOfLamentation_%d_Position"), i);
        SetPCGParameterModern(ParameterName, FVector(Position.X, Position.Y, Position.Z), TEXT("PurgatoryRealm"));
        
        ParameterName = FString::Printf(TEXT("TowerOfLamentation_%d_Scale"), i);
        SetPCGParameterModern(ParameterName, NewTower.Scale, TEXT("PurgatoryRealm"));
        
        ParameterName = FString::Printf(TEXT("TowerOfLamentation_%d_Energy"), i);
        SetPCGParameterModern(ParameterName, FVector(NewTower.Energy, 0, 0), TEXT("PurgatoryRealm"));
        
        ParameterName = FString::Printf(TEXT("TowerOfLamentation_%d_Type"), i);
        SetPCGParameterModern(ParameterName, FVector(NewTower.TowerType, 0, 0), TEXT("PurgatoryRealm"));
    }
    
    // Configurar número total de torres para o PCG
    SetPCGParameterModern(TEXT("TowerOfLamentationCount"), FVector(TowerOfLamentationData.Num(), 0, 0), TEXT("PurgatoryRealm"));
    
    // Inicializar efeitos de partículas para cada torre
    if (TowerOfLamentationParticleSystem)
    {
        for (int32 i = 0; i < TowerOfLamentationData.Num(); ++i)
        {
            UNiagaraComponent* TowerEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                TowerOfLamentationParticleSystem,
                TowerOfLamentationData[i].Position,
                TowerOfLamentationData[i].Rotation
            );
            
            if (TowerEffect)
            {
                // Configurar parâmetros do sistema de partículas
                TowerEffect->SetVariableFloat(TEXT("Energy"), TowerOfLamentationData[i].Energy);
                TowerEffect->SetVariableFloat(TEXT("PulseRate"), TowerOfLamentationData[i].PulseRate);
                TowerEffect->SetVariableInt(TEXT("TowerType"), TowerOfLamentationData[i].TowerType);
                
                // Armazenar referência ao componente
                TowerOfLamentationData[i].ParticleEffect = TowerEffect;
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateTowersOfLamentation - Generated %d Towers of Lamentation"), TowerOfLamentationData.Num());
}

void AAURACRONPCGEnvironment::UpdateTowersOfLamentation(float Time)
{
    // Atualizar efeitos visuais e comportamento das torres
    for (FTowerOfLamentationData& TowerData : TowerOfLamentationData)
    {
        if (TowerData.ParticleEffect && IsValid(TowerData.ParticleEffect))
        {
            // Calcular pulsação de energia baseada no tempo
            float PulseValue = 0.7f + 0.3f * FMath::Sin((Time + TowerData.TimeOffset) * TowerData.PulseRate);
            
            // Atualizar parâmetros do sistema de partículas
            TowerData.ParticleEffect->SetVariableFloat(TEXT("Energy"), TowerData.Energy * PulseValue);
            
            // Efeito de oscilação vertical lenta
            float HeightOffset = 20.0f * FMath::Sin((Time + TowerData.TimeOffset) * 0.2f);
            FVector NewPosition = TowerData.Position + FVector(0, 0, HeightOffset);
            TowerData.ParticleEffect->SetWorldLocation(NewPosition);
            
            // Rotação lenta
            FRotator NewRotation = TowerData.Rotation;
            NewRotation.Yaw += 2.0f * TowerData.PulseRate * GetWorld()->GetDeltaSeconds();
            TowerData.Rotation = NewRotation;
            
            // Aplicar rotação ao sistema de partículas
            TowerData.ParticleEffect->SetRelativeRotation(NewRotation);
        }
    }
}