// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGEnergyPulse.h"

#ifdef AURACRON_AURACRONPCGEnergyPulse_generated_h
#error "AURACRONPCGEnergyPulse.generated.h already included, missing '#pragma once' in AURACRONPCGEnergyPulse.h"
#endif
#define AURACRON_AURACRONPCGEnergyPulse_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UPrimitiveComponent;
enum class EAURACRONMapPhase : uint8;
enum class EAURACRONPortalType : uint8;
struct FHitResult;

// ********** Begin Class AAURACRONPCGEnergyPulse **************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h_34_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnPlayerEnterPulseRadius); \
	DECLARE_FUNCTION(execSetPulseDuration); \
	DECLARE_FUNCTION(execSetPulseIntensity); \
	DECLARE_FUNCTION(execSetPulseRadius); \
	DECLARE_FUNCTION(execApplyPulseEffects); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execSetQualityScale); \
	DECLARE_FUNCTION(execCreateEnergyPulseForPortalType); \
	DECLARE_FUNCTION(execCreateVioletEnergyPulse); \
	DECLARE_FUNCTION(execCreateSilverEnergyPulse); \
	DECLARE_FUNCTION(execCreateGoldenEnergyPulse); \
	DECLARE_FUNCTION(execTriggerPulse);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnergyPulse_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h_34_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGEnergyPulse(); \
	friend struct Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnergyPulse_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGEnergyPulse, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGEnergyPulse_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGEnergyPulse)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h_34_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGEnergyPulse(AAURACRONPCGEnergyPulse&&) = delete; \
	AAURACRONPCGEnergyPulse(const AAURACRONPCGEnergyPulse&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGEnergyPulse); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGEnergyPulse); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGEnergyPulse) \
	NO_API virtual ~AAURACRONPCGEnergyPulse();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h_31_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h_34_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h_34_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h_34_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h_34_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGEnergyPulse;

// ********** End Class AAURACRONPCGEnergyPulse ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h

// ********** Begin Enum EAURACRONEnergyType *******************************************************
#define FOREACH_ENUM_EAURACRONENERGYTYPE(op) \
	op(EAURACRONEnergyType::Golden) \
	op(EAURACRONEnergyType::Silver) \
	op(EAURACRONEnergyType::Violet) 

enum class EAURACRONEnergyType : uint8;
template<> struct TIsUEnumClass<EAURACRONEnergyType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnergyType>();
// ********** End Enum EAURACRONEnergyType *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
