// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGObjectiveSystem.h"

#ifdef AURACRON_AURACRONPCGObjectiveSystem_generated_h
#error "AURACRONPCGObjectiveSystem.generated.h already included, missing '#pragma once' in AURACRONPCGObjectiveSystem.h"
#endif
#define AURACRON_AURACRONPCGObjectiveSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONMapPhase : uint8;
enum class EAURACRONObjectiveState : uint8;
enum class EAURACRONObjectiveType : uint8;
struct FAURACRONObjectiveInfo;
struct FAURACRONProceduralObjective;

// ********** Begin ScriptStruct FAURACRONObjectiveInfo ********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_40_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONObjectiveInfo;
// ********** End ScriptStruct FAURACRONObjectiveInfo **********************************************

// ********** Begin Delegate FOnObjectiveCreated ***************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_133_DELEGATE \
AURACRON_API void FOnObjectiveCreated_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveCreated, FAURACRONProceduralObjective const& Objective);


// ********** End Delegate FOnObjectiveCreated *****************************************************

// ********** Begin Delegate FOnObjectiveDestroyed *************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_134_DELEGATE \
AURACRON_API void FOnObjectiveDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveDestroyed, FAURACRONProceduralObjective const& Objective);


// ********** End Delegate FOnObjectiveDestroyed ***************************************************

// ********** Begin Delegate FOnObjectiveStateChanged **********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_135_DELEGATE \
AURACRON_API void FOnObjectiveStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveStateChanged, FAURACRONProceduralObjective const& Objective, EAURACRONObjectiveState OldState);


// ********** End Delegate FOnObjectiveStateChanged ************************************************

// ********** Begin Delegate FOnObjectiveCaptured **************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_136_DELEGATE \
AURACRON_API void FOnObjectiveCaptured_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveCaptured, int32 ObjectiveIndex, int32 CapturingTeam);


// ********** End Delegate FOnObjectiveCaptured ****************************************************

// ********** Begin Delegate FOnChaosIslandEvent ***************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_137_DELEGATE \
AURACRON_API void FOnChaosIslandEvent_DelegateWrapper(const FMulticastScriptDelegate& OnChaosIslandEvent, int32 IslandIndex);


// ********** End Delegate FOnChaosIslandEvent *****************************************************

// ********** Begin Class AAURACRONPCGObjectiveSystem **********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_147_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnProceduralGenerationTimerExpired); \
	DECLARE_FUNCTION(execTriggerChaosIslandEvent); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execUpdateForEnvironment); \
	DECLARE_FUNCTION(execGetObjectiveBuffs); \
	DECLARE_FUNCTION(execIsObjectiveAvailable); \
	DECLARE_FUNCTION(execCaptureObjective); \
	DECLARE_FUNCTION(execAttackObjective); \
	DECLARE_FUNCTION(execGetObjectivesByState); \
	DECLARE_FUNCTION(execGetObjectivesByType); \
	DECLARE_FUNCTION(execGetAllProceduralObjectives); \
	DECLARE_FUNCTION(execGetAllObjectives); \
	DECLARE_FUNCTION(execForceGenerateObjective); \
	DECLARE_FUNCTION(execStopObjectiveSystem); \
	DECLARE_FUNCTION(execStartObjectiveSystem); \
	DECLARE_FUNCTION(execGenerateObjectivesForEnvironment); \
	DECLARE_FUNCTION(execGenerateObjectives);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_147_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGObjectiveSystem(); \
	friend struct Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGObjectiveSystem, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGObjectiveSystem) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		ActiveProceduralObjectives=NETFIELD_REP_START, \
		ProceduralObjectives, \
		bProceduralSystemActive, \
		NETFIELD_REP_END=bProceduralSystemActive	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_147_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGObjectiveSystem(AAURACRONPCGObjectiveSystem&&) = delete; \
	AAURACRONPCGObjectiveSystem(const AAURACRONPCGObjectiveSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGObjectiveSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGObjectiveSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGObjectiveSystem) \
	NO_API virtual ~AAURACRONPCGObjectiveSystem();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_144_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_147_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_147_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_147_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h_147_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGObjectiveSystem;

// ********** End Class AAURACRONPCGObjectiveSystem ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
