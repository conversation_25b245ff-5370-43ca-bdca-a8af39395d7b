{"RemapUnityFiles": {"Module.AURACRON.1.cpp.obj": ["AURACRON.init.gen.cpp.obj", "AURACRONMapMeasurements.gen.cpp.obj", "AURACRONPCGEnvironment.gen.cpp.obj", "AURACRONPCGIsland.gen.cpp.obj", "AURACRONPCGMathLibrary.gen.cpp.obj", "AURACRONPCGSubsystem.gen.cpp.obj"], "Module.AURACRON.2.cpp.obj": ["AURACRONPCGTrail.gen.cpp.obj", "SigilAbilities.gen.cpp.obj", "SigilAbilityEffects.gen.cpp.obj"], "Module.AURACRON.3.cpp.obj": ["SigilAttributeSet.gen.cpp.obj", "SigilDebugCommands.gen.cpp.obj", "SigilFusionSystem.gen.cpp.obj", "SigilGameplayEffects.gen.cpp.obj"], "Module.AURACRON.4.cpp.obj": ["SigilItem.gen.cpp.obj", "SigilManagerComponent.gen.cpp.obj"], "Module.AURACRON.5.cpp.obj": ["SigilNetworkConfig.gen.cpp.obj", "SigilReplicationManager.gen.cpp.obj", "SigilVFXManager.gen.cpp.obj"], "Module.AURACRON.6.cpp.obj": ["SigilWidgets.gen.cpp.obj", "PerModuleInline.gen.cpp.obj", "AURACRON.cpp.obj", "SigilDebugCommands.cpp.obj", "SigilFusionSystem.cpp.obj"]}}