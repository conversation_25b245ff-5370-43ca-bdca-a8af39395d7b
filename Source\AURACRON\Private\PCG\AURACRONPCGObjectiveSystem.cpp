// AURACRONPCGObjectiveSystem.cpp
// Implementação do sistema de objetivos estratégicos baseado em LoL Baron/Dragon

#include "PCG/AURACRONPCGObjectiveSystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"

AAURACRONPCGObjectiveSystem::AAURACRONPCGObjectiveSystem()
    : bAutoGenerate(true)
    , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , NextChaosIslandIndex(0)
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Configurar replicação para multiplayer
    bReplicates = true;
    SetReplicateMovement(false);
    
    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
}

void AAURACRONPCGObjectiveSystem::BeginPlay()
{
    Super::BeginPlay();
    
    // Gerar sistema apenas no servidor
    if (HasAuthority() && bAutoGenerate)
    {
        // Delay pequeno para garantir que outros sistemas estejam prontos
        FTimerHandle GenerationTimer;
        GetWorld()->GetTimerManager().SetTimer(GenerationTimer, this, 
            &AAURACRONPCGObjectiveSystem::GenerateObjectives, 2.0f, false);
    }
}

void AAURACRONPCGObjectiveSystem::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Atualizar timers de respawn
    if (HasAuthority())
    {
        for (int32 i = 0; i < Objectives.Num(); ++i)
        {
            if (Objectives[i].CurrentState == EAURACRONObjectiveState::Respawning && 
                Objectives[i].TimeUntilRespawn > 0.0f)
            {
                Objectives[i].TimeUntilRespawn -= DeltaTime;
                
                if (Objectives[i].TimeUntilRespawn <= 0.0f)
                {
                    OnObjectiveRespawn(i);
                }
            }
        }
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGObjectiveSystem::GenerateObjectives()
{
    if (!HasAuthority())
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Gerando objetivos estratégicos baseados em LoL Baron/Dragon"));
    
    // Limpar objetivos anteriores
    for (const auto& EnvPair : ObjectiveMeshesByEnvironment)
    {
        ClearObjectivesForEnvironment(EnvPair.Key);
    }
    
    // Inicializar informações dos objetivos
    InitializeObjectiveInfos();
    
    // Gerar objetivos para todos os 3 ambientes
    for (int32 EnvIndex = 0; EnvIndex < 3; ++EnvIndex)
    {
        EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvIndex);
        GenerateObjectivesForEnvironment(Environment);
    }
    
    // Iniciar com Radiant Plains ativo
    UpdateForEnvironment(EAURACRONEnvironmentType::RadiantPlains);
    
    // Iniciar rotação de Chaos Islands
    StartChaosIslandRotation();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Gerados %d objetivos estratégicos para 3 ambientes"), Objectives.Num());
}

void AAURACRONPCGObjectiveSystem::GenerateObjectivesForEnvironment(EAURACRONEnvironmentType Environment)
{
    if (!HasAuthority())
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Gerando objetivos para ambiente %d"), static_cast<int32>(Environment));
    
    for (int32 i = 0; i < Objectives.Num(); ++i)
    {
        CreateObjective(i, Environment);
        ApplyEnvironmentCharacteristics(i, Environment);
    }
}

TArray<FAURACRONObjectiveInfo> AAURACRONPCGObjectiveSystem::GetObjectivesByType(EAURACRONObjectiveType ObjectiveType) const
{
    TArray<FAURACRONObjectiveInfo> FilteredObjectives;
    
    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        if (Objective.ObjectiveType == ObjectiveType)
        {
            FilteredObjectives.Add(Objective);
        }
    }
    
    return FilteredObjectives;
}

TArray<FAURACRONObjectiveInfo> AAURACRONPCGObjectiveSystem::GetObjectivesByState(EAURACRONObjectiveState State) const
{
    TArray<FAURACRONObjectiveInfo> FilteredObjectives;
    
    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        if (Objective.CurrentState == State)
        {
            FilteredObjectives.Add(Objective);
        }
    }
    
    return FilteredObjectives;
}

bool AAURACRONPCGObjectiveSystem::AttackObjective(int32 ObjectiveIndex, float Damage, int32 AttackingTeam)
{
    if (!HasAuthority() || ObjectiveIndex < 0 || ObjectiveIndex >= Objectives.Num())
    {
        return false;
    }
    
    FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
    
    if (Objective.CurrentState != EAURACRONObjectiveState::Available && 
        Objective.CurrentState != EAURACRONObjectiveState::InCombat)
    {
        return false;
    }
    
    // Aplicar dano
    Objective.CurrentHealth = FMath::Max(0.0f, Objective.CurrentHealth - Damage);
    Objective.CurrentState = EAURACRONObjectiveState::InCombat;
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Objetivo %d atacado por Team %d, HP: %.1f/%.1f"), 
        ObjectiveIndex, AttackingTeam, Objective.CurrentHealth, Objective.MaxHealth);
    
    // Verificar se foi morto
    if (Objective.CurrentHealth <= 0.0f)
    {
        return CaptureObjective(ObjectiveIndex, AttackingTeam);
    }
    
    return true;
}

bool AAURACRONPCGObjectiveSystem::CaptureObjective(int32 ObjectiveIndex, int32 CapturingTeam)
{
    if (!HasAuthority() || ObjectiveIndex < 0 || ObjectiveIndex >= Objectives.Num())
    {
        return false;
    }
    
    FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
    
    if (Objective.CurrentState == EAURACRONObjectiveState::Captured)
    {
        return false;
    }
    
    // Capturar objetivo
    Objective.CurrentState = EAURACRONObjectiveState::Captured;
    Objective.ControllingTeam = CapturingTeam;
    Objective.TimeUntilRespawn = Objective.RespawnTime;
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Objetivo %d capturado por Team %d, respawn em %.1fs"), 
        ObjectiveIndex, CapturingTeam, Objective.RespawnTime);
    
    // Aplicar buffs ao time
    ApplyObjectiveBuffsToTeam(ObjectiveIndex, CapturingTeam);
    
    // Iniciar timer de respawn
    StartRespawnTimer(ObjectiveIndex);
    
    // Atualizar visibilidade
    UpdateObjectiveVisibility();
    
    return true;
}

bool AAURACRONPCGObjectiveSystem::IsObjectiveAvailable(int32 ObjectiveIndex) const
{
    if (ObjectiveIndex < 0 || ObjectiveIndex >= Objectives.Num())
    {
        return false;
    }
    
    const FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
    return Objective.CurrentState == EAURACRONObjectiveState::Available && 
           Objective.bIsActiveInCurrentPhase;
}

TMap<FString, float> AAURACRONPCGObjectiveSystem::GetObjectiveBuffs(int32 ObjectiveIndex) const
{
    if (ObjectiveIndex < 0 || ObjectiveIndex >= Objectives.Num())
    {
        return TMap<FString, float>();
    }
    
    return Objectives[ObjectiveIndex].ObjectiveBuffs;
}

void AAURACRONPCGObjectiveSystem::UpdateForEnvironment(EAURACRONEnvironmentType NewEnvironment)
{
    if (CurrentEnvironment == NewEnvironment)
    {
        return;
    }
    
    CurrentEnvironment = NewEnvironment;
    UpdateObjectiveVisibility();
    
    // Atualizar objetivos específicos do ambiente
    UpdateEnvironmentSpecificObjectives(NewEnvironment);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Atualizado para ambiente %d"), static_cast<int32>(NewEnvironment));
}

void AAURACRONPCGObjectiveSystem::UpdateEnvironmentSpecificObjectives(EAURACRONEnvironmentType EnvironmentType)
{
    // Limpar objetivos procedurais específicos do ambiente anterior
    TArray<FAURACRONProceduralObjective> RemainingObjectives;
    for (const FAURACRONProceduralObjective& Objective : ActiveProceduralObjectives)
    {
        // Manter apenas objetivos que não são específicos de ambiente
        if (Objective.EnvironmentType != EnvironmentType && 
            Objective.ObjectiveCategory != EAURACRONObjectiveCategory::Environment)
        {
            RemainingObjectives.Add(Objective);
        }
    }
    
    // Substituir a lista ativa com os objetivos restantes
    ActiveProceduralObjectives = RemainingObjectives;
    
    // Gerar novos objetivos específicos para o ambiente atual
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        // Gerar objetivos específicos para Radiant Plains
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::RadiantShrine);
            NewObjective.EnvironmentType = EnvironmentType;
            NewObjective.ObjectiveCategory = EAURACRONObjectiveCategory::Environment;
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Gerar objetivos específicos para Firmamento Zephyr
        
        // Gerar Núcleo de Tempestade (objetivo principal)
        {
            FAURACRONProceduralObjective StormCore = GenerateNewProceduralObjective(EAURACRONObjectiveType::StormCore);
            StormCore.EnvironmentType = EnvironmentType;
            StormCore.ObjectiveCategory = EAURACRONObjectiveCategory::Environment;
            StormCore.StrategicValue = 0.9f; // Valor estratégico alto
            StormCore.RespawnTime = 300.0f; // 5 minutos para respawn
            
            // Posicionar no centro do mapa em uma posição elevada
            FVector CentralPosition;
            EAURACRONEnvironmentType TempEnv;
            CentralPosition = FindCentralPosition(TempEnv);
            CentralPosition.Z += 200.0f; // Posição elevada
            StormCore.WorldPosition = CentralPosition;
            
            ActiveProceduralObjectives.Add(StormCore);
            
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Núcleo de Tempestade gerado em %s"), 
                   *StormCore.WorldPosition.ToString());
        }
        
        // Gerar Santuários dos Ventos (objetivos secundários)
        for (int32 i = 0; i < 3; ++i)
        {
            FAURACRONProceduralObjective WindSanctuary = GenerateNewProceduralObjective(EAURACRONObjectiveType::WindSanctuary);
            WindSanctuary.EnvironmentType = EnvironmentType;
            WindSanctuary.ObjectiveCategory = EAURACRONObjectiveCategory::Environment;
            WindSanctuary.StrategicValue = 0.6f; // Valor estratégico médio
            WindSanctuary.RespawnTime = 180.0f; // 3 minutos para respawn
            
            // Posicionar em pontos estratégicos do mapa
            FVector SanctuaryPosition;
            EAURACRONEnvironmentType TempEnv;
            
            // Distribuir os santuários em diferentes áreas
            if (i == 0) {
                // Próximo à base da equipe 1
                SanctuaryPosition = FindPositionNearTeamBase(0, TempEnv);
                SanctuaryPosition.Z += 50.0f;
            } else if (i == 1) {
                // Próximo à base da equipe 2
                SanctuaryPosition = FindPositionNearTeamBase(1, TempEnv);
                SanctuaryPosition.Z += 50.0f;
            } else {
                // Em uma posição neutra
                SanctuaryPosition = FindCentralPosition(TempEnv);
                SanctuaryPosition += FVector(FMath::RandRange(-500.0f, 500.0f), 
                                           FMath::RandRange(-500.0f, 500.0f), 
                                           100.0f);
            }
            
            WindSanctuary.WorldPosition = SanctuaryPosition;
            ActiveProceduralObjectives.Add(WindSanctuary);
            
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Santuário dos Ventos %d gerado em %s"), 
                   i, *WindSanctuary.WorldPosition.ToString());
        }
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Gerar objetivos específicos para Purgatory Realm
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::PurgatoryShrine);
            NewObjective.EnvironmentType = EnvironmentType;
            NewObjective.ObjectiveCategory = EAURACRONObjectiveCategory::Environment;
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    default:
        break;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Objetivos específicos do ambiente %d atualizados"), 
           static_cast<int32>(EnvironmentType));
}

void AAURACRONPCGObjectiveSystem::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    if (CurrentMapPhase != MapPhase)
    {
        EAURACRONMapPhase PreviousPhase = CurrentMapPhase;
        CurrentMapPhase = MapPhase;
        
        // Ativar objetivos baseado na fase
        for (FAURACRONObjectiveInfo& Objective : Objectives)
        {
            bool bShouldBeActive = (MapPhase >= Objective.MinimumPhaseForActivation);
            
            if (bShouldBeActive && !Objective.bIsActiveInCurrentPhase)
            {
                Objective.bIsActiveInCurrentPhase = true;
                if (Objective.CurrentState == EAURACRONObjectiveState::Inactive)
                {
                    Objective.CurrentState = EAURACRONObjectiveState::Available;
                    Objective.CurrentHealth = Objective.MaxHealth;
                }
            }
        }
        
        // Gerar novos objetivos procedurais específicos da fase
        GeneratePhaseSpecificObjectives(MapPhase);
        
        ApplyMapPhaseEffects();
        UpdateObjectiveVisibility();
        
        // Notificar sobre a mudança de fase
        OnMapPhaseChanged(PreviousPhase, MapPhase);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Atualizado para fase %d"), static_cast<int32>(MapPhase));
    }
}

void AAURACRONPCGObjectiveSystem::TriggerChaosIslandEvent(int32 ChaosIslandIndex)
{
    // Implementar eventos específicos das Chaos Islands
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem::TriggerChaosIslandEvent - Ativando evento para ilha %d"), ChaosIslandIndex);
    
    // Buscar a ilha de caos correspondente
    if (PCGActorReferences.IslandActors.IsValidIndex(ChaosIslandIndex))
    {
        AAURACRONPCGIsland* ChaosIsland = PCGActorReferences.IslandActors[ChaosIslandIndex];
        if (IsValid(ChaosIsland) && ChaosIsland->GetIslandType() == EAURACRONIslandType::Chaos)
        {
            // Ativar a ilha
            ChaosIsland->OnIslandActivated();
            
            // Gerar objetivos procedurais na ilha
            for (int32 i = 0; i < 3; ++i) // Gerar 3 objetivos
            {
                FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::ChaosRift);
                
                // Posicionar o objetivo na ilha
                FVector IslandLocation = ChaosIsland->GetActorLocation();
                float IslandRadius = ChaosIsland->GetIslandSize() / 2.0f;
                
                // Distribuir objetivos em diferentes pontos da ilha
                float Angle = (float)i / 3.0f * 2.0f * PI;
                float Distance = IslandRadius * 0.7f; // 70% do raio da ilha
                
                FVector ObjectiveOffset;
                ObjectiveOffset.X = Distance * FMath::Cos(Angle);
                ObjectiveOffset.Y = Distance * FMath::Sin(Angle);
                ObjectiveOffset.Z = 100.0f; // Ligeiramente acima da superfície da ilha
                
                NewObjective.WorldPosition = IslandLocation + ObjectiveOffset;
                
                // Adicionar o objetivo à lista de objetivos ativos
                ActiveProceduralObjectives.Add(NewObjective);
            }
            
            // Disparar evento para notificar outros sistemas
            OnChaosIslandEvent.Broadcast(ChaosIslandIndex);
        }
    }
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Evento de Chaos Island %d ativado"), ChaosIslandIndex);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGObjectiveSystem::InitializeObjectiveInfos()
{
    Objectives.Empty();

    // OBJETIVO PRINCIPAL - Prismal Nexus (Baron equivalent)
    FAURACRONObjectiveInfo PrismalNexus = GetDefaultObjectiveConfig(EAURACRONObjectiveType::PrismalNexus);
    Objectives.Add(PrismalNexus);

    // OBJETIVOS SECUNDÁRIOS - Elemental Anchors (Dragon equivalents)
    FAURACRONObjectiveInfo RadiantAnchor = GetDefaultObjectiveConfig(EAURACRONObjectiveType::RadiantAnchor);
    Objectives.Add(RadiantAnchor);

    FAURACRONObjectiveInfo ZephyrAnchor = GetDefaultObjectiveConfig(EAURACRONObjectiveType::ZephyrAnchor);
    Objectives.Add(ZephyrAnchor);

    FAURACRONObjectiveInfo PurgatoryAnchor = GetDefaultObjectiveConfig(EAURACRONObjectiveType::PurgatoryAnchor);
    Objectives.Add(PurgatoryAnchor);

    // OBJETIVOS ÚNICOS DO AURACRON

    // Storm Core (novo objetivo)
    FAURACRONObjectiveInfo StormCore = GetDefaultObjectiveConfig(EAURACRONObjectiveType::StormCore);
    Objectives.Add(StormCore);

    // Nexus Island (centro do mapa)
    FAURACRONObjectiveInfo NexusIsland = GetDefaultObjectiveConfig(EAURACRONObjectiveType::NexusIsland);
    Objectives.Add(NexusIsland);

    // Sanctuary Islands (4 posições simétricas)
    for (int32 i = 0; i < 4; ++i)
    {
        FAURACRONObjectiveInfo SanctuaryIsland = GetDefaultObjectiveConfig(EAURACRONObjectiveType::SanctuaryIsland);
        Objectives.Add(SanctuaryIsland);
    }

    // Arsenal Islands (2 posições)
    for (int32 i = 0; i < 2; ++i)
    {
        FAURACRONObjectiveInfo ArsenalIsland = GetDefaultObjectiveConfig(EAURACRONObjectiveType::ArsenalIsland);
        Objectives.Add(ArsenalIsland);
    }

    // Chaos Islands (3 posições rotativas)
    for (int32 i = 0; i < 3; ++i)
    {
        FAURACRONObjectiveInfo ChaosIsland = GetDefaultObjectiveConfig(EAURACRONObjectiveType::ChaosIsland);
        Objectives.Add(ChaosIsland);
    }

    // Calcular posições para todos os ambientes
    for (int32 i = 0; i < Objectives.Num(); ++i)
    {
        for (int32 EnvIndex = 0; EnvIndex < 3; ++EnvIndex)
        {
            EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvIndex);
            FVector Position = CalculateObjectivePosition(Objectives[i].ObjectiveType, Environment);
            Objectives[i].PositionsByEnvironment.Add(Environment, Position);
        }
    }
}

FAURACRONObjectiveInfo AAURACRONPCGObjectiveSystem::GetDefaultObjectiveConfig(EAURACRONObjectiveType ObjectiveType)
{
    FAURACRONObjectiveInfo Config;
    Config.ObjectiveType = ObjectiveType;

    switch (ObjectiveType)
    {
    case EAURACRONObjectiveType::PrismalNexus:
        Config.ObjectiveRadius = FAURACRONMapDimensions::PRISMAL_NEXUS_PIT_RADIUS_CM;
        Config.PitDepth = FAURACRONMapDimensions::PRISMAL_NEXUS_PIT_DEPTH_CM;
        Config.RespawnTime = FAURACRONMapDimensions::PRISMAL_NEXUS_RESPAWN_TIME;
        Config.MaxHealth = 15000.0f;
        Config.CurrentHealth = 15000.0f;
        Config.BuffDuration = 180.0f; // 3 minutos (como Baron)
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Convergence;
        Config.ObjectiveBuffs.Add(TEXT("PrismalEmpowerment"), 40.0f);
        Config.ObjectiveBuffs.Add(TEXT("AttackDamage"), 50.0f);
        Config.ObjectiveBuffs.Add(TEXT("AbilityPower"), 50.0f);
        Config.ObjectiveBuffs.Add(TEXT("MinionEmpowerment"), 100.0f);
        break;

    case EAURACRONObjectiveType::RadiantAnchor:
        Config.ObjectiveRadius = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_RADIUS_CM;
        Config.PitDepth = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_DEPTH_CM;
        Config.RespawnTime = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_RESPAWN_TIME;
        Config.MaxHealth = 8000.0f;
        Config.CurrentHealth = 8000.0f;
        Config.BuffDuration = 150.0f; // 2.5 minutos
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Convergence;
        Config.ObjectiveBuffs.Add(TEXT("FireResistance"), 25.0f);
        Config.ObjectiveBuffs.Add(TEXT("AttackDamage"), 20.0f);
        Config.ObjectiveBuffs.Add(TEXT("HealthRegen"), 15.0f);
        break;

    case EAURACRONObjectiveType::ZephyrAnchor:
        Config.ObjectiveRadius = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_RADIUS_CM;
        Config.PitDepth = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_DEPTH_CM;
        Config.RespawnTime = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_RESPAWN_TIME;
        Config.MaxHealth = 8000.0f;
        Config.CurrentHealth = 8000.0f;
        Config.BuffDuration = 150.0f;
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Convergence;
        Config.ObjectiveBuffs.Add(TEXT("MovementSpeed"), 30.0f);
        Config.ObjectiveBuffs.Add(TEXT("AttackSpeed"), 25.0f);
        Config.ObjectiveBuffs.Add(TEXT("CooldownReduction"), 15.0f);
        break;

    case EAURACRONObjectiveType::PurgatoryAnchor:
        Config.ObjectiveRadius = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_RADIUS_CM;
        Config.PitDepth = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_DEPTH_CM;
        Config.RespawnTime = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_RESPAWN_TIME;
        Config.MaxHealth = 8000.0f;
        Config.CurrentHealth = 8000.0f;
        Config.BuffDuration = 150.0f;
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Convergence;
        Config.ObjectiveBuffs.Add(TEXT("AbilityPower"), 30.0f);
        Config.ObjectiveBuffs.Add(TEXT("ManaRegen"), 20.0f);
        Config.ObjectiveBuffs.Add(TEXT("SpellVamp"), 15.0f);
        break;
        
    case EAURACRONObjectiveType::SpectralGuardian:
        Config.ObjectiveRadius = 800.0f; // Maior que os outros objetivos
        Config.PitDepth = 0.0f; // Não fica em um buraco
        Config.RespawnTime = 420.0f; // 7 minutos
        Config.MaxHealth = 12000.0f; // Mais resistente que outros objetivos
        Config.CurrentHealth = 12000.0f;
        Config.BuffDuration = 180.0f; // 3 minutos
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Intensification; // Disponível apenas na fase de intensificação
        Config.ObjectiveBuffs.Add(TEXT("SpectralVision"), 1.0f); // Visão espectral (ver através de paredes)
        Config.ObjectiveBuffs.Add(TEXT("SpectralDamage"), 15.0f); // Dano espectral adicional
        Config.ObjectiveBuffs.Add(TEXT("SpectralResistance"), 20.0f); // Resistência a dano espectral
        Config.ObjectiveBuffs.Add(TEXT("UltimateHaste"), 10.0f); // Redução de cooldown da ultimate
        break;

    case EAURACRONObjectiveType::NexusIsland:
        Config.ObjectiveRadius = FAURACRONMapDimensions::NEXUS_ISLAND_RADIUS_CM;
        Config.PitDepth = 0.0f; // Ilha elevada
        Config.RespawnTime = 0.0f; // Permanente
        Config.MaxHealth = 0.0f; // Não atacável
        Config.CurrentHealth = 0.0f;
        Config.BuffDuration = 60.0f; // 1 minuto
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Awakening;
        Config.CurrentState = EAURACRONObjectiveState::Available;
        Config.ObjectiveBuffs.Add(TEXT("VisionRange"), 50.0f);
        Config.ObjectiveBuffs.Add(TEXT("ExperienceBonus"), 25.0f);
        break;

    case EAURACRONObjectiveType::SanctuaryIsland:
        Config.ObjectiveRadius = FAURACRONMapDimensions::SANCTUARY_ISLAND_RADIUS_CM;
        Config.PitDepth = 0.0f;
        Config.RespawnTime = 180.0f; // 3 minutos
        Config.MaxHealth = 0.0f; // Não atacável
        Config.CurrentHealth = 0.0f;
        Config.BuffDuration = 120.0f; // 2 minutos
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Convergence;
        Config.CurrentState = EAURACRONObjectiveState::Available;
        Config.ObjectiveBuffs.Add(TEXT("HealthRegen"), 30.0f);
        Config.ObjectiveBuffs.Add(TEXT("ManaRegen"), 30.0f);
        break;

    case EAURACRONObjectiveType::ArsenalIsland:
        Config.ObjectiveRadius = FAURACRONMapDimensions::ARSENAL_ISLAND_RADIUS_CM;
        Config.PitDepth = 0.0f;
        Config.RespawnTime = 300.0f; // 5 minutos
        Config.MaxHealth = 0.0f; // Não atacável
        Config.CurrentHealth = 0.0f;
        Config.BuffDuration = 0.0f; // Permanente até morte
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Intensification;
        Config.CurrentState = EAURACRONObjectiveState::Available;
        Config.ObjectiveBuffs.Add(TEXT("ItemUpgrade"), 1.0f);
        Config.ObjectiveBuffs.Add(TEXT("GoldBonus"), 500.0f);
        break;

    case EAURACRONObjectiveType::ChaosIsland:
        Config.ObjectiveRadius = FAURACRONMapDimensions::CHAOS_ISLAND_RADIUS_CM;
        Config.PitDepth = 0.0f;
        Config.RespawnTime = FAURACRONMapDimensions::CHAOS_ISLAND_ROTATION_TIME;
        Config.MaxHealth = 0.0f; // Não atacável
        Config.CurrentHealth = 0.0f;
        Config.BuffDuration = 90.0f; // 1.5 minutos
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Intensification;
        Config.CurrentState = EAURACRONObjectiveState::Inactive; // Ativa por rotação
        Config.ObjectiveBuffs.Add(TEXT("RandomEvent"), 1.0f);
        break;
        
    case EAURACRONObjectiveType::StormCore:
        Config.ObjectiveRadius = 750.0f;
        Config.PitDepth = 300.0f;
        Config.RespawnTime = 360.0f; // 6 minutos
        Config.MaxHealth = 10000.0f;
        Config.CurrentHealth = 10000.0f;
        Config.BuffDuration = 150.0f; // 2.5 minutos
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Intensification;
        Config.ObjectiveBuffs.Add(TEXT("StormEmpowerment"), 1.0f);
        Config.ObjectiveBuffs.Add(TEXT("AttackDamage"), 15.0f);
        Config.ObjectiveBuffs.Add(TEXT("MovementSpeed"), 10.0f);
        Config.ObjectiveBuffs.Add(TEXT("CrowdControlResist"), 25.0f);
        break;
    }

    return Config;
}

FVector AAURACRONPCGObjectiveSystem::CalculateObjectivePosition(EAURACRONObjectiveType ObjectiveType, EAURACRONEnvironmentType Environment)
{
    FVector BasePosition = FAURACRONMapDimensions::MAP_CENTER;
    float EnvironmentHeight = 0.0f;

    // Obter altura do ambiente
    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        EnvironmentHeight = FAURACRONMapDimensions::RADIANT_PLAINS_HEIGHT_CM;
        break;
    case EAURACRONEnvironmentType::ZephyrFirmament:
        EnvironmentHeight = FAURACRONMapDimensions::ZEPHYR_FIRMAMENT_HEIGHT_CM;
        break;
    case EAURACRONEnvironmentType::PurgatoryRealm:
        EnvironmentHeight = FAURACRONMapDimensions::PURGATORY_REALM_HEIGHT_CM;
        break;
    }

    FVector Position = BasePosition;
    Position.Z = EnvironmentHeight;

    // Calcular posição baseada no tipo de objetivo
    switch (ObjectiveType)
    {
    case EAURACRONObjectiveType::PrismalNexus:
        // Baron equivalent - lado superior do mapa
        Position += FVector(FAURACRONMapDimensions::PRISMAL_NEXUS_X, FAURACRONMapDimensions::PRISMAL_NEXUS_Y, 0.0f);
        break;

    case EAURACRONObjectiveType::RadiantAnchor:
        // Fire/Earth Dragon - inferior esquerdo
        Position += FVector(FAURACRONMapDimensions::RADIANT_ANCHOR_X, FAURACRONMapDimensions::RADIANT_ANCHOR_Y, 0.0f);
        break;

    case EAURACRONObjectiveType::ZephyrAnchor:
        // Air/Lightning Dragon - direita
        Position += FVector(FAURACRONMapDimensions::ZEPHYR_ANCHOR_X, FAURACRONMapDimensions::ZEPHYR_ANCHOR_Y, 0.0f);
        break;

    case EAURACRONObjectiveType::PurgatoryAnchor:
        // Shadow/Spectral Dragon - inferior direito
        Position += FVector(FAURACRONMapDimensions::PURGATORY_ANCHOR_X, FAURACRONMapDimensions::PURGATORY_ANCHOR_Y, 0.0f);
        break;

    case EAURACRONObjectiveType::NexusIsland:
        // Centro do mapa
        Position += FVector(FAURACRONMapDimensions::NEXUS_ISLAND_X, FAURACRONMapDimensions::NEXUS_ISLAND_Y, 0.0f);
        break;

    case EAURACRONObjectiveType::SanctuaryIsland:
        {
            // 4 posições simétricas ao redor do centro
            static int32 SanctuaryCounter = 0;
            float Angle = (SanctuaryCounter % 4) * 90.0f * PI / 180.0f; // 0°, 90°, 180°, 270°
            float Distance = FAURACRONMapDimensions::SANCTUARY_ISLAND_DISTANCE_CM;

            Position += FVector(
                FMath::Cos(Angle) * Distance,
                FMath::Sin(Angle) * Distance,
                0.0f
            );

            SanctuaryCounter++;
        }
        break;

    case EAURACRONObjectiveType::ArsenalIsland:
        {
            // 2 posições específicas
            static int32 ArsenalCounter = 0;
            if (ArsenalCounter % 2 == 0)
            {
                Position += FVector(FAURACRONMapDimensions::ARSENAL_ISLAND_1_X, FAURACRONMapDimensions::ARSENAL_ISLAND_1_Y, 0.0f);
            }
            else
            {
                Position += FVector(FAURACRONMapDimensions::ARSENAL_ISLAND_2_X, FAURACRONMapDimensions::ARSENAL_ISLAND_2_Y, 0.0f);
            }
            ArsenalCounter++;
        }
        break;

    case EAURACRONObjectiveType::ChaosIsland:
        {
            // 3 posições rotativas
            static int32 ChaosCounter = 0;
            float Angle = (ChaosCounter % 3) * 120.0f * PI / 180.0f; // 0°, 120°, 240°
            float Distance = 4000.0f; // 40 metros do centro

            Position += FVector(
                FMath::Cos(Angle) * Distance,
                FMath::Sin(Angle) * Distance,
                0.0f
            );
        }
        break;
        
    case EAURACRONObjectiveType::StormCore:
        {
            // Posição entre o Prismal Nexus e o Nexus Island
            FVector PrismalPos = BasePosition + FVector(FAURACRONMapDimensions::PRISMAL_NEXUS_X, FAURACRONMapDimensions::PRISMAL_NEXUS_Y, 0.0f);
            FVector NexusIslandPos = BasePosition + FVector(FAURACRONMapDimensions::NEXUS_ISLAND_X, FAURACRONMapDimensions::NEXUS_ISLAND_Y, 0.0f);
            
            // Posição a 70% do caminho do Nexus Island para o Prismal Nexus
            Position = FMath::Lerp(NexusIslandPos, PrismalPos, 0.7f);
            Position.Z = EnvironmentHeight;
        }

            ChaosCounter++;
        }
        break;
    }

    // Aplicar variações específicas do ambiente
    if (Environment == EAURACRONEnvironmentType::ZephyrFirmament)
    {
        // Elevar ilhas no ambiente aéreo
        if (ObjectiveType == EAURACRONObjectiveType::NexusIsland ||
            ObjectiveType == EAURACRONObjectiveType::SanctuaryIsland ||
            ObjectiveType == EAURACRONObjectiveType::ArsenalIsland)
        {
            Position.Z += 300.0f; // +3m de elevação extra
        }
    }
    else if (Environment == EAURACRONEnvironmentType::PurgatoryRealm)
    {
        // Aprofundar pits no ambiente espectral
        if (ObjectiveType == EAURACRONObjectiveType::PrismalNexus ||
            ObjectiveType == EAURACRONObjectiveType::RadiantAnchor ||
            ObjectiveType == EAURACRONObjectiveType::ZephyrAnchor ||
            ObjectiveType == EAURACRONObjectiveType::PurgatoryAnchor)
        {
            Position.Z -= 200.0f; // -2m mais profundo
        }
    }

    return Position;
}

void AAURACRONPCGObjectiveSystem::CreateObjective(int32 ObjectiveIndex, EAURACRONEnvironmentType Environment)
{
    if (ObjectiveIndex < 0 || ObjectiveIndex >= Objectives.Num())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGObjectiveSystem::CreateObjective - Índice inválido: %d"), ObjectiveIndex);
        return;
    }
    
    FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
    
    // Verificar se o objetivo já foi criado
    if (Objective.CurrentState != EAURACRONObjectiveState::Inactive)
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::CreateObjective - Objetivo %d já está ativo"), ObjectiveIndex);
        return;
    }
    
    // Obter mesh apropriado para o tipo de objetivo e ambiente
    UStaticMesh* ObjectiveMesh = GetObjectiveMesh(Objective.ObjectiveType, Environment);
    
    if (!ObjectiveMesh)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGObjectiveSystem::CreateObjective - Mesh não encontrado para objetivo tipo %d"), (int32)Objective.ObjectiveType);
        return;
    }
    
    // Criar componente de mesh para o objetivo
    UStaticMeshComponent* MeshComponent = NewObject<UStaticMeshComponent>(this, UStaticMeshComponent::StaticClass(), *FString::Printf(TEXT("ObjectiveMesh_%d"), ObjectiveIndex));
    if (!MeshComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGObjectiveSystem::CreateObjective - Falha ao criar MeshComponent"));
        return;
    }
    
    MeshComponent->SetStaticMesh(ObjectiveMesh);
    MeshComponent->SetWorldLocation(Objective.WorldPosition);
    MeshComponent->SetCollisionProfileName(TEXT("Objective"));
    MeshComponent->SetVisibility(true);
    MeshComponent->SetCastShadow(true);
    MeshComponent->SetReceivesDecals(false);
    
    // Configurar propriedades de renderização
    MeshComponent->SetRenderCustomDepth(true);
    MeshComponent->SetCustomDepthStencilValue(GetStencilValueForObjectiveType(Objective.ObjectiveType));
    
    // Registrar componente
    MeshComponent->RegisterComponent();
    MeshComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepWorldTransform);
    
    // Armazenar referência do componente
    Objective.MeshComponent = MeshComponent;
    
    // Adicionar efeitos visuais baseados no tipo de objetivo
    switch (Objective.ObjectiveType)
    {
        case EAURACRONObjectiveType::PrismalNexus:
            AddPrismalNexusEffects(ObjectiveIndex, MeshComponent);
            break;
            
        case EAURACRONObjectiveType::RadiantAnchor:
        case EAURACRONObjectiveType::ZephyrAnchor:
        case EAURACRONObjectiveType::PurgatoryAnchor:
            AddAnchorEffects(ObjectiveIndex, MeshComponent);
            break;
            
        case EAURACRONObjectiveType::PowerCore:
        case EAURACRONObjectiveType::StormCore:
            AddStormCoreEffects(ObjectiveIndex, MeshComponent);
            break;
            
        default:
            // Efeitos genéricos para outros tipos
            break;
    }
    
    // Aplicar características específicas do ambiente
    ApplyEnvironmentCharacteristics(ObjectiveIndex, Environment);
    
    // Atualizar estado do objetivo
    Objective.CurrentState = EAURACRONObjectiveState::Available;
    Objective.Health = Objective.MaxHealth;
    Objective.ControllingTeam = -1; // Neutro inicialmente
    
    // Notificar sistemas relacionados
    OnObjectiveCreated.Broadcast(ObjectiveIndex, Objective.ObjectiveType);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::CreateObjective - Objetivo %d criado com sucesso (Tipo: %d, Ambiente: %d)"),
           ObjectiveIndex, (int32)Objective.ObjectiveType, (int32)Environment);
                AddPrismalNexusEffects(ObjectiveIndex, MeshComponent);
                break;
                
            case EAURACRONObjectiveType::RadiantAnchor:
            case EAURACRONObjectiveType::ZephyrAnchor:
            case EAURACRONObjectiveType::PurgatoryAnchor:
                // Adicionar efeito de âncora
                AddAnchorEffects(ObjectiveIndex, MeshComponent);
                break;
                
            case EAURACRONObjectiveType::StormCore:
                // Adicionar efeito do Storm Core
                AddStormCoreEffects(ObjectiveIndex, MeshComponent);
                break;
                
            default:
                // Efeitos padrão para outros tipos
                break;
            }
            
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Objetivo %d criado visualmente"), ObjectiveIndex);
        }
    }
}

// Métodos auxiliares para criação de efeitos visuais
UStaticMesh* AAURACRONPCGObjectiveSystem::GetObjectiveMesh(EAURACRONObjectiveType ObjectiveType, EAURACRONEnvironmentType Environment)
{
    // Obter mesh apropriado para o tipo de objetivo e ambiente
    if (ObjectiveMeshesByType.Contains(ObjectiveType))
    {
        return ObjectiveMeshesByType[ObjectiveType];
    }
    else if (ObjectiveMeshesByEnvironment.Contains(Environment) && 
             ObjectiveMeshesByEnvironment[Environment].Num() > 0)
    {
        // Usar mesh padrão do ambiente se não houver específico para o tipo
        return ObjectiveMeshesByEnvironment[Environment][0];
    }
    
    // Mesh padrão se nenhum específico for encontrado
    return DefaultObjectiveMesh;
}

void AAURACRONPCGObjectiveSystem::AddPrismalNexusEffects(int32 ObjectiveIndex, UStaticMeshComponent* MeshComponent)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num() && MeshComponent)
    {
        // Adicionar efeito de partículas para o Nexus
        if (PrismalNexusParticleSystem)
        {
            UNiagaraComponent* NiagaraComp = NewObject<UNiagaraComponent>(this);
            NiagaraComp->SetAsset(PrismalNexusParticleSystem);
            NiagaraComp->SetupAttachment(MeshComponent);
            NiagaraComp->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
            NiagaraComp->SetRelativeScale3D(FVector(2.0f));
            NiagaraComp->RegisterComponent();
            
            // Armazenar referência para uso posterior
            if (ObjectiveIndex < ObjectiveEffectComponents.Num())
            {
                ObjectiveEffectComponents[ObjectiveIndex] = NiagaraComp;
            }
            else
            {
                ObjectiveEffectComponents.Add(NiagaraComp);
            }
        }
        
        // Adicionar luz para o Nexus
        UPointLightComponent* LightComp = NewObject<UPointLightComponent>(this);
        LightComp->SetupAttachment(MeshComponent);
        LightComp->SetRelativeLocation(FVector(0.0f, 0.0f, 150.0f));
        LightComp->SetLightColor(FLinearColor(0.1f, 0.8f, 1.0f));
        LightComp->SetIntensity(5000.0f);
        LightComp->SetAttenuationRadius(500.0f);
        LightComp->RegisterComponent();
    }
}

void AAURACRONPCGObjectiveSystem::AddAnchorEffects(int32 ObjectiveIndex, UStaticMeshComponent* MeshComponent)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num() && MeshComponent)
    {
        FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
        
        // Selecionar sistema de partículas com base no tipo de âncora
        UNiagaraSystem* ParticleSystem = nullptr;
        FLinearColor LightColor;
        
        switch (Objective.ObjectiveType)
        {
        case EAURACRONObjectiveType::RadiantAnchor:
            ParticleSystem = RadiantAnchorParticleSystem;
            LightColor = FLinearColor(1.0f, 0.8f, 0.2f); // Dourado
            break;
            
        case EAURACRONObjectiveType::ZephyrAnchor:
            ParticleSystem = ZephyrAnchorParticleSystem;
            LightColor = FLinearColor(0.2f, 0.8f, 1.0f); // Azul claro
            break;
            
        case EAURACRONObjectiveType::PurgatoryAnchor:
            ParticleSystem = PurgatoryAnchorParticleSystem;
            LightColor = FLinearColor(0.8f, 0.2f, 1.0f); // Roxo
            break;
            
        default:
            ParticleSystem = DefaultAnchorParticleSystem;
            LightColor = FLinearColor(0.5f, 0.5f, 0.5f); // Cinza
            break;
        }
        
        // Adicionar sistema de partículas
        if (ParticleSystem)
        {
            UNiagaraComponent* NiagaraComp = NewObject<UNiagaraComponent>(this);
            NiagaraComp->SetAsset(ParticleSystem);
            NiagaraComp->SetupAttachment(MeshComponent);
            NiagaraComp->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f));
            NiagaraComp->RegisterComponent();
            
            // Armazenar referência para uso posterior
            if (ObjectiveIndex < ObjectiveEffectComponents.Num())
            {
                ObjectiveEffectComponents[ObjectiveIndex] = NiagaraComp;
            }
            else
            {
                ObjectiveEffectComponents.Add(NiagaraComp);
            }
        }
        
        // Adicionar luz
        UPointLightComponent* LightComp = NewObject<UPointLightComponent>(this);
    }
}

void AAURACRONPCGObjectiveSystem::AddStormCoreEffects(int32 ObjectiveIndex, UStaticMeshComponent* MeshComponent)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num() && MeshComponent)
    {
        // Adicionar efeito de partículas para o Storm Core
        // Usar sistema de partículas padrão se não houver específico
        UNiagaraSystem* ParticleSystem = DefaultAnchorParticleSystem;
        
        if (ParticleSystem)
        {
            // Criar componente de partículas principal
            UNiagaraComponent* MainNiagaraComp = NewObject<UNiagaraComponent>(this);
            MainNiagaraComp->SetAsset(ParticleSystem);
            MainNiagaraComp->SetupAttachment(MeshComponent);
            MainNiagaraComp->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
            MainNiagaraComp->SetRelativeScale3D(FVector(1.5f));
            MainNiagaraComp->RegisterComponent();
            
            // Armazenar referência para uso posterior
            if (ObjectiveIndex < ObjectiveEffectComponents.Num())
            {
                ObjectiveEffectComponents[ObjectiveIndex] = MainNiagaraComp;
            }
            else
            {
                ObjectiveEffectComponents.Add(MainNiagaraComp);
            }
            
            // Criar componentes de partículas secundários (raios elétricos)
            for (int32 i = 0; i < 3; ++i)
            {
                UNiagaraComponent* SecondaryNiagaraComp = NewObject<UNiagaraComponent>(this);
                SecondaryNiagaraComp->SetAsset(ParticleSystem);
                SecondaryNiagaraComp->SetupAttachment(MeshComponent);
                
                // Posicionar em diferentes pontos ao redor do núcleo
                float Angle = i * 120.0f * PI / 180.0f;
                float Radius = 150.0f;
                SecondaryNiagaraComp->SetRelativeLocation(FVector(
                    FMath::Cos(Angle) * Radius,
                    FMath::Sin(Angle) * Radius,
                    50.0f
                ));
                
                SecondaryNiagaraComp->SetRelativeScale3D(FVector(0.7f));
                SecondaryNiagaraComp->RegisterComponent();
            }
        }
        
        // Adicionar luz principal (azul elétrico)
        UPointLightComponent* MainLightComp = NewObject<UPointLightComponent>(this);
        MainLightComp->SetupAttachment(MeshComponent);
        MainLightComp->SetRelativeLocation(FVector(0.0f, 0.0f, 150.0f));
        MainLightComp->SetLightColor(FLinearColor(0.2f, 0.4f, 1.0f)); // Azul elétrico
        MainLightComp->SetIntensity(8000.0f);
        MainLightComp->SetAttenuationRadius(600.0f);
        MainLightComp->SetCastShadows(true);
        MainLightComp->RegisterComponent();
        
        // Adicionar luz secundária pulsante
        UPointLightComponent* PulsingLightComp = NewObject<UPointLightComponent>(this);
        PulsingLightComp->SetupAttachment(MeshComponent);
        PulsingLightComp->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f));
        PulsingLightComp->SetLightColor(FLinearColor(0.6f, 0.8f, 1.0f)); // Azul claro
        PulsingLightComp->SetIntensity(4000.0f);
        PulsingLightComp->SetAttenuationRadius(400.0f);
        PulsingLightComp->SetCastShadows(false);
        PulsingLightComp->RegisterComponent();
        
        // Adicionar luz adicional
        UPointLightComponent* LightComp = NewObject<UPointLightComponent>(this);
        LightComp->SetupAttachment(MeshComponent);
        LightComp->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
        LightComp->SetLightColor(FLinearColor(0.5f, 0.7f, 1.0f)); // Assumindo uma cor azul similar
        LightComp->SetIntensity(2000.0f);
        LightComp->SetAttenuationRadius(300.0f);
        LightComp->SetCastShadows(false);
        LightComp->RegisterComponent();
    }
}

void AAURACRONPCGObjectiveSystem::ApplyEnvironmentCharacteristics(int32 ObjectiveIndex, EAURACRONEnvironmentType Environment)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num())
    {
        FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
        
        // Aplicar modificações baseadas no ambiente
        switch (Environment)
        {
        case EAURACRONEnvironmentType::RadiantPlains:
            // Aumentar visibilidade e reduzir tempo de captura
            Objective.ObjectiveRadius *= 1.2f;
            Objective.CaptureTime *= 0.9f;
            break;
            
        case EAURACRONEnvironmentType::ZephyrFirmament:
            // Aumentar altura e adicionar efeito de vento
            Objective.WorldPosition.Z += 100.0f;
            
            // Implementar efeitos específicos baseados no tipo de objetivo
            if (Objective.ObjectiveType == EAURACRONObjectiveType::StormCore)
            {
                // Núcleo de Tempestade - Aumenta dano de habilidades e velocidade de movimento
                Objective.MaxHealth *= 1.5f;
                Objective.CurrentHealth = Objective.MaxHealth;
                Objective.BuffDuration *= 2.0f;
                Objective.BuffStrength *= 1.8f;
                Objective.CaptureTime *= 1.2f; // Mais difícil de capturar
                
                // Configurar efeitos visuais de tempestade
                Objective.VisualEffectScale = 1.5f;
                Objective.EffectColor = FLinearColor(0.2f, 0.4f, 1.0f); // Azul elétrico
            }
            else if (Objective.ObjectiveType == EAURACRONObjectiveType::WindSanctuary)
            {
                // Santuários dos Ventos - Concede buff de velocidade e redução de cooldown
                Objective.MaxHealth *= 0.8f; // Mais fácil de capturar
                Objective.CurrentHealth = Objective.MaxHealth;
                Objective.BuffDuration *= 1.5f;
                Objective.CaptureTime *= 0.8f; // Mais rápido de capturar
                
                // Configurar efeitos visuais de vento
                Objective.VisualEffectScale = 1.2f;
                Objective.EffectColor = FLinearColor(0.8f, 0.9f, 1.0f); // Branco azulado
            }
            
            // Efeito de vento implementado via sistema de partículas
            break;
            
        case EAURACRONEnvironmentType::PurgatoryRealm:
            // Aumentar dificuldade e recompensas
            Objective.MaxHealth *= 1.3f;
            Objective.CurrentHealth = Objective.MaxHealth;
            Objective.BuffDuration *= 1.5f;
            break;
            
        case EAURACRONEnvironmentType::CrystalCaverns:
            // Adicionar efeito de cristalização e aumentar defesa
            Objective.MaxHealth *= 1.2f;
            Objective.CurrentHealth = Objective.MaxHealth;
            break;
            
        default:
            break;
        }
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Características do ambiente %d aplicadas ao objetivo %d"), 
               static_cast<int32>(Environment), ObjectiveIndex);
    }
}

void AAURACRONPCGObjectiveSystem::StartRespawnTimer(int32 ObjectiveIndex)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num())
    {
        Objectives[ObjectiveIndex].CurrentState = EAURACRONObjectiveState::Respawning;
    }
}

void AAURACRONPCGObjectiveSystem::OnObjectiveRespawn(int32 ObjectiveIndex)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num())
    {
        FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
        Objective.CurrentState = EAURACRONObjectiveState::Available;
        Objective.CurrentHealth = Objective.MaxHealth;
        Objective.ControllingTeam = -1;
        Objective.TimeUntilRespawn = 0.0f;

        UpdateObjectiveVisibility();

        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Objetivo %d respawnou"), ObjectiveIndex);
    }
}

void AAURACRONPCGObjectiveSystem::UpdateObjectiveVisibility()
{
    // Atualizar visibilidade de todos os objetivos com base no ambiente atual e fase do mapa
    for (int32 i = 0; i < Objectives.Num(); ++i)
    {
        FAURACRONObjectiveInfo& Objective = Objectives[i];
        bool bShouldBeVisible = false;
        
        // Verificar se o objetivo deve estar visível com base na fase do mapa
        if (Objective.bIsActiveInCurrentPhase)
        {
            // Verificar se o objetivo está em um estado visível
            if (Objective.CurrentState != EAURACRONObjectiveState::Inactive && 
                Objective.CurrentState != EAURACRONObjectiveState::Respawning)
            {
                bShouldBeVisible = true;
            }
        }
        
        // Atualizar visibilidade dos componentes visuais do objetivo
        if (i < ObjectiveCollisionComponents.Num() && ObjectiveCollisionComponents[i])
        {
            ObjectiveCollisionComponents[i]->SetVisibility(bShouldBeVisible);
            ObjectiveCollisionComponents[i]->SetCollisionEnabled(
                bShouldBeVisible ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::NoCollision);
        }
    }
    
    // Atualizar visibilidade dos objetivos procedurais
    for (const FAURACRONProceduralObjective& ProcObjective : ActiveProceduralObjectives)
    {
        // Implementação específica para objetivos procedurais
        // Seria necessário manter uma referência aos componentes visuais
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("AURACRONPCGObjectiveSystem: Visibilidade dos objetivos atualizada"));
}

void AAURACRONPCGObjectiveSystem::ClearObjectivesForEnvironment(EAURACRONEnvironmentType Environment)
{
    // Remover objetivos procedurais do ambiente específico
    TArray<FAURACRONProceduralObjective> RemainingObjectives;
    
    for (const FAURACRONProceduralObjective& Objective : ActiveProceduralObjectives)
    {
        if (Objective.EnvironmentType != Environment)
        {
            // Manter objetivos de outros ambientes
            RemainingObjectives.Add(Objective);
        }
        else
        {
            // Destruir componentes visuais associados a este objetivo
            // Seria necessário manter uma referência aos componentes visuais
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Removendo objetivo procedural no ambiente %d"), 
                   static_cast<int32>(Environment));
        }
    }
    
    // Atualizar a lista de objetivos ativos
    ActiveProceduralObjectives = RemainingObjectives;
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Objetivos do ambiente %d removidos"), 
           static_cast<int32>(Environment));
}

void AAURACRONPCGObjectiveSystem::GeneratePhaseSpecificObjectives(EAURACRONMapPhase MapPhase)
{
    // Gerar objetivos específicos para cada fase do mapa
    switch (MapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // Fase inicial - gerar objetivos básicos como ResourceNode e PowerCore
        for (int32 i = 0; i < 3; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::ResourceNode);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::PowerCore);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    case EAURACRONMapPhase::Convergence:
        // Fase média - gerar objetivos mais valiosos como AncientRelic e EnergyConduit
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::AncientRelic);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::EnergyConduit);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    case EAURACRONMapPhase::Intensification:
        // Fase avançada - gerar objetivos poderosos como DefenseTower e NexusFragment
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::DefenseTower);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        for (int32 i = 0; i < 3; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::NexusFragment);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    case EAURACRONMapPhase::Resolution:
        // Fase final - gerar objetivos decisivos como TemporalRift e FusionCatalyst
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::TemporalRift);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::FusionCatalyst);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    default:
        break;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Objetivos específicos da fase %d gerados"), 
           static_cast<int32>(MapPhase));
}

void AAURACRONPCGObjectiveSystem::OnMapPhaseChanged(EAURACRONMapPhase PreviousPhase, EAURACRONMapPhase NewPhase)
{
    // Notificar outros sistemas sobre a mudança de fase
    if (PCGActorReferences.IsValid())
    {
        // Notificar ilhas
        for (AAURACRONPCGIsland* Island : PCGActorReferences.IslandActors)
        {
            if (IsValid(Island))
            {
                Island->UpdateForMapPhase(NewPhase);
            }
        }
        
        // Notificar trilhas e atualizar conexões com objetivos
        for (AAURACRONPCGTrail* Trail : PCGActorReferences.TrailActors)
        {
            if (IsValid(Trail))
            {
                Trail->UpdateForMapPhase(NewPhase);
                
                // Atualizar conexões entre trilhas e objetivos
                UpdateTrailObjectiveConnections(Trail, NewPhase);
            }
        }
        
        // Notificar fluxo prismal
        if (IsValid(PCGActorReferences.PrismalFlowActor))
        {
            PCGActorReferences.PrismalFlowActor->UpdateForMapPhase(NewPhase);
        }
        
        // Notificar gerenciador de ambientes
        UWorld* World = GetWorld();
        if (World)
        {
            TArray<AActor*> FoundManagers;
            UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGEnvironmentManager::StaticClass(), FoundManagers);
            
            if (FoundManagers.Num() > 0)
            {
                if (AAURACRONPCGEnvironmentManager* EnvironmentManager = Cast<AAURACRONPCGEnvironmentManager>(FoundManagers[0]))
                {
                    EnvironmentManager->UpdateForMapPhase(NewPhase);
                    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Notificado EnvironmentManager sobre mudança de fase"));
                }
            }
        }
    }
}

void AAURACRONPCGObjectiveSystem::UpdateTrailObjectiveConnections(AAURACRONPCGTrail* Trail, EAURACRONMapPhase MapPhase)
{
    if (!IsValid(Trail))
    {
        return;
    }
    
    // Obter objetivos ativos na fase atual
    TArray<FVector> ObjectivePositions;
    
    // Adicionar posições dos objetivos principais
    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        if (Objective.bIsActiveInCurrentPhase && 
            (Objective.CurrentState == EAURACRONObjectiveState::Available || 
             Objective.CurrentState == EAURACRONObjectiveState::Captured))
        {
            // Obter posição do objetivo
            int32 Index = &Objective - &Objectives[0];
            if (ObjectiveCollisionComponents.IsValidIndex(Index))
            {
                if (UPrimitiveComponent* Component = ObjectiveCollisionComponents[Index])
                {
                    ObjectivePositions.Add(Component->GetComponentLocation());
                }
            }
        }
    }
    
    // Adicionar posições dos objetivos procedurais
    for (const FAURACRONProceduralObjective& Objective : ActiveProceduralObjectives)
    {
        if (Objective.CurrentState == EAURACRONObjectiveState::Available || 
            Objective.CurrentState == EAURACRONObjectiveState::Captured)
        {
            ObjectivePositions.Add(Objective.WorldPosition);
        }
    }
    
    // Atualizar conexões da trilha com os objetivos
    Trail->UpdateObjectiveConnections(ObjectivePositions, MapPhase);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Atualizadas conexões entre trilha e %d objetivos"), 
           ObjectivePositions.Num());
}
    
    // Ativar eventos específicos da fase
    if (NewPhase == EAURACRONMapPhase::Intensification)
    {
        // Ativar rotação de Chaos Islands na fase de intensificação
        StartChaosIslandRotation();
    }
    else if (NewPhase == EAURACRONMapPhase::Resolution)
    {
        // Ativar todos os portais de transição na fase final
        for (FAURACRONProceduralObjective& Objective : ActiveProceduralObjectives)
        {
            if (Objective.ObjectiveType == EAURACRONObjectiveType::TransitionPortal && 
                Objective.CurrentState == EAURACRONObjectiveState::Inactive)
            {
                Objective.CurrentState = EAURACRONObjectiveState::Available;
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Notificação de mudança de fase de %d para %d"), 
           static_cast<int32>(PreviousPhase), static_cast<int32>(NewPhase));
}

void AAURACRONPCGObjectiveSystem::ApplyMapPhaseEffects()
{
    // Aplicar efeitos específicos da fase atual do mapa aos objetivos
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // Fase inicial - objetivos básicos
        for (FAURACRONObjectiveInfo& Objective : Objectives)
        {
            // Reduzir recompensas na fase inicial
            Objective.BuffDuration *= 0.7f;
        }
        break;
        
    case EAURACRONMapPhase::Convergence:
        // Fase média - aumentar importância dos objetivos
        for (FAURACRONObjectiveInfo& Objective : Objectives)
        {
            // Aumentar recompensas na fase média
            Objective.BuffDuration *= 1.2f;
            
            // Reduzir tempo de respawn
            Objective.RespawnTime *= 0.8f;
        }
        break;
        
    case EAURACRONMapPhase::Intensification:
        // Fase avançada - objetivos mais poderosos
        for (FAURACRONObjectiveInfo& Objective : Objectives)
        {
            // Aumentar significativamente as recompensas
            Objective.BuffDuration *= 1.5f;
            
            // Aumentar resistência dos objetivos
            Objective.MaxHealth *= 1.3f;
            Objective.CurrentHealth = Objective.MaxHealth;
        }
        break;
        
    case EAURACRONMapPhase::Resolution:
        // Fase final - objetivos decisivos
        for (FAURACRONObjectiveInfo& Objective : Objectives)
        {
            // Recompensas máximas na fase final
            Objective.BuffDuration *= 2.0f;
            
            // Reduzir tempo de respawn ao mínimo
            Objective.RespawnTime *= 0.5f;
        }
        break;
        
    default:
        break;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Efeitos da fase %d aplicados aos objetivos"), 
           static_cast<int32>(CurrentMapPhase));
}

void AAURACRONPCGObjectiveSystem::StartChaosIslandRotation()
{
    // Iniciar rotação das Chaos Islands a cada 3 minutos
    GetWorld()->GetTimerManager().SetTimer(
        ChaosIslandEventTimer,
        this,
        &AAURACRONPCGObjectiveSystem::OnChaosIslandRotation,
        FAURACRONMapDimensions::CHAOS_ISLAND_ROTATION_TIME,
        true
    );
}

void AAURACRONPCGObjectiveSystem::OnChaosIslandRotation()
{
    // Ativar próxima Chaos Island
    TriggerChaosIslandEvent(NextChaosIslandIndex);
    NextChaosIslandIndex = (NextChaosIslandIndex + 1) % 3; // Rotacionar entre 0, 1, 2
}

void AAURACRONPCGObjectiveSystem::ApplyObjectiveBuffsToTeam(int32 ObjectiveIndex, int32 TeamIndex)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num() && TeamIndex >= 0)
    {
        FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
        
        // Aplicar buffs específicos com base no tipo de objetivo
        switch (Objective.ObjectiveType)
        {
        case EAURACRONObjectiveType::PrismalNexus:
            // Buff principal - aumenta poder de habilidades e regeneração
            ApplyPrismalNexusBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::RadiantAnchor:
            // Buff de movimento e ataque
            ApplyRadiantAnchorBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::ZephyrAnchor:
            // Buff de velocidade e cooldown
            ApplyZephyrAnchorBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::PurgatoryAnchor:
            // Buff de resistência e dano
            ApplyPurgatoryAnchorBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::PowerCore:
            // Buff de energia e mana
            ApplyPowerCoreBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::StormCore:
            // Buff de dano e velocidade
            ApplyStormCoreBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::WindSanctuary:
            // Buff de velocidade e redução de cooldown
            ApplyWindSanctuaryBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        default:
            // Buff genérico para outros tipos
            ApplyGenericBuff(TeamIndex, Objective.BuffDuration);
            break;
        }
        
        // Notificar outros sistemas sobre o buff aplicado
        OnObjectiveCaptured.Broadcast(ObjectiveIndex, TeamIndex);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Buffs do objetivo %d aplicados ao Team %d por %.1f segundos"), 
               ObjectiveIndex, TeamIndex, Objective.BuffDuration);
    }
}

// Métodos auxiliares para aplicação de buffs específicos
void AAURACRONPCGObjectiveSystem::ApplyPrismalNexusBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Prismal Nexus
    // Aumenta poder de habilidades e regeneração
    if (AAURACRONGameState* GameState = GetWorld()->GetGameState<AAURACRONGameState>())
    {
        // Aplicar buff ao time inteiro
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::AbilityPower, 0.25f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::HealthRegen, 0.30f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::ManaRegen, 0.30f, Duration);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Prismal Nexus buff aplicado ao Team %d"), TeamIndex);
    }
}

void AAURACRONPCGObjectiveSystem::ApplyRadiantAnchorBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Radiant Anchor
    // Aumenta velocidade de movimento e dano de ataque
    if (AAURACRONGameState* GameState = GetWorld()->GetGameState<AAURACRONGameState>())
    {
        // Aplicar buff ao time inteiro
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::MovementSpeed, 0.15f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::AttackDamage, 0.10f, Duration);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Radiant Anchor buff aplicado ao Team %d"), TeamIndex);
    }
}

void AAURACRONPCGObjectiveSystem::ApplyZephyrAnchorBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Zephyr Anchor
    // Reduz cooldowns e aumenta velocidade de ataque
    if (AAURACRONGameState* GameState = GetWorld()->GetGameState<AAURACRONGameState>())
    {
        // Aplicar buff ao time inteiro
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::CooldownReduction, 0.15f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::AttackSpeed, 0.20f, Duration);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Zephyr Anchor buff aplicado ao Team %d"), TeamIndex);
    }
}

void AAURACRONPCGObjectiveSystem::ApplyPurgatoryAnchorBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Purgatory Anchor
    // Aumenta resistência e dano mágico
    if (AAURACRONGameState* GameState = GetWorld()->GetGameState<AAURACRONGameState>())
    {
        // Aplicar buff ao time inteiro
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::Armor, 0.20f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::MagicResist, 0.20f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::MagicDamage, 0.15f, Duration);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Purgatory Anchor buff aplicado ao Team %d"), TeamIndex);
    }
}

void AAURACRONPCGObjectiveSystem::ApplyPowerCoreBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Power Core
    // Aumenta regeneração de energia e mana
    if (AAURACRONGameState* GameState = GetWorld()->GetGameState<AAURACRONGameState>())
    {
        // Aplicar buff ao time inteiro
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::EnergyRegen, 0.25f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::ManaRegen, 0.25f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::UltimateCharge, 0.10f, Duration);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Power Core buff aplicado ao Team %d"), TeamIndex);
    }
}

void AAURACRONPCGObjectiveSystem::ApplyStormCoreBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Storm Core
    // Aumenta dano de ataque, velocidade de movimento e resistência a controle de grupo
    if (AAURACRONGameState* GameState = GetWorld()->GetGameState<AAURACRONGameState>())
    {
        // Aplicar buff ao time inteiro
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::AttackDamage, 0.15f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::MovementSpeed, 0.10f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::CrowdControlResist, 0.25f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::UltimateCharge, 0.05f, Duration);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Storm Core buff aplicado ao Team %d"), TeamIndex);
    }
}

void AAURACRONPCGObjectiveSystem::ApplyWindSanctuaryBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Wind Sanctuary
    // Aumenta velocidade de movimento e reduz cooldowns de habilidades
    if (AAURACRONGameState* GameState = GetWorld()->GetGameState<AAURACRONGameState>())
    {
        // Aplicar buff ao time inteiro
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::MovementSpeed, 0.20f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::CooldownReduction, 0.15f, Duration);
        GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::DodgeChance, 0.10f, Duration);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Wind Sanctuary buff aplicado ao Team %d"), TeamIndex);
    }
}

void AAURACRONPCGObjectiveSystem::ApplyGenericBuff(int32 TeamIndex, float Duration)
{
    // Implementação de buff adaptativo baseado na fase do mapa
    if (AAURACRONGameState* GameState = GetWorld()->GetGameState<AAURACRONGameState>())
    {
        // Aplicar buffs específicos baseados na fase atual
        switch (CurrentMapPhase)
        {
            case EAURACRONMapPhase::Awakening:
                // Buff de exploração e movimento
                GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::MovementSpeed, 0.15f, Duration);
                GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::VisionRange, 0.20f, Duration);
                break;
                
            case EAURACRONMapPhase::Intensification:
                // Buff de combate e resistência
                GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::AttackPower, 0.10f, Duration);
                GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::Defense, 0.10f, Duration);
                break;
                
            case EAURACRONMapPhase::Resolution:
                // Buff de regeneração e cooldown
                GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::HealthRegen, 0.25f, Duration);
                GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::CooldownReduction, 0.15f, Duration);
                break;
                
            default:
                // Fallback para buff balanceado
                GameState->ApplyTeamBuff(TeamIndex, EAURACRONBuffType::AllStats, 0.08f, Duration);
                break;
        }
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Buff adaptativo aplicado ao Team %d na fase %d"), 
               TeamIndex, static_cast<int32>(CurrentMapPhase));
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGObjectiveSystem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades usando APIs modernas do UE 5.6
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, Objectives);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, CurrentEnvironment);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, CurrentMapPhase);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, NextChaosIslandIndex);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, bAutoGenerate);
}

void AAURACRONPCGObjectiveSystem::StartObjectiveSystem()
{
    // Iniciar sistema de objetivos procedurais usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGObjectiveSystem::StartObjectiveSystem - Only server can start objective system"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::StartObjectiveSystem - Starting objective system with modern UE 5.6 APIs"));

    // Parar qualquer timer existente
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Gerar objetivos iniciais
    GenerateObjectives();

    // Configurar timer de geração procedural usando APIs modernas
    if (UWorld* World = GetWorld())
    {
        FTimerHandle ProceduralTimer;
        World->GetTimerManager().SetTimer(ProceduralTimer, this,
            &AAURACRONPCGObjectiveSystem::OnProceduralGenerationTimerExpired,
            300.0f, // 5 minutos
            true); // Repetir
    }

    // Configurar timer de rotação das Chaos Islands
    if (UWorld* World = GetWorld())
    {
        FTimerHandle ChaosRotationTimer;
        World->GetTimerManager().SetTimer(ChaosRotationTimer, this,
            &AAURACRONPCGObjectiveSystem::OnChaosIslandRotation,
            600.0f, // 10 minutos
            true); // Repetir
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::StartObjectiveSystem - Objective system started successfully"));
}

void AAURACRONPCGObjectiveSystem::StopObjectiveSystem()
{
    // Parar sistema de objetivos procedurais usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGObjectiveSystem::StopObjectiveSystem - Only server can stop objective system"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::StopObjectiveSystem - Stopping objective system"));

    // Parar todos os timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Limpar objetivos ativos (mas não remover - apenas desativar)
    for (FAURACRONObjectiveInfo& Objective : Objectives)
    {
        if (Objective.bIsActive)
        {
            Objective.bIsActive = false;
            Objective.RespawnTime = 0.0f;

            // Desativar componentes visuais se existirem
            if (Objective.ObjectiveActor && IsValid(Objective.ObjectiveActor))
            {
                Objective.ObjectiveActor->SetActorHiddenInGame(true);
                Objective.ObjectiveActor->SetActorEnableCollision(false);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::StopObjectiveSystem - Objective system stopped successfully"));
}

void AAURACRONPCGObjectiveSystem::ForceGenerateObjective(EAURACRONObjectiveType ObjectiveType)
{
    // Forçar geração de objetivo procedural usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGObjectiveSystem::ForceGenerateObjective - Only server can force generate objectives"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::ForceGenerateObjective - Forcing generation of objective type %d"), (int32)ObjectiveType);

    // Se tipo específico foi solicitado
    if (ObjectiveType != EAURACRONObjectiveType::None)
    {
        // Buscar objetivo do tipo solicitado
        for (int32 i = 0; i < Objectives.Num(); ++i)
        {
            if (Objectives[i].ObjectiveType == ObjectiveType)
            {
                // Forçar respawn imediato
                Objectives[i].bIsActive = true;
                Objectives[i].RespawnTime = 0.0f;
                Objectives[i].ControllingTeam = -1; // Neutro

                // Reativar ator se existir
                if (Objectives[i].ObjectiveActor && IsValid(Objectives[i].ObjectiveActor))
                {
                    Objectives[i].ObjectiveActor->SetActorHiddenInGame(false);
                    Objectives[i].ObjectiveActor->SetActorEnableCollision(true);
                }

                UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::ForceGenerateObjective - Forced respawn of objective %d"), i);
                return;
            }
        }

        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGObjectiveSystem::ForceGenerateObjective - Objective type %d not found"), (int32)ObjectiveType);
    }
    else
    {
        // Gerar todos os objetivos
        GenerateObjectives();
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::ForceGenerateObjective - Regenerated all objectives"));
    }
}

void AAURACRONPCGObjectiveSystem::OnProceduralGenerationTimerExpired()
{
    // Callback para timer de geração procedural usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        return;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGObjectiveSystem::OnProceduralGenerationTimerExpired - Procedural generation timer expired"));

    // Verificar se algum objetivo precisa ser regenerado
    bool bNeedsRegeneration = false;

    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        // Se objetivo não está ativo e não tem time controlador, pode ser regenerado
        if (!Objective.bIsActive && Objective.ControllingTeam == -1)
        {
            bNeedsRegeneration = true;
            break;
        }
    }

    if (bNeedsRegeneration)
    {
        // Regenerar objetivos baseado na fase atual do mapa
        switch (CurrentMapPhase)
        {
            case EAURACRONMapPhase::Awakening:
                // Fase inicial: gerar objetivos básicos
                ForceGenerateObjective(EAURACRONObjectiveType::RadiantAnchor);
                break;

            case EAURACRONMapPhase::Convergence:
                // Fase média: gerar objetivos estratégicos
                ForceGenerateObjective(EAURACRONObjectiveType::ZephyrAnchor);
                break;

            case EAURACRONMapPhase::Intensification:
                // Fase avançada: gerar objetivos épicos
                ForceGenerateObjective(EAURACRONObjectiveType::PurgatoryAnchor);
                break;

            case EAURACRONMapPhase::Resolution:
                // Fase final: gerar todos os objetivos
                ForceGenerateObjective(EAURACRONObjectiveType::None);
                break;
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::OnProceduralGenerationTimerExpired - Regenerated objectives for map phase %d"), (int32)CurrentMapPhase);
    }
}
