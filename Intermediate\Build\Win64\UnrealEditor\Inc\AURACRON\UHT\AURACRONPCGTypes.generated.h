// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGTypes.h"

#ifdef AURACRON_AURACRONPCGTypes_generated_h
#error "AURACRONPCGTypes.generated.h already included, missing '#pragma once' in AURACRONPCGTypes.h"
#endif
#define AURACRON_AURACRONPCGTypes_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAURACRONMapTacticalAdvantages ************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h_40_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONMapTacticalAdvantages;
// ********** End ScriptStruct FAURACRONMapTacticalAdvantages **************************************

// ********** Begin ScriptStruct FAURACRONMapTypeConfig ********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h_77_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONMapTypeConfig;
// ********** End ScriptStruct FAURACRONMapTypeConfig **********************************************

// ********** Begin ScriptStruct FAURACRONVisualEffectSettings *************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h_119_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONVisualEffectSettings;
// ********** End ScriptStruct FAURACRONVisualEffectSettings ***************************************

// ********** Begin ScriptStruct FAURACRONEnvironmentConfig ****************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h_148_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONEnvironmentConfig;
// ********** End ScriptStruct FAURACRONEnvironmentConfig ******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h

// ********** Begin Enum EAURACRONMapPhase *********************************************************
#define FOREACH_ENUM_EAURACRONMAPPHASE(op) \
	op(EAURACRONMapPhase::Awakening) \
	op(EAURACRONMapPhase::Expansion) \
	op(EAURACRONMapPhase::Convergence) \
	op(EAURACRONMapPhase::Intensification) \
	op(EAURACRONMapPhase::PrismalFlow) \
	op(EAURACRONMapPhase::Resolution) 

enum class EAURACRONMapPhase : uint8;
template<> struct TIsUEnumClass<EAURACRONMapPhase> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONMapPhase>();
// ********** End Enum EAURACRONMapPhase ***********************************************************

// ********** Begin Enum EAURACRONEnvironmentType **************************************************
#define FOREACH_ENUM_EAURACRONENVIRONMENTTYPE(op) \
	op(EAURACRONEnvironmentType::None) \
	op(EAURACRONEnvironmentType::RadiantPlains) \
	op(EAURACRONEnvironmentType::ZephyrFirmament) \
	op(EAURACRONEnvironmentType::PurgatoryRealm) 

enum class EAURACRONEnvironmentType : uint8;
template<> struct TIsUEnumClass<EAURACRONEnvironmentType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnvironmentType>();
// ********** End Enum EAURACRONEnvironmentType ****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
