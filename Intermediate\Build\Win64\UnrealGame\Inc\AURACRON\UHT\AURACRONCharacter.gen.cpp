// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Character/AURACRONCharacter.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONCharacter() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONCharacter();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONCharacter_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONAttributeSet_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONMovementComponent_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONSigilComponent_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONSigilType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
ENGINE_API UClass* Z_Construct_UClass_ACharacter();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemInterface_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayAbility_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AAURACRONCharacter Function ApplyDefaultEffects **************************
struct Z_Construct_UFunction_AAURACRONCharacter_ApplyDefaultEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplica efeitos de gameplay padr\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica efeitos de gameplay padr\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_ApplyDefaultEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "ApplyDefaultEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_ApplyDefaultEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_ApplyDefaultEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONCharacter_ApplyDefaultEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_ApplyDefaultEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execApplyDefaultEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyDefaultEffects();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function ApplyDefaultEffects ****************************

// ********** Begin Class AAURACRONCharacter Function ApplyTemporalEffect **************************
struct Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics
{
	struct AURACRONCharacter_eventApplyTemporalEffect_Parms
	{
		EAURACRONTemporalEffectType EffectType;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Temporal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplica um efeito temporal ao personagem */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica um efeito temporal ao personagem" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EffectType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EffectType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::NewProp_EffectType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::NewProp_EffectType = { "EffectType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventApplyTemporalEffect_Parms, EffectType), Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType, METADATA_PARAMS(0, nullptr) }; // 3331782527
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventApplyTemporalEffect_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::NewProp_EffectType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::NewProp_EffectType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "ApplyTemporalEffect", Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::AURACRONCharacter_eventApplyTemporalEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::AURACRONCharacter_eventApplyTemporalEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execApplyTemporalEffect)
{
	P_GET_ENUM(EAURACRONTemporalEffectType,Z_Param_EffectType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTemporalEffect(EAURACRONTemporalEffectType(Z_Param_EffectType),Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function ApplyTemporalEffect ****************************

// ********** Begin Class AAURACRONCharacter Function EquipSigil ***********************************
struct AURACRONCharacter_eventEquipSigil_Parms
{
	EAURACRONSigilType SigilType;
};
static FName NAME_AAURACRONCharacter_EquipSigil = FName(TEXT("EquipSigil"));
void AAURACRONCharacter::EquipSigil(EAURACRONSigilType SigilType)
{
	AURACRONCharacter_eventEquipSigil_Parms Parms;
	Parms.SigilType=SigilType;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONCharacter_EquipSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Sigilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Equipa um S\xc3\xadgilo espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipa um S\xc3\xadgilo espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventEquipSigil_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::NewProp_SigilType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "EquipSigil", Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::PropPointers), sizeof(AURACRONCharacter_eventEquipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONCharacter_eventEquipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_EquipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_EquipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execEquipSigil)
{
	P_GET_ENUM(EAURACRONSigilType,Z_Param_SigilType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EquipSigil_Implementation(EAURACRONSigilType(Z_Param_SigilType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function EquipSigil *************************************

// ********** Begin Class AAURACRONCharacter Function GetAbilityPower ******************************
struct Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics
{
	struct AURACRONCharacter_eventGetAbilityPower_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m poder de habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m poder de habilidade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventGetAbilityPower_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "GetAbilityPower", Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::AURACRONCharacter_eventGetAbilityPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::AURACRONCharacter_eventGetAbilityPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execGetAbilityPower)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAbilityPower();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function GetAbilityPower ********************************

// ********** Begin Class AAURACRONCharacter Function GetAttackDamage ******************************
struct Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics
{
	struct AURACRONCharacter_eventGetAttackDamage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m dano de ataque */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m dano de ataque" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventGetAttackDamage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "GetAttackDamage", Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::AURACRONCharacter_eventGetAttackDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::AURACRONCharacter_eventGetAttackDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execGetAttackDamage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAttackDamage();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function GetAttackDamage ********************************

// ********** Begin Class AAURACRONCharacter Function GetEquippedSigils ****************************
struct Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics
{
	struct AURACRONCharacter_eventGetEquippedSigils_Parms
	{
		TArray<EAURACRONSigilType> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Sigilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m todos os S\xc3\xadgilos equipados */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m todos os S\xc3\xadgilos equipados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::NewProp_ReturnValue_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventGetEquippedSigils_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::NewProp_ReturnValue_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "GetEquippedSigils", Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::AURACRONCharacter_eventGetEquippedSigils_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::AURACRONCharacter_eventGetEquippedSigils_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execGetEquippedSigils)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<EAURACRONSigilType>*)Z_Param__Result=P_THIS->GetEquippedSigils();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function GetEquippedSigils ******************************

// ********** Begin Class AAURACRONCharacter Function GetHealth ************************************
struct Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics
{
	struct AURACRONCharacter_eventGetHealth_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m vida atual */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m vida atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventGetHealth_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "GetHealth", Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::AURACRONCharacter_eventGetHealth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::AURACRONCharacter_eventGetHealth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_GetHealth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_GetHealth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execGetHealth)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetHealth();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function GetHealth **************************************

// ********** Begin Class AAURACRONCharacter Function GetMana **************************************
struct Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics
{
	struct AURACRONCharacter_eventGetMana_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m mana atual */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m mana atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventGetMana_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "GetMana", Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::AURACRONCharacter_eventGetMana_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::AURACRONCharacter_eventGetMana_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_GetMana()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_GetMana_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execGetMana)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMana();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function GetMana ****************************************

// ********** Begin Class AAURACRONCharacter Function GetMaxHealth *********************************
struct Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics
{
	struct AURACRONCharacter_eventGetMaxHealth_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m vida m\xc3\xa1xima */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m vida m\xc3\xa1xima" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventGetMaxHealth_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "GetMaxHealth", Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::AURACRONCharacter_eventGetMaxHealth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::AURACRONCharacter_eventGetMaxHealth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execGetMaxHealth)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMaxHealth();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function GetMaxHealth ***********************************

// ********** Begin Class AAURACRONCharacter Function GetMaxMana ***********************************
struct Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics
{
	struct AURACRONCharacter_eventGetMaxMana_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m mana m\xc3\xa1xima */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m mana m\xc3\xa1xima" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventGetMaxMana_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "GetMaxMana", Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::AURACRONCharacter_eventGetMaxMana_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::AURACRONCharacter_eventGetMaxMana_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execGetMaxMana)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMaxMana();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function GetMaxMana *************************************

// ********** Begin Class AAURACRONCharacter Function GetTeamID ************************************
struct Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics
{
	struct AURACRONCharacter_eventGetTeamID_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m a equipe do personagem */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m a equipe do personagem" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventGetTeamID_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "GetTeamID", Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::AURACRONCharacter_eventGetTeamID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::AURACRONCharacter_eventGetTeamID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_GetTeamID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_GetTeamID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execGetTeamID)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTeamID();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function GetTeamID **************************************

// ********** Begin Class AAURACRONCharacter Function GiveDefaultAbilities *************************
struct Z_Construct_UFunction_AAURACRONCharacter_GiveDefaultAbilities_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Concede habilidades padr\xc3\xa3o ao personagem */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Concede habilidades padr\xc3\xa3o ao personagem" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_GiveDefaultAbilities_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "GiveDefaultAbilities", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_GiveDefaultAbilities_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_GiveDefaultAbilities_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONCharacter_GiveDefaultAbilities()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_GiveDefaultAbilities_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execGiveDefaultAbilities)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GiveDefaultAbilities();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function GiveDefaultAbilities ***************************

// ********** Begin Class AAURACRONCharacter Function InitializeAbilitySystem **********************
struct Z_Construct_UFunction_AAURACRONCharacter_InitializeAbilitySystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializa o sistema de habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializa o sistema de habilidades" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_InitializeAbilitySystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "InitializeAbilitySystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_InitializeAbilitySystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_InitializeAbilitySystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONCharacter_InitializeAbilitySystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_InitializeAbilitySystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execInitializeAbilitySystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeAbilitySystem();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function InitializeAbilitySystem ************************

// ********** Begin Class AAURACRONCharacter Function IsAlly ***************************************
struct Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics
{
	struct AURACRONCharacter_eventIsAlly_Parms
	{
		const AAURACRONCharacter* OtherCharacter;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se \xc3\xa9 aliado de outro personagem */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se \xc3\xa9 aliado de outro personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherCharacter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherCharacter;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::NewProp_OtherCharacter = { "OtherCharacter", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventIsAlly_Parms, OtherCharacter), Z_Construct_UClass_AAURACRONCharacter_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherCharacter_MetaData), NewProp_OtherCharacter_MetaData) };
void Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONCharacter_eventIsAlly_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONCharacter_eventIsAlly_Parms), &Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::NewProp_OtherCharacter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "IsAlly", Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::AURACRONCharacter_eventIsAlly_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::AURACRONCharacter_eventIsAlly_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_IsAlly()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_IsAlly_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execIsAlly)
{
	P_GET_OBJECT(AAURACRONCharacter,Z_Param_OtherCharacter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAlly(Z_Param_OtherCharacter);
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function IsAlly *****************************************

// ********** Begin Class AAURACRONCharacter Function IsSigilEquipped ******************************
struct Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics
{
	struct AURACRONCharacter_eventIsSigilEquipped_Parms
	{
		EAURACRONSigilType SigilType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Sigilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se um S\xc3\xadgilo est\xc3\xa1 equipado */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se um S\xc3\xadgilo est\xc3\xa1 equipado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventIsSigilEquipped_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
void Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONCharacter_eventIsSigilEquipped_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONCharacter_eventIsSigilEquipped_Parms), &Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "IsSigilEquipped", Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::AURACRONCharacter_eventIsSigilEquipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::AURACRONCharacter_eventIsSigilEquipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execIsSigilEquipped)
{
	P_GET_ENUM(EAURACRONSigilType,Z_Param_SigilType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSigilEquipped(EAURACRONSigilType(Z_Param_SigilType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function IsSigilEquipped ********************************

// ********** Begin Class AAURACRONCharacter Function OnHealthChanged ******************************
struct AURACRONCharacter_eventOnHealthChanged_Parms
{
	float NewHealth;
	float MaxHealth;
};
static FName NAME_AAURACRONCharacter_OnHealthChanged = FName(TEXT("OnHealthChanged"));
void AAURACRONCharacter::OnHealthChanged(float NewHealth, float MaxHealth)
{
	AURACRONCharacter_eventOnHealthChanged_Parms Parms;
	Parms.NewHealth=NewHealth;
	Parms.MaxHealth=MaxHealth;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONCharacter_OnHealthChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando a vida muda */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando a vida muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::NewProp_NewHealth = { "NewHealth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventOnHealthChanged_Parms, NewHealth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventOnHealthChanged_Parms, MaxHealth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::NewProp_NewHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::NewProp_MaxHealth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "OnHealthChanged", Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::PropPointers), sizeof(AURACRONCharacter_eventOnHealthChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONCharacter_eventOnHealthChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAURACRONCharacter Function OnHealthChanged ********************************

// ********** Begin Class AAURACRONCharacter Function OnManaChanged ********************************
struct AURACRONCharacter_eventOnManaChanged_Parms
{
	float NewMana;
	float MaxMana;
};
static FName NAME_AAURACRONCharacter_OnManaChanged = FName(TEXT("OnManaChanged"));
void AAURACRONCharacter::OnManaChanged(float NewMana, float MaxMana)
{
	AURACRONCharacter_eventOnManaChanged_Parms Parms;
	Parms.NewMana=NewMana;
	Parms.MaxMana=MaxMana;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONCharacter_OnManaChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando a mana muda */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando a mana muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewMana;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxMana;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::NewProp_NewMana = { "NewMana", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventOnManaChanged_Parms, NewMana), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::NewProp_MaxMana = { "MaxMana", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventOnManaChanged_Parms, MaxMana), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::NewProp_NewMana,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::NewProp_MaxMana,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "OnManaChanged", Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::PropPointers), sizeof(AURACRONCharacter_eventOnManaChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONCharacter_eventOnManaChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAURACRONCharacter Function OnManaChanged **********************************

// ********** Begin Class AAURACRONCharacter Function OnRep_EquippedSigils *************************
struct Z_Construct_UFunction_AAURACRONCharacter_OnRep_EquippedSigils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_OnRep_EquippedSigils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "OnRep_EquippedSigils", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnRep_EquippedSigils_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_OnRep_EquippedSigils_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONCharacter_OnRep_EquippedSigils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_OnRep_EquippedSigils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execOnRep_EquippedSigils)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_EquippedSigils();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function OnRep_EquippedSigils ***************************

// ********** Begin Class AAURACRONCharacter Function OnRep_TeamID *********************************
struct Z_Construct_UFunction_AAURACRONCharacter_OnRep_TeamID_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de Replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de Replica\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_OnRep_TeamID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "OnRep_TeamID", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnRep_TeamID_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_OnRep_TeamID_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONCharacter_OnRep_TeamID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_OnRep_TeamID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execOnRep_TeamID)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_TeamID();
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function OnRep_TeamID ***********************************

// ********** Begin Class AAURACRONCharacter Function OnSigilEquipped ******************************
struct AURACRONCharacter_eventOnSigilEquipped_Parms
{
	EAURACRONSigilType SigilType;
};
static FName NAME_AAURACRONCharacter_OnSigilEquipped = FName(TEXT("OnSigilEquipped"));
void AAURACRONCharacter::OnSigilEquipped(EAURACRONSigilType SigilType)
{
	AURACRONCharacter_eventOnSigilEquipped_Parms Parms;
	Parms.SigilType=SigilType;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONCharacter_OnSigilEquipped);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando um S\xc3\xadgilo \xc3\xa9 equipado */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando um S\xc3\xadgilo \xc3\xa9 equipado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventOnSigilEquipped_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::NewProp_SigilType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "OnSigilEquipped", Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::PropPointers), sizeof(AURACRONCharacter_eventOnSigilEquipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONCharacter_eventOnSigilEquipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAURACRONCharacter Function OnSigilEquipped ********************************

// ********** Begin Class AAURACRONCharacter Function OnSigilUnequipped ****************************
struct AURACRONCharacter_eventOnSigilUnequipped_Parms
{
	EAURACRONSigilType SigilType;
};
static FName NAME_AAURACRONCharacter_OnSigilUnequipped = FName(TEXT("OnSigilUnequipped"));
void AAURACRONCharacter::OnSigilUnequipped(EAURACRONSigilType SigilType)
{
	AURACRONCharacter_eventOnSigilUnequipped_Parms Parms;
	Parms.SigilType=SigilType;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONCharacter_OnSigilUnequipped);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando um S\xc3\xadgilo \xc3\xa9 removido */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando um S\xc3\xadgilo \xc3\xa9 removido" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventOnSigilUnequipped_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::NewProp_SigilType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "OnSigilUnequipped", Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::PropPointers), sizeof(AURACRONCharacter_eventOnSigilUnequipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONCharacter_eventOnSigilUnequipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAURACRONCharacter Function OnSigilUnequipped ******************************

// ********** Begin Class AAURACRONCharacter Function SetTeamID ************************************
struct AURACRONCharacter_eventSetTeamID_Parms
{
	int32 NewTeamID;
};
static FName NAME_AAURACRONCharacter_SetTeamID = FName(TEXT("SetTeamID"));
void AAURACRONCharacter::SetTeamID(int32 NewTeamID)
{
	AURACRONCharacter_eventSetTeamID_Parms Parms;
	Parms.NewTeamID=NewTeamID;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONCharacter_SetTeamID);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Define a equipe do personagem */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define a equipe do personagem" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewTeamID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics::NewProp_NewTeamID = { "NewTeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventSetTeamID_Parms, NewTeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics::NewProp_NewTeamID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "SetTeamID", Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics::PropPointers), sizeof(AURACRONCharacter_eventSetTeamID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONCharacter_eventSetTeamID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_SetTeamID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_SetTeamID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execSetTeamID)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewTeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTeamID_Implementation(Z_Param_NewTeamID);
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function SetTeamID **************************************

// ********** Begin Class AAURACRONCharacter Function UnequipSigil *********************************
struct AURACRONCharacter_eventUnequipSigil_Parms
{
	EAURACRONSigilType SigilType;
};
static FName NAME_AAURACRONCharacter_UnequipSigil = FName(TEXT("UnequipSigil"));
void AAURACRONCharacter::UnequipSigil(EAURACRONSigilType SigilType)
{
	AURACRONCharacter_eventUnequipSigil_Parms Parms;
	Parms.SigilType=SigilType;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONCharacter_UnequipSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Sigilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remove um S\xc3\xadgilo equipado */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove um S\xc3\xadgilo equipado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONCharacter_eventUnequipSigil_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::NewProp_SigilType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONCharacter, nullptr, "UnequipSigil", Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::PropPointers), sizeof(AURACRONCharacter_eventUnequipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONCharacter_eventUnequipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONCharacter::execUnequipSigil)
{
	P_GET_ENUM(EAURACRONSigilType,Z_Param_SigilType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnequipSigil_Implementation(EAURACRONSigilType(Z_Param_SigilType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONCharacter Function UnequipSigil ***********************************

// ********** Begin Class AAURACRONCharacter *******************************************************
void AAURACRONCharacter::StaticRegisterNativesAAURACRONCharacter()
{
	UClass* Class = AAURACRONCharacter::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyDefaultEffects", &AAURACRONCharacter::execApplyDefaultEffects },
		{ "ApplyTemporalEffect", &AAURACRONCharacter::execApplyTemporalEffect },
		{ "EquipSigil", &AAURACRONCharacter::execEquipSigil },
		{ "GetAbilityPower", &AAURACRONCharacter::execGetAbilityPower },
		{ "GetAttackDamage", &AAURACRONCharacter::execGetAttackDamage },
		{ "GetEquippedSigils", &AAURACRONCharacter::execGetEquippedSigils },
		{ "GetHealth", &AAURACRONCharacter::execGetHealth },
		{ "GetMana", &AAURACRONCharacter::execGetMana },
		{ "GetMaxHealth", &AAURACRONCharacter::execGetMaxHealth },
		{ "GetMaxMana", &AAURACRONCharacter::execGetMaxMana },
		{ "GetTeamID", &AAURACRONCharacter::execGetTeamID },
		{ "GiveDefaultAbilities", &AAURACRONCharacter::execGiveDefaultAbilities },
		{ "InitializeAbilitySystem", &AAURACRONCharacter::execInitializeAbilitySystem },
		{ "IsAlly", &AAURACRONCharacter::execIsAlly },
		{ "IsSigilEquipped", &AAURACRONCharacter::execIsSigilEquipped },
		{ "OnRep_EquippedSigils", &AAURACRONCharacter::execOnRep_EquippedSigils },
		{ "OnRep_TeamID", &AAURACRONCharacter::execOnRep_TeamID },
		{ "SetTeamID", &AAURACRONCharacter::execSetTeamID },
		{ "UnequipSigil", &AAURACRONCharacter::execUnequipSigil },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONCharacter;
UClass* AAURACRONCharacter::GetPrivateStaticClass()
{
	using TClass = AAURACRONCharacter;
	if (!Z_Registration_Info_UClass_AAURACRONCharacter.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONCharacter"),
			Z_Registration_Info_UClass_AAURACRONCharacter.InnerSingleton,
			StaticRegisterNativesAAURACRONCharacter,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONCharacter.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONCharacter_NoRegister()
{
	return AAURACRONCharacter::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONCharacter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe base para todos os personagens do AURACRON\n * Implementa sistema completo de S\xc3\xadgilos, GAS e mec\xc3\xa2nicas espec\xc3\xad""ficas\n */" },
#endif
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "Character/AURACRONCharacter.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe base para todos os personagens do AURACRON\nImplementa sistema completo de S\xc3\xadgilos, GAS e mec\xc3\xa2nicas espec\xc3\xad""ficas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySystemComponent_MetaData[] = {
		{ "Category", "AURACRON|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente do sistema de habilidades */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente do sistema de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeSet_MetaData[] = {
		{ "Category", "AURACRON|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set de atributos do personagem */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set de atributos do personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilComponent_MetaData[] = {
		{ "Category", "AURACRON|Sigilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de S\xc3\xadgilos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de S\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AURACRONMovementComponent_MetaData[] = {
		{ "Category", "AURACRON|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de movimento customizado */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de movimento customizado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultAbilities_MetaData[] = {
		{ "Category", "AURACRON|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidades padr\xc3\xa3o concedidas ao personagem */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidades padr\xc3\xa3o concedidas ao personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultEffects_MetaData[] = {
		{ "Category", "AURACRON|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos padr\xc3\xa3o aplicados ao personagem */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos padr\xc3\xa3o aplicados ao personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InitialGameplayTags_MetaData[] = {
		{ "Category", "AURACRON|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags de gameplay iniciais */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags de gameplay iniciais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamID_MetaData[] = {
		{ "Category", "AURACRON|Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID da equipe (0 = Equipe A, 1 = Equipe B) */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID da equipe (0 = Equipe A, 1 = Equipe B)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedSigils_MetaData[] = {
		{ "Category", "AURACRON|Sigilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** S\xc3\xadgilos atualmente equipados */" },
#endif
		{ "ModuleRelativePath", "Public/Character/AURACRONCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "S\xc3\xadgilos atualmente equipados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AbilitySystemComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AttributeSet;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SigilComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AURACRONMovementComponent;
	static const UECodeGen_Private::FClassPropertyParams NewProp_DefaultAbilities_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DefaultAbilities;
	static const UECodeGen_Private::FClassPropertyParams NewProp_DefaultEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DefaultEffects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InitialGameplayTags;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EquippedSigils_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EquippedSigils_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EquippedSigils;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONCharacter_ApplyDefaultEffects, "ApplyDefaultEffects" }, // 4046748296
		{ &Z_Construct_UFunction_AAURACRONCharacter_ApplyTemporalEffect, "ApplyTemporalEffect" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_EquipSigil, "EquipSigil" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_GetAbilityPower, "GetAbilityPower" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_GetAttackDamage, "GetAttackDamage" }, // 140785161
		{ &Z_Construct_UFunction_AAURACRONCharacter_GetEquippedSigils, "GetEquippedSigils" }, // 412795272
		{ &Z_Construct_UFunction_AAURACRONCharacter_GetHealth, "GetHealth" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_GetMana, "GetMana" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_GetMaxHealth, "GetMaxHealth" }, // 739884896
		{ &Z_Construct_UFunction_AAURACRONCharacter_GetMaxMana, "GetMaxMana" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_GetTeamID, "GetTeamID" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_GiveDefaultAbilities, "GiveDefaultAbilities" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_InitializeAbilitySystem, "InitializeAbilitySystem" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_IsAlly, "IsAlly" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_IsSigilEquipped, "IsSigilEquipped" }, // 243413162
		{ &Z_Construct_UFunction_AAURACRONCharacter_OnHealthChanged, "OnHealthChanged" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_OnManaChanged, "OnManaChanged" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_OnRep_EquippedSigils, "OnRep_EquippedSigils" }, // 122186836
		{ &Z_Construct_UFunction_AAURACRONCharacter_OnRep_TeamID, "OnRep_TeamID" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_OnSigilEquipped, "OnSigilEquipped" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_OnSigilUnequipped, "OnSigilUnequipped" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_SetTeamID, "SetTeamID" }, // **********
		{ &Z_Construct_UFunction_AAURACRONCharacter_UnequipSigil, "UnequipSigil" }, // **********
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static const UECodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONCharacter>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_AbilitySystemComponent = { "AbilitySystemComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONCharacter, AbilitySystemComponent), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySystemComponent_MetaData), NewProp_AbilitySystemComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_AttributeSet = { "AttributeSet", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONCharacter, AttributeSet), Z_Construct_UClass_UAURACRONAttributeSet_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeSet_MetaData), NewProp_AttributeSet_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_SigilComponent = { "SigilComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONCharacter, SigilComponent), Z_Construct_UClass_UAURACRONSigilComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilComponent_MetaData), NewProp_SigilComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_AURACRONMovementComponent = { "AURACRONMovementComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONCharacter, AURACRONMovementComponent), Z_Construct_UClass_UAURACRONMovementComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AURACRONMovementComponent_MetaData), NewProp_AURACRONMovementComponent_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_DefaultAbilities_Inner = { "DefaultAbilities", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_DefaultAbilities = { "DefaultAbilities", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONCharacter, DefaultAbilities), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultAbilities_MetaData), NewProp_DefaultAbilities_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_DefaultEffects_Inner = { "DefaultEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_DefaultEffects = { "DefaultEffects", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONCharacter, DefaultEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultEffects_MetaData), NewProp_DefaultEffects_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_InitialGameplayTags = { "InitialGameplayTags", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONCharacter, InitialGameplayTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InitialGameplayTags_MetaData), NewProp_InitialGameplayTags_MetaData) }; // 2104890724
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_TeamID = { "TeamID", "OnRep_TeamID", (EPropertyFlags)0x0020080100000034, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONCharacter, TeamID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamID_MetaData), NewProp_TeamID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_EquippedSigils_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_EquippedSigils_Inner = { "EquippedSigils", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_EquippedSigils = { "EquippedSigils", "OnRep_EquippedSigils", (EPropertyFlags)0x0020080100000034, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONCharacter, EquippedSigils), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedSigils_MetaData), NewProp_EquippedSigils_MetaData) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONCharacter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_AbilitySystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_AttributeSet,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_SigilComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_AURACRONMovementComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_DefaultAbilities_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_DefaultAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_DefaultEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_DefaultEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_InitialGameplayTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_EquippedSigils_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_EquippedSigils_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONCharacter_Statics::NewProp_EquippedSigils,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONCharacter_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONCharacter_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ACharacter,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONCharacter_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_AAURACRONCharacter_Statics::InterfaceParams[] = {
	{ Z_Construct_UClass_UAbilitySystemInterface_NoRegister, (int32)VTABLE_OFFSET(AAURACRONCharacter, IAbilitySystemInterface), false },  // 1199015870
};
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONCharacter_Statics::ClassParams = {
	&AAURACRONCharacter::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONCharacter_Statics::PropPointers,
	InterfaceParams,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONCharacter_Statics::PropPointers),
	UE_ARRAY_COUNT(InterfaceParams),
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONCharacter_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONCharacter_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONCharacter()
{
	if (!Z_Registration_Info_UClass_AAURACRONCharacter.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONCharacter.OuterSingleton, Z_Construct_UClass_AAURACRONCharacter_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONCharacter.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AAURACRONCharacter::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_TeamID(TEXT("TeamID"));
	static FName Name_EquippedSigils(TEXT("EquippedSigils"));
	const bool bIsValid = true
		&& Name_TeamID == ClassReps[(int32)ENetFields_Private::TeamID].Property->GetFName()
		&& Name_EquippedSigils == ClassReps[(int32)ENetFields_Private::EquippedSigils].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AAURACRONCharacter"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONCharacter);
AAURACRONCharacter::~AAURACRONCharacter() {}
// ********** End Class AAURACRONCharacter *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONCharacter, AAURACRONCharacter::StaticClass, TEXT("AAURACRONCharacter"), &Z_Registration_Info_UClass_AAURACRONCharacter, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONCharacter), 1264049002U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h__Script_AURACRON_1321326713(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
