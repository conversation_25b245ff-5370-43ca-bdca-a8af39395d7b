// AURACRONPCGMathLibrary.cpp
// Biblioteca Matemática para PCG AURACRON - UE 5.6
// Implementação das funções matemáticas avançadas para geração procedural

#include "PCG/AURACRONPCGMathLibrary.h"
#include "Kismet/KismetMathLibrary.h"
#include "Engine/Engine.h"

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE CURVAS E SPLINES
// ========================================

FAURACRONSplineCurve UAURACRONPCGMathLibrary::CreateSerpentineCurve(
    const FVector& StartPoint,
    const FVector& EndPoint,
    int32 NumControlPoints,
    float Amplitude,
    float Frequency)
{
    FAURACRONSplineCurve Curve;
    
    if (NumControlPoints < 2)
    {
        NumControlPoints = 2;
    }
    
    // Calcular direção principal
    FVector Direction = (EndPoint - StartPoint).GetSafeNormal();
    FVector Perpendicular = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();
    float TotalDistance = FVector::Dist(StartPoint, EndPoint);
    
    // Gerar pontos de controle
    for (int32 i = 0; i < NumControlPoints; ++i)
    {
        float T = static_cast<float>(i) / (NumControlPoints - 1);
        
        // Posição base ao longo da linha reta
        FVector BasePosition = FMath::Lerp(StartPoint, EndPoint, T);
        
        // Adicionar oscilação serpentina
        float SineValue = FMath::Sin(T * Frequency * PI);
        FVector Offset = Perpendicular * SineValue * Amplitude;
        
        FVector ControlPoint = BasePosition + Offset;
        Curve.ControlPoints.Add(ControlPoint);
        
        // Calcular tangente
        FVector Tangent = Direction;
        if (i > 0 && i < NumControlPoints - 1)
        {
            float DerivativeT = T * Frequency * PI;
            float CosValue = FMath::Cos(DerivativeT);
            FVector TangentOffset = Perpendicular * CosValue * Amplitude * Frequency * PI / TotalDistance;
            Tangent = (Direction + TangentOffset).GetSafeNormal();
        }
        
        Curve.Tangents.Add(Tangent * 1000.0f); // Escalar tangente
    }
    
    return Curve;
}

FVector UAURACRONPCGMathLibrary::EvaluateSplineCurve(const FAURACRONSplineCurve& Curve, float T)
{
    if (Curve.ControlPoints.Num() < 2)
    {
        return FVector::ZeroVector;
    }
    
    T = FMath::Clamp(T, 0.0f, 1.0f);
    
    // Encontrar segmento
    float ScaledT = T * (Curve.ControlPoints.Num() - 1);
    int32 Index = FMath::FloorToInt(ScaledT);
    float LocalT = ScaledT - Index;
    
    if (Index >= Curve.ControlPoints.Num() - 1)
    {
        return Curve.ControlPoints.Last();
    }
    
    // Interpolação cúbica de Hermite
    const FVector& P0 = Curve.ControlPoints[Index];
    const FVector& P1 = Curve.ControlPoints[Index + 1];
    
    FVector T0 = (Index < Curve.Tangents.Num()) ? Curve.Tangents[Index] : FVector::ZeroVector;
    FVector T1 = (Index + 1 < Curve.Tangents.Num()) ? Curve.Tangents[Index + 1] : FVector::ZeroVector;
    
    float T2 = LocalT * LocalT;
    float T3 = T2 * LocalT;
    
    float H1 = 2.0f * T3 - 3.0f * T2 + 1.0f;
    float H2 = -2.0f * T3 + 3.0f * T2;
    float H3 = T3 - 2.0f * T2 + LocalT;
    float H4 = T3 - T2;
    
    return H1 * P0 + H2 * P1 + H3 * T0 + H4 * T1;
}

FVector UAURACRONPCGMathLibrary::GetSplineTangent(const FAURACRONSplineCurve& Curve, float T)
{
    if (Curve.ControlPoints.Num() < 2)
    {
        return FVector::ForwardVector;
    }
    
    // Calcular tangente usando diferença finita
    float Delta = 0.001f;
    FVector P1 = EvaluateSplineCurve(Curve, FMath::Clamp(T - Delta, 0.0f, 1.0f));
    FVector P2 = EvaluateSplineCurve(Curve, FMath::Clamp(T + Delta, 0.0f, 1.0f));
    
    return (P2 - P1).GetSafeNormal();
}

TArray<FVector> UAURACRONPCGMathLibrary::CreateOrbitalPath(
    const FVector& Center,
    float Radius,
    float Height,
    int32 NumPoints,
    float PhaseOffset)
{
    TArray<FVector> Points;
    
    for (int32 i = 0; i < NumPoints; ++i)
    {
        float Angle = 2.0f * PI * i / NumPoints + PhaseOffset;
        float X = Center.X + Radius * FMath::Cos(Angle);
        float Y = Center.Y + Radius * FMath::Sin(Angle);
        float Z = Center.Z + Height;
        
        Points.Add(FVector(X, Y, Z));
    }
    
    return Points;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE RUÍDO PROCEDURAL
// ========================================

float UAURACRONPCGMathLibrary::GeneratePerlinNoise2D(float X, float Y, const FAURACRONNoisePattern& Pattern)
{
    return PerlinNoise(X * Pattern.Frequency, Y * Pattern.Frequency, Pattern.Seed) * Pattern.Amplitude;
}

float UAURACRONPCGMathLibrary::GeneratePerlinNoise3D(float X, float Y, float Z, const FAURACRONNoisePattern& Pattern)
{
    // Implementação simplificada de ruído 3D usando múltiplas camadas 2D
    float Noise1 = PerlinNoise(X * Pattern.Frequency, Y * Pattern.Frequency, Pattern.Seed);
    float Noise2 = PerlinNoise(Y * Pattern.Frequency, Z * Pattern.Frequency, Pattern.Seed + 1000);
    float Noise3 = PerlinNoise(Z * Pattern.Frequency, X * Pattern.Frequency, Pattern.Seed + 2000);
    
    return (Noise1 + Noise2 + Noise3) / 3.0f * Pattern.Amplitude;
}

float UAURACRONPCGMathLibrary::GenerateFractalNoise(float X, float Y, const FAURACRONNoisePattern& Pattern)
{
    float Result = 0.0f;
    float Amplitude = Pattern.Amplitude;
    float Frequency = Pattern.Frequency;
    float MaxValue = 0.0f;
    
    for (int32 i = 0; i < Pattern.Octaves; ++i)
    {
        Result += PerlinNoise(X * Frequency, Y * Frequency, Pattern.Seed + i * 1000) * Amplitude;
        MaxValue += Amplitude;
        
        Amplitude *= Pattern.Persistence;
        Frequency *= Pattern.Lacunarity;
    }
    
    return Result / MaxValue * Pattern.Amplitude;
}

TArray<float> UAURACRONPCGMathLibrary::GenerateHeightMap(
    int32 Width,
    int32 Height,
    const FAURACRONNoisePattern& Pattern,
    float MinHeight,
    float MaxHeight)
{
    TArray<float> HeightMap;
    HeightMap.Reserve(Width * Height);
    
    for (int32 Y = 0; Y < Height; ++Y)
    {
        for (int32 X = 0; X < Width; ++X)
        {
            float NoiseValue = GenerateFractalNoise(static_cast<float>(X), static_cast<float>(Y), Pattern);
            
            // Normalizar para [0, 1]
            float NormalizedValue = (NoiseValue + Pattern.Amplitude) / (2.0f * Pattern.Amplitude);
            NormalizedValue = FMath::Clamp(NormalizedValue, 0.0f, 1.0f);
            
            // Mapear para o intervalo de altura desejado
            float HeightValue = FMath::Lerp(MinHeight, MaxHeight, NormalizedValue);
            HeightMap.Add(HeightValue);
        }
    }
    
    return HeightMap;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE DISTRIBUIÇÃO ESPACIAL
// ========================================

TArray<FVector> UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
    const FVector& Center,
    float Radius,
    float MinDistance,
    int32 MaxAttempts,
    int32 Seed)
{
    TArray<FVector> Points;
    TArray<FVector> ActiveList;
    
    FRandomStream RandomStream(Seed);
    
    // Adicionar primeiro ponto
    FVector FirstPoint = Center + FVector(
        RandomStream.FRandRange(-Radius, Radius),
        RandomStream.FRandRange(-Radius, Radius),
        0.0f
    );
    
    Points.Add(FirstPoint);
    ActiveList.Add(FirstPoint);
    
    while (ActiveList.Num() > 0)
    {
        int32 RandomIndex = RandomStream.RandRange(0, ActiveList.Num() - 1);
        FVector CurrentPoint = ActiveList[RandomIndex];
        bool bFoundValidPoint = false;
        
        for (int32 Attempt = 0; Attempt < MaxAttempts; ++Attempt)
        {
            float Angle = RandomStream.FRandRange(0.0f, 2.0f * PI);
            float Distance = RandomStream.FRandRange(MinDistance, 2.0f * MinDistance);
            
            FVector NewPoint = CurrentPoint + FVector(
                Distance * FMath::Cos(Angle),
                Distance * FMath::Sin(Angle),
                CurrentPoint.Z
            );
            
            // Verificar se está dentro do raio
            if (FVector::Dist2D(NewPoint, Center) > Radius)
            {
                continue;
            }
            
            // Verificar distância mínima de outros pontos
            bool bTooClose = false;
            for (const FVector& ExistingPoint : Points)
            {
                if (FVector::Dist2D(NewPoint, ExistingPoint) < MinDistance)
                {
                    bTooClose = true;
                    break;
                }
            }
            
            if (!bTooClose)
            {
                Points.Add(NewPoint);
                ActiveList.Add(NewPoint);
                bFoundValidPoint = true;
                break;
            }
        }
        
        if (!bFoundValidPoint)
        {
            ActiveList.RemoveAt(RandomIndex);
        }
    }
    
    return Points;
}

TArray<FVector> UAURACRONPCGMathLibrary::GenerateHexagonalGrid(
    const FVector& Center,
    float Radius,
    float Spacing,
    bool bAddRandomOffset,
    float RandomOffsetAmount)
{
    TArray<FVector> Points;
    FRandomStream RandomStream(12345);
    
    int32 MaxRings = FMath::CeilToInt(Radius / Spacing);
    
    // Centro
    FVector CenterPoint = Center;
    if (bAddRandomOffset)
    {
        CenterPoint += FVector(
            RandomStream.FRandRange(-Spacing * RandomOffsetAmount, Spacing * RandomOffsetAmount),
            RandomStream.FRandRange(-Spacing * RandomOffsetAmount, Spacing * RandomOffsetAmount),
            0.0f
        );
    }
    Points.Add(CenterPoint);
    
    // Anéis hexagonais
    for (int32 Ring = 1; Ring <= MaxRings; ++Ring)
    {
        int32 PointsInRing = 6 * Ring;
        
        for (int32 i = 0; i < PointsInRing; ++i)
        {
            float Angle = 2.0f * PI * i / PointsInRing;
            float Distance = Ring * Spacing;
            
            FVector Point = Center + FVector(
                Distance * FMath::Cos(Angle),
                Distance * FMath::Sin(Angle),
                Center.Z
            );
            
            if (bAddRandomOffset)
            {
                Point += FVector(
                    RandomStream.FRandRange(-Spacing * RandomOffsetAmount, Spacing * RandomOffsetAmount),
                    RandomStream.FRandRange(-Spacing * RandomOffsetAmount, Spacing * RandomOffsetAmount),
                    0.0f
                );
            }
            
            // Verificar se está dentro do raio
            if (FVector::Dist2D(Point, Center) <= Radius)
            {
                Points.Add(Point);
            }
        }
    }
    
    return Points;
}

TArray<FVector> UAURACRONPCGMathLibrary::DistributePointsAlongCurve(
    const FAURACRONSplineCurve& Curve,
    float Spacing,
    bool bAlignToTangent)
{
    TArray<FVector> Points;
    
    if (Curve.ControlPoints.Num() < 2 || Spacing <= 0.0f)
    {
        return Points;
    }
    
    // Estimar comprimento da curva
    float CurveLength = 0.0f;
    const int32 LengthSamples = 100;
    FVector PreviousPoint = EvaluateSplineCurve(Curve, 0.0f);
    
    for (int32 i = 1; i <= LengthSamples; ++i)
    {
        float T = static_cast<float>(i) / LengthSamples;
        FVector CurrentPoint = EvaluateSplineCurve(Curve, T);
        CurveLength += FVector::Dist(PreviousPoint, CurrentPoint);
        PreviousPoint = CurrentPoint;
    }
    
    // Distribuir pontos
    int32 NumPoints = FMath::FloorToInt(CurveLength / Spacing);
    
    for (int32 i = 0; i <= NumPoints; ++i)
    {
        float T = static_cast<float>(i) / FMath::Max(NumPoints, 1);
        FVector Point = EvaluateSplineCurve(Curve, T);
        Points.Add(Point);
    }
    
    return Points;
}

// ========================================
// FUNÇÕES AUXILIARES INTERNAS
// ========================================

float UAURACRONPCGMathLibrary::SmoothStep(float Edge0, float Edge1, float X)
{
    float T = FMath::Clamp((X - Edge0) / (Edge1 - Edge0), 0.0f, 1.0f);
    return T * T * (3.0f - 2.0f * T);
}

float UAURACRONPCGMathLibrary::PerlinNoise(float X, float Y, int32 Seed)
{
    int32 Xi = FMath::FloorToInt(X) & 255;
    int32 Yi = FMath::FloorToInt(Y) & 255;
    
    float Xf = X - FMath::FloorToFloat(X);
    float Yf = Y - FMath::FloorToFloat(Y);
    
    float U = Fade(Xf);
    float V = Fade(Yf);
    
    int32 AA = Hash(Xi, Yi, Seed);
    int32 AB = Hash(Xi, Yi + 1, Seed);
    int32 BA = Hash(Xi + 1, Yi, Seed);
    int32 BB = Hash(Xi + 1, Yi + 1, Seed);
    
    float X1 = FMath::Lerp(Grad(AA, Xf, Yf), Grad(BA, Xf - 1, Yf), U);
    float X2 = FMath::Lerp(Grad(AB, Xf, Yf - 1), Grad(BB, Xf - 1, Yf - 1), U);
    
    return FMath::Lerp(X1, X2, V);
}

float UAURACRONPCGMathLibrary::Fade(float T)
{
    return T * T * T * (T * (T * 6.0f - 15.0f) + 10.0f);
}

float UAURACRONPCGMathLibrary::Grad(int32 Hash, float X, float Y)
{
    int32 H = Hash & 15;
    float U = H < 8 ? X : Y;
    float V = H < 4 ? Y : H == 12 || H == 14 ? X : 0;
    return ((H & 1) == 0 ? U : -U) + ((H & 2) == 0 ? V : -V);
}

int32 UAURACRONPCGMathLibrary::Hash(int32 X, int32 Y, int32 Seed)
{
    int32 Hash = Seed;
    Hash ^= X * 374761393 + Y * 668265263;
    Hash = (Hash ^ (Hash >> 13)) * 1274126177;
    return Hash ^ (Hash >> 16);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE GEOMETRIA PROCEDURAL
// ========================================

TArray<FVector> UAURACRONPCGMathLibrary::GenerateCrystallinePlateauVertices(
    const FVector& Center,
    float Radius,
    float Height,
    int32 NumSides,
    float Irregularity)
{
    TArray<FVector> Vertices;
    FRandomStream RandomStream(12345);

    // Base do platô
    for (int32 i = 0; i < NumSides; ++i)
    {
        float Angle = 2.0f * PI * i / NumSides;
        float RadiusVariation = 1.0f + RandomStream.FRandRange(-Irregularity, Irregularity);
        float ActualRadius = Radius * RadiusVariation;

        float X = Center.X + ActualRadius * FMath::Cos(Angle);
        float Y = Center.Y + ActualRadius * FMath::Sin(Angle);

        Vertices.Add(FVector(X, Y, Center.Z));
    }

    // Topo do platô
    for (int32 i = 0; i < NumSides; ++i)
    {
        float Angle = 2.0f * PI * i / NumSides;
        float RadiusVariation = 1.0f + RandomStream.FRandRange(-Irregularity * 0.5f, Irregularity * 0.5f);
        float ActualRadius = Radius * 0.8f * RadiusVariation; // Topo menor que a base

        float X = Center.X + ActualRadius * FMath::Cos(Angle);
        float Y = Center.Y + ActualRadius * FMath::Sin(Angle);

        Vertices.Add(FVector(X, Y, Center.Z + Height));
    }

    return Vertices;
}

TArray<FVector> UAURACRONPCGMathLibrary::GenerateLivingCanyonPath(
    const FVector& StartPoint,
    const FVector& EndPoint,
    float Width,
    float Depth,
    int32 NumSegments)
{
    TArray<FVector> PathPoints;

    // Criar curva serpentina para o cânion
    FAURACRONSplineCurve CanyonCurve = CreateSerpentineCurve(
        StartPoint, EndPoint, NumSegments, Width * 0.5f, 2.0f
    );

    // Gerar pontos ao longo da curva
    for (int32 i = 0; i <= NumSegments; ++i)
    {
        float T = static_cast<float>(i) / NumSegments;
        FVector CenterPoint = EvaluateSplineCurve(CanyonCurve, T);

        // Adicionar profundidade variável
        float DepthVariation = 1.0f + 0.3f * FMath::Sin(T * 4.0f * PI);
        CenterPoint.Z -= Depth * DepthVariation;

        PathPoints.Add(CenterPoint);

        // Adicionar pontos laterais para definir a largura
        FVector Tangent = GetSplineTangent(CanyonCurve, T);
        FVector Perpendicular = FVector::CrossProduct(Tangent, FVector::UpVector).GetSafeNormal();

        PathPoints.Add(CenterPoint + Perpendicular * Width * 0.5f);
        PathPoints.Add(CenterPoint - Perpendicular * Width * 0.5f);
    }

    return PathPoints;
}

TArray<FVector> UAURACRONPCGMathLibrary::GenerateBreathingForestPositions(
    const FVector& Center,
    float Radius,
    int32 TreeCount,
    float MinDistance,
    float BreathingAmplitude,
    float Time)
{
    TArray<FVector> TreePositions;

    // Gerar posições base usando amostragem de Poisson
    TArray<FVector> BasePositions = GeneratePoissonDiscSampling(
        Center, Radius, MinDistance, 30, 54321
    );

    // Limitar ao número desejado de árvores
    int32 ActualTreeCount = FMath::Min(TreeCount, BasePositions.Num());

    for (int32 i = 0; i < ActualTreeCount; ++i)
    {
        FVector BasePosition = BasePositions[i];

        // Adicionar efeito de "respiração" - movimento sutil baseado no tempo
        float BreathingPhase = Time + i * 0.1f; // Cada árvore com fase ligeiramente diferente
        float BreathingOffset = FMath::Sin(BreathingPhase) * BreathingAmplitude;

        // Movimento em direção radial do centro
        FVector DirectionFromCenter = (BasePosition - Center).GetSafeNormal2D();
        FVector BreathingPosition = BasePosition + DirectionFromCenter * BreathingOffset;

        TreePositions.Add(BreathingPosition);
    }

    return TreePositions;
}

TArray<FVector> UAURACRONPCGMathLibrary::GenerateTectonicBridgePoints(
    const FVector& StartPoint,
    const FVector& EndPoint,
    float Width,
    int32 NumSupports,
    float ArchHeight)
{
    TArray<FVector> BridgePoints;

    FVector Direction = (EndPoint - StartPoint).GetSafeNormal();
    FVector Perpendicular = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();
    float BridgeLength = FVector::Dist(StartPoint, EndPoint);

    // Pontos principais da ponte
    for (int32 i = 0; i <= NumSupports + 1; ++i)
    {
        float T = static_cast<float>(i) / (NumSupports + 1);
        FVector BasePosition = FMath::Lerp(StartPoint, EndPoint, T);

        // Adicionar curvatura de arco
        float ArchT = FMath::Sin(T * PI);
        BasePosition.Z += ArchHeight * ArchT;

        BridgePoints.Add(BasePosition);

        // Adicionar pontos laterais para definir a largura
        BridgePoints.Add(BasePosition + Perpendicular * Width * 0.5f);
        BridgePoints.Add(BasePosition - Perpendicular * Width * 0.5f);
    }

    return BridgePoints;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE TRANSFORMAÇÃO TEMPORAL
// ========================================

FVector UAURACRONPCGMathLibrary::GetTimeBasedPosition(
    const FVector& BasePosition,
    float TimeOfDay,
    float Amplitude,
    float Frequency)
{
    float TimeRadians = TimeOfDay * 2.0f * PI * Frequency;

    FVector Offset = FVector(
        FMath::Sin(TimeRadians) * Amplitude,
        FMath::Cos(TimeRadians) * Amplitude,
        FMath::Sin(TimeRadians * 0.5f) * Amplitude * 0.5f
    );

    return BasePosition + Offset;
}

float UAURACRONPCGMathLibrary::GetLunarPhaseIntensity(float TimeOfDay, float LunarCycle)
{
    // Simular ciclo lunar (0 = lua nova, 0.5 = lua cheia)
    float LunarPhase = FMath::Fmod(TimeOfDay * LunarCycle, 1.0f);

    // Intensidade máxima na lua cheia
    float Intensity = 1.0f - FMath::Abs(LunarPhase - 0.5f) * 2.0f;

    // Considerar também a hora do dia (lua mais visível à noite)
    float NightIntensity = FMath::Max(0.0f, FMath::Cos((TimeOfDay - 0.5f) * 2.0f * PI));

    return Intensity * NightIntensity;
}

float UAURACRONPCGMathLibrary::GetSolarIntensity(float TimeOfDay)
{
    // TimeOfDay de 0 a 1 representa 24 horas
    // Intensidade máxima ao meio-dia (0.5)
    float SolarAngle = (TimeOfDay - 0.5f) * 2.0f * PI;
    float Intensity = FMath::Max(0.0f, FMath::Cos(SolarAngle));

    return Intensity;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE OTIMIZAÇÃO E PERFORMANCE
// ========================================

int32 UAURACRONPCGMathLibrary::CalculateLODLevel(
    const FVector& ObjectPosition,
    const FVector& ViewerPosition,
    float LOD0Distance,
    float LOD1Distance,
    float LOD2Distance)
{
    float Distance = FVector::Dist(ObjectPosition, ViewerPosition);

    if (Distance <= LOD0Distance)
    {
        return 0; // Máxima qualidade
    }
    else if (Distance <= LOD1Distance)
    {
        return 1; // Qualidade média
    }
    else if (Distance <= LOD2Distance)
    {
        return 2; // Qualidade baixa
    }
    else
    {
        return 3; // Não renderizar
    }
}

bool UAURACRONPCGMathLibrary::ShouldRenderObject(
    const FVector& ObjectPosition,
    float ObjectRadius,
    const FVector& ViewerPosition,
    const FVector& ViewerForward,
    float ViewDistance,
    float FOVDegrees)
{
    // Verificar distância
    float Distance = FVector::Dist(ObjectPosition, ViewerPosition);
    if (Distance > ViewDistance + ObjectRadius)
    {
        return false;
    }

    // Verificar frustum (simplificado)
    FVector ToObject = (ObjectPosition - ViewerPosition).GetSafeNormal();
    float DotProduct = FVector::DotProduct(ViewerForward, ToObject);
    float FOVRadians = FMath::DegreesToRadians(FOVDegrees * 0.5f);

    if (DotProduct < FMath::Cos(FOVRadians))
    {
        // Verificar se o objeto está parcialmente visível considerando seu raio
        float AngleToObject = FMath::Acos(DotProduct);
        float ObjectAngularSize = FMath::Atan(ObjectRadius / Distance);

        return (AngleToObject - ObjectAngularSize) <= FOVRadians;
    }

    return true;
}

float UAURACRONPCGMathLibrary::CalculateObjectDensity(
    float BaselineFrameRate,
    float CurrentFrameRate,
    float MinDensity,
    float MaxDensity)
{
    if (BaselineFrameRate <= 0.0f)
    {
        return MaxDensity;
    }

    float PerformanceRatio = CurrentFrameRate / BaselineFrameRate;
    PerformanceRatio = FMath::Clamp(PerformanceRatio, 0.1f, 2.0f);

    // Se o framerate está baixo, reduzir densidade
    float Density = FMath::Lerp(MinDensity, MaxDensity, PerformanceRatio);

    return FMath::Clamp(Density, MinDensity, MaxDensity);
}


