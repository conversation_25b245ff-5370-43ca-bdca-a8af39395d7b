// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Data/AURACRONEnums.h"

#ifdef AURACRON_AURACRONEnums_generated_h
#error "AURACRONEnums.generated.h already included, missing '#pragma once' in AURACRONEnums.h"
#endif
#define AURACRON_AURACRONEnums_generated_h

#include "Templates/IsUEnumClass.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ReflectedTypeAccessors.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Data_AURACRONEnums_h

// ********** Begin Enum EAURACRONMapPhase *********************************************************
#define FOREACH_ENUM_EAURACRONMAPPHASE(op) \
	op(EAURACRONMapPhase::Awakening) \
	op(EAURACRONMapPhase::Expansion) \
	op(EAURACRONMapPhase::Convergence) \
	op(EAURACRONMapPhase::Intensification) \
	op(EAURACRONMapPhase::Resolution) 

enum class EAURACRONMapPhase : uint8;
template<> struct TIsUEnumClass<EAURACRONMapPhase> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONMapPhase>();
// ********** End Enum EAURACRONMapPhase ***********************************************************

// ********** Begin Enum EAURACRONEnvironmentType **************************************************
#define FOREACH_ENUM_EAURACRONENVIRONMENTTYPE(op) \
	op(EAURACRONEnvironmentType::RadiantPlains) \
	op(EAURACRONEnvironmentType::ZephyrFirmament) \
	op(EAURACRONEnvironmentType::PurgatoryRealm) 

enum class EAURACRONEnvironmentType : uint8;
template<> struct TIsUEnumClass<EAURACRONEnvironmentType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnvironmentType>();
// ********** End Enum EAURACRONEnvironmentType ****************************************************

// ********** Begin Enum EAURACRONObjectiveType ****************************************************
#define FOREACH_ENUM_EAURACRONOBJECTIVETYPE(op) \
	op(EAURACRONObjectiveType::None) \
	op(EAURACRONObjectiveType::FragmentAuracron) \
	op(EAURACRONObjectiveType::NexusFragment) \
	op(EAURACRONObjectiveType::TemporalRift) \
	op(EAURACRONObjectiveType::EnvironmentAnchor) \
	op(EAURACRONObjectiveType::MapAnchor) \
	op(EAURACRONObjectiveType::FusionCatalyst) \
	op(EAURACRONObjectiveType::TransitionPortal) \
	op(EAURACRONObjectiveType::PrismalGuardian) \
	op(EAURACRONObjectiveType::PrismalNexus) \
	op(EAURACRONObjectiveType::StormCore) \
	op(EAURACRONObjectiveType::SpectralGuardian) \
	op(EAURACRONObjectiveType::UmbraticLeviathan) 

enum class EAURACRONObjectiveType : uint8;
template<> struct TIsUEnumClass<EAURACRONObjectiveType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveType>();
// ********** End Enum EAURACRONObjectiveType ******************************************************

// ********** Begin Enum EAURACRONBuffType *********************************************************
#define FOREACH_ENUM_EAURACRONBUFFTYPE(op) \
	op(EAURACRONBuffType::MovementSpeed) \
	op(EAURACRONBuffType::DamageBoost) \
	op(EAURACRONBuffType::DefenseBoost) \
	op(EAURACRONBuffType::CooldownReduction) \
	op(EAURACRONBuffType::HealthRegeneration) \
	op(EAURACRONBuffType::ManaRegeneration) \
	op(EAURACRONBuffType::CriticalChance) \
	op(EAURACRONBuffType::AttackSpeed) \
	op(EAURACRONBuffType::SpellPower) \
	op(EAURACRONBuffType::Armor) \
	op(EAURACRONBuffType::MagicResistance) \
	op(EAURACRONBuffType::Lifesteal) \
	op(EAURACRONBuffType::Tenacity) 

enum class EAURACRONBuffType : uint8;
template<> struct TIsUEnumClass<EAURACRONBuffType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONBuffType>();
// ********** End Enum EAURACRONBuffType ***********************************************************

// ********** Begin Enum EAURACRONTemporalEffectType ***********************************************
#define FOREACH_ENUM_EAURACRONTEMPORALEFFECTTYPE(op) \
	op(EAURACRONTemporalEffectType::None) \
	op(EAURACRONTemporalEffectType::Rewind) \
	op(EAURACRONTemporalEffectType::Slow) \
	op(EAURACRONTemporalEffectType::Accelerate) \
	op(EAURACRONTemporalEffectType::Freeze) \
	op(EAURACRONTemporalEffectType::Loop) \
	op(EAURACRONTemporalEffectType::TimeAcceleration) \
	op(EAURACRONTemporalEffectType::TimeDeceleration) \
	op(EAURACRONTemporalEffectType::ChronoShield) \
	op(EAURACRONTemporalEffectType::TemporalEcho) 

enum class EAURACRONTemporalEffectType : uint8;
template<> struct TIsUEnumClass<EAURACRONTemporalEffectType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONTemporalEffectType>();
// ********** End Enum EAURACRONTemporalEffectType *************************************************

// ********** Begin Enum EAURACRONTrailType ********************************************************
#define FOREACH_ENUM_EAURACRONTRAILTYPE(op) \
	op(EAURACRONTrailType::None) \
	op(EAURACRONTrailType::Solar) \
	op(EAURACRONTrailType::Axis) \
	op(EAURACRONTrailType::Lunar) \
	op(EAURACRONTrailType::PrismalFlow) \
	op(EAURACRONTrailType::EtherealPath) \
	op(EAURACRONTrailType::NexusConnection) 

enum class EAURACRONTrailType : uint8;
template<> struct TIsUEnumClass<EAURACRONTrailType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONTrailType>();
// ********** End Enum EAURACRONTrailType **********************************************************

// ********** Begin Enum EAURACRONHardwareQuality **************************************************
#define FOREACH_ENUM_EAURACRONHARDWAREQUALITY(op) \
	op(EAURACRONHardwareQuality::Entry) \
	op(EAURACRONHardwareQuality::MidRange) \
	op(EAURACRONHardwareQuality::HighEnd) 

enum class EAURACRONHardwareQuality : uint8;
template<> struct TIsUEnumClass<EAURACRONHardwareQuality> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONHardwareQuality>();
// ********** End Enum EAURACRONHardwareQuality ****************************************************

// ********** Begin Enum EAURACRONSigilType ********************************************************
#define FOREACH_ENUM_EAURACRONSIGILTYPE(op) \
	op(EAURACRONSigilType::Aegis) \
	op(EAURACRONSigilType::Ruin) \
	op(EAURACRONSigilType::Vesper) 

enum class EAURACRONSigilType : uint8;
template<> struct TIsUEnumClass<EAURACRONSigilType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONSigilType>();
// ********** End Enum EAURACRONSigilType **********************************************************

// ********** Begin Enum EAURACRONNetworkState *****************************************************
#define FOREACH_ENUM_EAURACRONNETWORKSTATE(op) \
	op(EAURACRONNetworkState::Disconnected) \
	op(EAURACRONNetworkState::Connecting) \
	op(EAURACRONNetworkState::Connected) \
	op(EAURACRONNetworkState::Synchronizing) \
	op(EAURACRONNetworkState::Ready) \
	op(EAURACRONNetworkState::InGame) \
	op(EAURACRONNetworkState::Reconnecting) \
	op(EAURACRONNetworkState::Error) 

enum class EAURACRONNetworkState : uint8;
template<> struct TIsUEnumClass<EAURACRONNetworkState> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONNetworkState>();
// ********** End Enum EAURACRONNetworkState *******************************************************

// ********** Begin Enum EAURACRONObjectiveCategory ************************************************
#define FOREACH_ENUM_EAURACRONOBJECTIVECATEGORY(op) \
	op(EAURACRONObjectiveCategory::Core) \
	op(EAURACRONObjectiveCategory::CatchUp) \
	op(EAURACRONObjectiveCategory::Bonus) \
	op(EAURACRONObjectiveCategory::Event) 

enum class EAURACRONObjectiveCategory : uint8;
template<> struct TIsUEnumClass<EAURACRONObjectiveCategory> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveCategory>();
// ********** End Enum EAURACRONObjectiveCategory **************************************************

// ********** Begin Enum EAURACRONObjectiveState ***************************************************
#define FOREACH_ENUM_EAURACRONOBJECTIVESTATE(op) \
	op(EAURACRONObjectiveState::Inactive) \
	op(EAURACRONObjectiveState::Active) \
	op(EAURACRONObjectiveState::InProgress) \
	op(EAURACRONObjectiveState::Captured) \
	op(EAURACRONObjectiveState::Completed) \
	op(EAURACRONObjectiveState::Expired) 

enum class EAURACRONObjectiveState : uint8;
template<> struct TIsUEnumClass<EAURACRONObjectiveState> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveState>();
// ********** End Enum EAURACRONObjectiveState *****************************************************

// ********** Begin Enum EAURACRONEnergyType *******************************************************
#define FOREACH_ENUM_EAURACRONENERGYTYPE(op) \
	op(EAURACRONEnergyType::Golden) \
	op(EAURACRONEnergyType::Silver) \
	op(EAURACRONEnergyType::Violet) \
	op(EAURACRONEnergyType::Solar) \
	op(EAURACRONEnergyType::Lunar) \
	op(EAURACRONEnergyType::Prismal) \
	op(EAURACRONEnergyType::Chaos) \
	op(EAURACRONEnergyType::Void) 

enum class EAURACRONEnergyType : uint8;
template<> struct TIsUEnumClass<EAURACRONEnergyType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnergyType>();
// ********** End Enum EAURACRONEnergyType *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
