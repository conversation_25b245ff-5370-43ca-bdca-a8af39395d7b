{"RemapUnityFiles": {"Module.AURACRON.1.cpp.obj": ["AURACRON.init.gen.cpp.obj", "AURACRONAttributeSet.gen.cpp.obj", "AURACRONCharacter.gen.cpp.obj", "AURACRONEnums.gen.cpp.obj", "AURACRONMapMeasurements.gen.cpp.obj", "AURACRONMovementComponent.gen.cpp.obj"], "Module.AURACRON.2.cpp.obj": ["AURACRONPCGArsenalIsland.gen.cpp.obj", "AURACRONPCGChaosIsland.gen.cpp.obj", "AURACRONPCGChaosIslandManager.gen.cpp.obj", "AURACRONPCGChaosPortal.gen.cpp.obj", "AURACRONPCGEnergyPulse.gen.cpp.obj", "AURACRONPCGEnvironment.gen.cpp.obj"], "Module.AURACRON.3.cpp.obj": ["AURACRONPCGEnvironmentManager.gen.cpp.obj", "AURACRONPCGIsland.gen.cpp.obj", "AURACRONPCGJungleSystem.gen.cpp.obj"], "Module.AURACRON.4.cpp.obj": ["AURACRONPCGLaneSystem.gen.cpp.obj", "AURACRONPCGMathLibrary.gen.cpp.obj", "AURACRONPCGNexusIsland.gen.cpp.obj", "AURACRONPCGObjectiveSystem.gen.cpp.obj"], "Module.AURACRON.5.cpp.obj": ["AURACRONPCGPerformanceManager.gen.cpp.obj", "AURACRONPCGPhaseManager.gen.cpp.obj", "AURACRONPCGPortal.gen.cpp.obj"], "Module.AURACRON.6.cpp.obj": ["AURACRONPCGPrismalFlow.gen.cpp.obj", "AURACRONPCGSanctuaryIsland.gen.cpp.obj", "AURACRONPCGSubsystem.gen.cpp.obj", "AURACRONPCGTrail.gen.cpp.obj"], "Module.AURACRON.7.cpp.obj": ["AURACRONPCGTypes.gen.cpp.obj", "AURACRONPCGUtility.gen.cpp.obj", "AURACRONPCGWorldPartitionIntegration.gen.cpp.obj", "AURACRONSigilComponent.gen.cpp.obj", "AURACRONStructs.gen.cpp.obj"], "Module.AURACRON.8.cpp.obj": ["DamageZoneComponent.gen.cpp.obj", "SigilAbilities.gen.cpp.obj", "SigilAbilityEffects.gen.cpp.obj", "SigilAttributeSet.gen.cpp.obj"], "Module.AURACRON.9.cpp.obj": ["SigilDebugCommands.gen.cpp.obj", "SigilFusionSystem.gen.cpp.obj", "SigilGameplayEffects.gen.cpp.obj"], "Module.AURACRON.10.cpp.obj": ["SigilItem.gen.cpp.obj", "SigilManagerComponent.gen.cpp.obj"], "Module.AURACRON.11.cpp.obj": ["SigilNetworkConfig.gen.cpp.obj", "SigilReplicationManager.gen.cpp.obj", "SigilVFXManager.gen.cpp.obj"], "Module.AURACRON.12.cpp.obj": ["SigilWidgets.gen.cpp.obj", "AURACRON.cpp.obj", "AURACRONCharacter.cpp.obj", "AURACRONMovementComponent.cpp.obj", "AURACRONSigilComponent.cpp.obj", "DamageZoneComponent.cpp.obj", "SigilDebugCommands.cpp.obj"]}}