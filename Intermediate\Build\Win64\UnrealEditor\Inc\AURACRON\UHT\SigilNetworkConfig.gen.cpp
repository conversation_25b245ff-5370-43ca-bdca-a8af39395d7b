// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Multiplayer/SigilNetworkConfig.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilNetworkConfig() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilNetworkConfig();
AURACRON_API UClass* Z_Construct_UClass_USigilNetworkConfig_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilBandwidthConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilDistanceConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilFrequencyConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilNetworkStats();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilReplicationPriority();
DEVELOPERSETTINGS_API UClass* Z_Construct_UClass_UDeveloperSettings();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ESigilNetworkOptimization *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilNetworkOptimization;
static UEnum* ESigilNetworkOptimization_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilNetworkOptimization.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilNetworkOptimization.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilNetworkOptimization"));
	}
	return Z_Registration_Info_UEnum_ESigilNetworkOptimization.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilNetworkOptimization>()
{
	return ESigilNetworkOptimization_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Basic.DisplayName", "Basic Optimization" },
		{ "Basic.Name", "ESigilNetworkOptimization::Basic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum para tipos de otimiza\xc3\xa7\xc3\xa3o de rede\n" },
#endif
		{ "Competitive.DisplayName", "Competitive Mode" },
		{ "Competitive.Name", "ESigilNetworkOptimization::Competitive" },
		{ "Custom.DisplayName", "Custom Settings" },
		{ "Custom.Name", "ESigilNetworkOptimization::Custom" },
		{ "MOBA.DisplayName", "MOBA Optimized" },
		{ "MOBA.Name", "ESigilNetworkOptimization::MOBA" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
		{ "None.DisplayName", "No Optimization" },
		{ "None.Name", "ESigilNetworkOptimization::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum para tipos de otimiza\xc3\xa7\xc3\xa3o de rede" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilNetworkOptimization::None", (int64)ESigilNetworkOptimization::None },
		{ "ESigilNetworkOptimization::Basic", (int64)ESigilNetworkOptimization::Basic },
		{ "ESigilNetworkOptimization::MOBA", (int64)ESigilNetworkOptimization::MOBA },
		{ "ESigilNetworkOptimization::Competitive", (int64)ESigilNetworkOptimization::Competitive },
		{ "ESigilNetworkOptimization::Custom", (int64)ESigilNetworkOptimization::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilNetworkOptimization",
	"ESigilNetworkOptimization",
	Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization()
{
	if (!Z_Registration_Info_UEnum_ESigilNetworkOptimization.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilNetworkOptimization.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilNetworkOptimization.InnerSingleton;
}
// ********** End Enum ESigilNetworkOptimization ***************************************************

// ********** Begin ScriptStruct FSigilReplicationPriority *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilReplicationPriority;
class UScriptStruct* FSigilReplicationPriority::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilReplicationPriority.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilReplicationPriority.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilReplicationPriority, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilReplicationPriority"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilReplicationPriority.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para configura\xc3\xa7\xc3\xb5""es de prioridade de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xb5""es de prioridade de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedSigilPriority_MetaData[] = {
		{ "Category", "SigilReplicationPriority" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Prioridade para s\xc3\xadgilos equipados (0.0 - 10.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade para s\xc3\xadgilos equipados (0.0 - 10.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionPriority_MetaData[] = {
		{ "Category", "SigilReplicationPriority" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Prioridade para fus\xc3\xb5""es ativas (0.0 - 10.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade para fus\xc3\xb5""es ativas (0.0 - 10.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SystemStatsPriority_MetaData[] = {
		{ "Category", "SigilReplicationPriority" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Prioridade para estat\xc3\xadsticas do sistema (0.0 - 10.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade para estat\xc3\xadsticas do sistema (0.0 - 10.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXPriority_MetaData[] = {
		{ "Category", "SigilReplicationPriority" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Prioridade para efeitos visuais (0.0 - 10.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade para efeitos visuais (0.0 - 10.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeammatePriority_MetaData[] = {
		{ "Category", "SigilReplicationPriority" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Prioridade para jogadores da mesma equipe (0.0 - 10.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade para jogadores da mesma equipe (0.0 - 10.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemyPriority_MetaData[] = {
		{ "Category", "SigilReplicationPriority" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Prioridade para jogadores inimigos (0.0 - 10.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade para jogadores inimigos (0.0 - 10.0)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EquippedSigilPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SystemStatsPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VFXPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TeammatePriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnemyPriority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilReplicationPriority>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_EquippedSigilPriority = { "EquippedSigilPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationPriority, EquippedSigilPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedSigilPriority_MetaData), NewProp_EquippedSigilPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_FusionPriority = { "FusionPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationPriority, FusionPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionPriority_MetaData), NewProp_FusionPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_SystemStatsPriority = { "SystemStatsPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationPriority, SystemStatsPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SystemStatsPriority_MetaData), NewProp_SystemStatsPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_VFXPriority = { "VFXPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationPriority, VFXPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXPriority_MetaData), NewProp_VFXPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_TeammatePriority = { "TeammatePriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationPriority, TeammatePriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeammatePriority_MetaData), NewProp_TeammatePriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_EnemyPriority = { "EnemyPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationPriority, EnemyPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemyPriority_MetaData), NewProp_EnemyPriority_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_EquippedSigilPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_FusionPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_SystemStatsPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_VFXPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_TeammatePriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewProp_EnemyPriority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilReplicationPriority",
	Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::PropPointers),
	sizeof(FSigilReplicationPriority),
	alignof(FSigilReplicationPriority),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilReplicationPriority()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilReplicationPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilReplicationPriority.InnerSingleton, Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilReplicationPriority.InnerSingleton;
}
// ********** End ScriptStruct FSigilReplicationPriority *******************************************

// ********** Begin ScriptStruct FSigilDistanceConfig **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilDistanceConfig;
class UScriptStruct* FSigilDistanceConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilDistanceConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilDistanceConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilDistanceConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilDistanceConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilDistanceConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para configura\xc3\xa7\xc3\xb5""es de dist\xc3\xa2ncia\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xb5""es de dist\xc3\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxReplicationDistance_MetaData[] = {
		{ "Category", "SigilDistanceConfig" },
		{ "ClampMax", "20000.0" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dist\xc3\xa2ncia m\xc3\xa1xima para replica\xc3\xa7\xc3\xa3o completa (Unreal Units)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia m\xc3\xa1xima para replica\xc3\xa7\xc3\xa3o completa (Unreal Units)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MediumPriorityDistance_MetaData[] = {
		{ "Category", "SigilDistanceConfig" },
		{ "ClampMax", "15000.0" },
		{ "ClampMin", "500.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dist\xc3\xa2ncia para replica\xc3\xa7\xc3\xa3o de prioridade m\xc3\xa9""dia (Unreal Units)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia para replica\xc3\xa7\xc3\xa3o de prioridade m\xc3\xa9""dia (Unreal Units)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LowPriorityDistance_MetaData[] = {
		{ "Category", "SigilDistanceConfig" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dist\xc3\xa2ncia para replica\xc3\xa7\xc3\xa3o de baixa prioridade (Unreal Units)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia para replica\xc3\xa7\xc3\xa3o de baixa prioridade (Unreal Units)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "SigilDistanceConfig" },
		{ "ClampMax", "50000.0" },
		{ "ClampMin", "5000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dist\xc3\xa2ncia para culling completo (Unreal Units)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia para culling completo (Unreal Units)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxReplicationDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MediumPriorityDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LowPriorityDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilDistanceConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::NewProp_MaxReplicationDistance = { "MaxReplicationDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilDistanceConfig, MaxReplicationDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxReplicationDistance_MetaData), NewProp_MaxReplicationDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::NewProp_MediumPriorityDistance = { "MediumPriorityDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilDistanceConfig, MediumPriorityDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MediumPriorityDistance_MetaData), NewProp_MediumPriorityDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::NewProp_LowPriorityDistance = { "LowPriorityDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilDistanceConfig, LowPriorityDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LowPriorityDistance_MetaData), NewProp_LowPriorityDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilDistanceConfig, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::NewProp_MaxReplicationDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::NewProp_MediumPriorityDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::NewProp_LowPriorityDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::NewProp_CullingDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilDistanceConfig",
	Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::PropPointers),
	sizeof(FSigilDistanceConfig),
	alignof(FSigilDistanceConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilDistanceConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilDistanceConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilDistanceConfig.InnerSingleton, Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilDistanceConfig.InnerSingleton;
}
// ********** End ScriptStruct FSigilDistanceConfig ************************************************

// ********** Begin ScriptStruct FSigilFrequencyConfig *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilFrequencyConfig;
class UScriptStruct* FSigilFrequencyConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFrequencyConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilFrequencyConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilFrequencyConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilFrequencyConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilFrequencyConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para configura\xc3\xa7\xc3\xb5""es de frequ\xc3\xaancia\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xb5""es de frequ\xc3\xaancia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseReplicationFrequency_MetaData[] = {
		{ "Category", "SigilFrequencyConfig" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Frequ\xc3\xaancia base de replica\xc3\xa7\xc3\xa3o (Hz)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\xaancia base de replica\xc3\xa7\xc3\xa3o (Hz)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalDataFrequency_MetaData[] = {
		{ "Category", "SigilFrequencyConfig" },
		{ "ClampMax", "120.0" },
		{ "ClampMin", "5.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Frequ\xc3\xaancia para dados cr\xc3\xadticos (Hz)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\xaancia para dados cr\xc3\xadticos (Hz)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LowPriorityFrequency_MetaData[] = {
		{ "Category", "SigilFrequencyConfig" },
		{ "ClampMax", "30.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Frequ\xc3\xaancia para dados de baixa prioridade (Hz)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\xaancia para dados de baixa prioridade (Hz)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationInterval_MetaData[] = {
		{ "Category", "SigilFrequencyConfig" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intervalo de otimiza\xc3\xa7\xc3\xa3o autom\xc3\xa1tica (segundos)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de otimiza\xc3\xa7\xc3\xa3o autom\xc3\xa1tica (segundos)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseReplicationFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CriticalDataFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LowPriorityFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OptimizationInterval;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilFrequencyConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::NewProp_BaseReplicationFrequency = { "BaseReplicationFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFrequencyConfig, BaseReplicationFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseReplicationFrequency_MetaData), NewProp_BaseReplicationFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::NewProp_CriticalDataFrequency = { "CriticalDataFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFrequencyConfig, CriticalDataFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalDataFrequency_MetaData), NewProp_CriticalDataFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::NewProp_LowPriorityFrequency = { "LowPriorityFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFrequencyConfig, LowPriorityFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LowPriorityFrequency_MetaData), NewProp_LowPriorityFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::NewProp_OptimizationInterval = { "OptimizationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFrequencyConfig, OptimizationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationInterval_MetaData), NewProp_OptimizationInterval_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::NewProp_BaseReplicationFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::NewProp_CriticalDataFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::NewProp_LowPriorityFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::NewProp_OptimizationInterval,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilFrequencyConfig",
	Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::PropPointers),
	sizeof(FSigilFrequencyConfig),
	alignof(FSigilFrequencyConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilFrequencyConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFrequencyConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilFrequencyConfig.InnerSingleton, Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilFrequencyConfig.InnerSingleton;
}
// ********** End ScriptStruct FSigilFrequencyConfig ***********************************************

// ********** Begin ScriptStruct FSigilBandwidthConfig *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilBandwidthConfig;
class UScriptStruct* FSigilBandwidthConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilBandwidthConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilBandwidthConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilBandwidthConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilBandwidthConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilBandwidthConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para configura\xc3\xa7\xc3\xb5""es de bandwidth\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xb5""es de bandwidth" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBandwidthPerPlayer_MetaData[] = {
		{ "Category", "SigilBandwidthConfig" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Limite m\xc3\xa1ximo de bandwidth por jogador (KB/s)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limite m\xc3\xa1ximo de bandwidth por jogador (KB/s)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxReplicationsPerFrame_MetaData[] = {
		{ "Category", "SigilBandwidthConfig" },
		{ "ClampMax", "200" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Limite de replica\xc3\xa7\xc3\xb5""es por frame\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limite de replica\xc3\xa7\xc3\xb5""es por frame" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPacketSize_MetaData[] = {
		{ "Category", "SigilBandwidthConfig" },
		{ "ClampMax", "8192" },
		{ "ClampMin", "512" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tamanho m\xc3\xa1ximo de pacote (bytes)\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho m\xc3\xa1ximo de pacote (bytes)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCompression_MetaData[] = {
		{ "Category", "SigilBandwidthConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Usar compress\xc3\xa3o de dados\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar compress\xc3\xa3o de dados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDeltaCompression_MetaData[] = {
		{ "Category", "SigilBandwidthConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Usar delta compression\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar delta compression" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxBandwidthPerPlayer;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxReplicationsPerFrame;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPacketSize;
	static void NewProp_bUseCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCompression;
	static void NewProp_bUseDeltaCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDeltaCompression;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilBandwidthConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_MaxBandwidthPerPlayer = { "MaxBandwidthPerPlayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilBandwidthConfig, MaxBandwidthPerPlayer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBandwidthPerPlayer_MetaData), NewProp_MaxBandwidthPerPlayer_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_MaxReplicationsPerFrame = { "MaxReplicationsPerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilBandwidthConfig, MaxReplicationsPerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxReplicationsPerFrame_MetaData), NewProp_MaxReplicationsPerFrame_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_MaxPacketSize = { "MaxPacketSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilBandwidthConfig, MaxPacketSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPacketSize_MetaData), NewProp_MaxPacketSize_MetaData) };
void Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_bUseCompression_SetBit(void* Obj)
{
	((FSigilBandwidthConfig*)Obj)->bUseCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_bUseCompression = { "bUseCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilBandwidthConfig), &Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_bUseCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCompression_MetaData), NewProp_bUseCompression_MetaData) };
void Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_bUseDeltaCompression_SetBit(void* Obj)
{
	((FSigilBandwidthConfig*)Obj)->bUseDeltaCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_bUseDeltaCompression = { "bUseDeltaCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilBandwidthConfig), &Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_bUseDeltaCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDeltaCompression_MetaData), NewProp_bUseDeltaCompression_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_MaxBandwidthPerPlayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_MaxReplicationsPerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_MaxPacketSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_bUseCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewProp_bUseDeltaCompression,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilBandwidthConfig",
	Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::PropPointers),
	sizeof(FSigilBandwidthConfig),
	alignof(FSigilBandwidthConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilBandwidthConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilBandwidthConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilBandwidthConfig.InnerSingleton, Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilBandwidthConfig.InnerSingleton;
}
// ********** End ScriptStruct FSigilBandwidthConfig ***********************************************

// ********** Begin Class USigilNetworkConfig Function ApplyOptimizationType ***********************
struct Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics
{
	struct SigilNetworkConfig_eventApplyOptimizationType_Parms
	{
		ESigilNetworkOptimization NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de configura\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de configura\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventApplyOptimizationType_Parms, NewType), Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization, METADATA_PARAMS(0, nullptr) }; // 3974440704
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "ApplyOptimizationType", Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::SigilNetworkConfig_eventApplyOptimizationType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::SigilNetworkConfig_eventApplyOptimizationType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execApplyOptimizationType)
{
	P_GET_ENUM(ESigilNetworkOptimization,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyOptimizationType(ESigilNetworkOptimization(Z_Param_NewType));
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function ApplyOptimizationType *************************

// ********** Begin Class USigilNetworkConfig Function GetConfigurationWarnings ********************
struct Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics
{
	struct SigilNetworkConfig_eventGetConfigurationWarnings_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventGetConfigurationWarnings_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "GetConfigurationWarnings", Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::SigilNetworkConfig_eventGetConfigurationWarnings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::SigilNetworkConfig_eventGetConfigurationWarnings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execGetConfigurationWarnings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetConfigurationWarnings();
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function GetConfigurationWarnings **********************

// ********** Begin Class USigilNetworkConfig Function GetDistanceMultiplier ***********************
struct Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics
{
	struct SigilNetworkConfig_eventGetDistanceMultiplier_Parms
	{
		float Distance;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventGetDistanceMultiplier_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventGetDistanceMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "GetDistanceMultiplier", Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::SigilNetworkConfig_eventGetDistanceMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::SigilNetworkConfig_eventGetDistanceMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execGetDistanceMultiplier)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetDistanceMultiplier(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function GetDistanceMultiplier *************************

// ********** Begin Class USigilNetworkConfig Function GetEstimatedBandwidthUsage ******************
struct Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics
{
	struct SigilNetworkConfig_eventGetEstimatedBandwidthUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estat\xc3\xadsticas\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\xadsticas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventGetEstimatedBandwidthUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "GetEstimatedBandwidthUsage", Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::SigilNetworkConfig_eventGetEstimatedBandwidthUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::SigilNetworkConfig_eventGetEstimatedBandwidthUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execGetEstimatedBandwidthUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetEstimatedBandwidthUsage();
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function GetEstimatedBandwidthUsage ********************

// ********** Begin Class USigilNetworkConfig Function GetEstimatedReplicationsPerSecond ***********
struct Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics
{
	struct SigilNetworkConfig_eventGetEstimatedReplicationsPerSecond_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventGetEstimatedReplicationsPerSecond_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "GetEstimatedReplicationsPerSecond", Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::SigilNetworkConfig_eventGetEstimatedReplicationsPerSecond_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::SigilNetworkConfig_eventGetEstimatedReplicationsPerSecond_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execGetEstimatedReplicationsPerSecond)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetEstimatedReplicationsPerSecond();
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function GetEstimatedReplicationsPerSecond *************

// ********** Begin Class USigilNetworkConfig Function GetFrequencyForPriority *********************
struct Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics
{
	struct SigilNetworkConfig_eventGetFrequencyForPriority_Parms
	{
		float Priority;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventGetFrequencyForPriority_Parms, Priority), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventGetFrequencyForPriority_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "GetFrequencyForPriority", Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::SigilNetworkConfig_eventGetFrequencyForPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::SigilNetworkConfig_eventGetFrequencyForPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execGetFrequencyForPriority)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFrequencyForPriority(Z_Param_Priority);
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function GetFrequencyForPriority ***********************

// ********** Begin Class USigilNetworkConfig Function GetPriorityForTag ***************************
struct Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics
{
	struct SigilNetworkConfig_eventGetPriorityForTag_Parms
	{
		FGameplayTag Tag;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de consulta\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de consulta" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventGetPriorityForTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventGetPriorityForTag_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "GetPriorityForTag", Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::SigilNetworkConfig_eventGetPriorityForTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::SigilNetworkConfig_eventGetPriorityForTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execGetPriorityForTag)
{
	P_GET_STRUCT(FGameplayTag,Z_Param_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPriorityForTag(Z_Param_Tag);
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function GetPriorityForTag *****************************

// ********** Begin Class USigilNetworkConfig Function GetSigilNetworkConfig ***********************
struct Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics
{
	struct SigilNetworkConfig_eventGetSigilNetworkConfig_Parms
	{
		USigilNetworkConfig* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventGetSigilNetworkConfig_Parms, ReturnValue), Z_Construct_UClass_USigilNetworkConfig_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "GetSigilNetworkConfig", Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::SigilNetworkConfig_eventGetSigilNetworkConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::SigilNetworkConfig_eventGetSigilNetworkConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execGetSigilNetworkConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(USigilNetworkConfig**)Z_Param__Result=USigilNetworkConfig::GetSigilNetworkConfig();
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function GetSigilNetworkConfig *************************

// ********** Begin Class USigilNetworkConfig Function ResetToDefaults *****************************
struct Z_Construct_UFunction_USigilNetworkConfig_ResetToDefaults_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_ResetToDefaults_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "ResetToDefaults", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_ResetToDefaults_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_ResetToDefaults_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilNetworkConfig_ResetToDefaults()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_ResetToDefaults_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execResetToDefaults)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetToDefaults();
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function ResetToDefaults *******************************

// ********** Begin Class USigilNetworkConfig Function SetMaxPlayers *******************************
struct Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics
{
	struct SigilNetworkConfig_eventSetMaxPlayers_Parms
	{
		int32 NewMaxPlayers;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewMaxPlayers;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::NewProp_NewMaxPlayers = { "NewMaxPlayers", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventSetMaxPlayers_Parms, NewMaxPlayers), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::NewProp_NewMaxPlayers,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "SetMaxPlayers", Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::SigilNetworkConfig_eventSetMaxPlayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::SigilNetworkConfig_eventSetMaxPlayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execSetMaxPlayers)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewMaxPlayers);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMaxPlayers(Z_Param_NewMaxPlayers);
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function SetMaxPlayers *********************************

// ********** Begin Class USigilNetworkConfig Function SetMOBAOptimizations ************************
struct Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics
{
	struct SigilNetworkConfig_eventSetMOBAOptimizations_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((SigilNetworkConfig_eventSetMOBAOptimizations_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilNetworkConfig_eventSetMOBAOptimizations_Parms), &Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "SetMOBAOptimizations", Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::SigilNetworkConfig_eventSetMOBAOptimizations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::SigilNetworkConfig_eventSetMOBAOptimizations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execSetMOBAOptimizations)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMOBAOptimizations(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function SetMOBAOptimizations **************************

// ********** Begin Class USigilNetworkConfig Function ShouldReplicateAtDistance *******************
struct Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics
{
	struct SigilNetworkConfig_eventShouldReplicateAtDistance_Parms
	{
		float Distance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNetworkConfig_eventShouldReplicateAtDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilNetworkConfig_eventShouldReplicateAtDistance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilNetworkConfig_eventShouldReplicateAtDistance_Parms), &Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "ShouldReplicateAtDistance", Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::SigilNetworkConfig_eventShouldReplicateAtDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::SigilNetworkConfig_eventShouldReplicateAtDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execShouldReplicateAtDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldReplicateAtDistance(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function ShouldReplicateAtDistance *********************

// ********** Begin Class USigilNetworkConfig Function ValidateConfiguration ***********************
struct Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics
{
	struct SigilNetworkConfig_eventValidateConfiguration_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Valida\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilNetworkConfig_eventValidateConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilNetworkConfig_eventValidateConfiguration_Parms), &Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNetworkConfig, nullptr, "ValidateConfiguration", Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::SigilNetworkConfig_eventValidateConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::SigilNetworkConfig_eventValidateConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNetworkConfig::execValidateConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateConfiguration();
	P_NATIVE_END;
}
// ********** End Class USigilNetworkConfig Function ValidateConfiguration *************************

// ********** Begin Class USigilNetworkConfig ******************************************************
void USigilNetworkConfig::StaticRegisterNativesUSigilNetworkConfig()
{
	UClass* Class = USigilNetworkConfig::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyOptimizationType", &USigilNetworkConfig::execApplyOptimizationType },
		{ "GetConfigurationWarnings", &USigilNetworkConfig::execGetConfigurationWarnings },
		{ "GetDistanceMultiplier", &USigilNetworkConfig::execGetDistanceMultiplier },
		{ "GetEstimatedBandwidthUsage", &USigilNetworkConfig::execGetEstimatedBandwidthUsage },
		{ "GetEstimatedReplicationsPerSecond", &USigilNetworkConfig::execGetEstimatedReplicationsPerSecond },
		{ "GetFrequencyForPriority", &USigilNetworkConfig::execGetFrequencyForPriority },
		{ "GetPriorityForTag", &USigilNetworkConfig::execGetPriorityForTag },
		{ "GetSigilNetworkConfig", &USigilNetworkConfig::execGetSigilNetworkConfig },
		{ "ResetToDefaults", &USigilNetworkConfig::execResetToDefaults },
		{ "SetMaxPlayers", &USigilNetworkConfig::execSetMaxPlayers },
		{ "SetMOBAOptimizations", &USigilNetworkConfig::execSetMOBAOptimizations },
		{ "ShouldReplicateAtDistance", &USigilNetworkConfig::execShouldReplicateAtDistance },
		{ "ValidateConfiguration", &USigilNetworkConfig::execValidateConfiguration },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilNetworkConfig;
UClass* USigilNetworkConfig::GetPrivateStaticClass()
{
	using TClass = USigilNetworkConfig;
	if (!Z_Registration_Info_UClass_USigilNetworkConfig.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilNetworkConfig"),
			Z_Registration_Info_UClass_USigilNetworkConfig.InnerSingleton,
			StaticRegisterNativesUSigilNetworkConfig,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilNetworkConfig.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilNetworkConfig_NoRegister()
{
	return USigilNetworkConfig::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilNetworkConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es de rede para o sistema de s\xc3\xadgilos\n * Otimizado para ambiente MOBA 5x5 com 10 jogadores\n */" },
#endif
		{ "DisplayName", "Sigil Network Settings" },
		{ "IncludePath", "Multiplayer/SigilNetworkConfig.h" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de rede para o sistema de s\xc3\xadgilos\nOtimizado para ambiente MOBA 5x5 com 10 jogadores" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationType_MetaData[] = {
		{ "Category", "General" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es gerais\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es gerais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPlayers_MetaData[] = {
		{ "Category", "General" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNetworkOptimizations_MetaData[] = {
		{ "Category", "General" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "General" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReplicationPriorities_MetaData[] = {
		{ "Category", "Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de prioridade\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de prioridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceSettings_MetaData[] = {
		{ "Category", "Distance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de dist\xc3\xa2ncia\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de dist\xc3\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrequencySettings_MetaData[] = {
		{ "Category", "Frequency" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de frequ\xc3\xaancia\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de frequ\xc3\xaancia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BandwidthSettings_MetaData[] = {
		{ "Category", "Bandwidth" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de bandwidth\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de bandwidth" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPrioritizeTeammates_MetaData[] = {
		{ "Category", "MOBA" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas para MOBA\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas para MOBA" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReduceEnemyDetails_MetaData[] = {
		{ "Category", "MOBA" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeForObjectives_MetaData[] = {
		{ "Category", "MOBA" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveReplicationRadius_MetaData[] = {
		{ "Category", "MOBA" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighPriorityTags_MetaData[] = {
		{ "Category", "Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tags para otimiza\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags para otimiza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LowPriorityTags_MetaData[] = {
		{ "Category", "Tags" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalTags_MetaData[] = {
		{ "Category", "Tags" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OptimizationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OptimizationType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPlayers;
	static void NewProp_bEnableNetworkOptimizations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNetworkOptimizations;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReplicationPriorities;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DistanceSettings;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FrequencySettings;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BandwidthSettings;
	static void NewProp_bPrioritizeTeammates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPrioritizeTeammates;
	static void NewProp_bReduceEnemyDetails_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReduceEnemyDetails;
	static void NewProp_bOptimizeForObjectives_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeForObjectives;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveReplicationRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HighPriorityTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LowPriorityTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CriticalTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilNetworkConfig_ApplyOptimizationType, "ApplyOptimizationType" }, // 890073939
		{ &Z_Construct_UFunction_USigilNetworkConfig_GetConfigurationWarnings, "GetConfigurationWarnings" }, // 2517128719
		{ &Z_Construct_UFunction_USigilNetworkConfig_GetDistanceMultiplier, "GetDistanceMultiplier" }, // 717156879
		{ &Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedBandwidthUsage, "GetEstimatedBandwidthUsage" }, // 506682501
		{ &Z_Construct_UFunction_USigilNetworkConfig_GetEstimatedReplicationsPerSecond, "GetEstimatedReplicationsPerSecond" }, // 174748197
		{ &Z_Construct_UFunction_USigilNetworkConfig_GetFrequencyForPriority, "GetFrequencyForPriority" }, // 1049358626
		{ &Z_Construct_UFunction_USigilNetworkConfig_GetPriorityForTag, "GetPriorityForTag" }, // 1295343374
		{ &Z_Construct_UFunction_USigilNetworkConfig_GetSigilNetworkConfig, "GetSigilNetworkConfig" }, // 3681678641
		{ &Z_Construct_UFunction_USigilNetworkConfig_ResetToDefaults, "ResetToDefaults" }, // 1992020739
		{ &Z_Construct_UFunction_USigilNetworkConfig_SetMaxPlayers, "SetMaxPlayers" }, // 2655558350
		{ &Z_Construct_UFunction_USigilNetworkConfig_SetMOBAOptimizations, "SetMOBAOptimizations" }, // 3092916697
		{ &Z_Construct_UFunction_USigilNetworkConfig_ShouldReplicateAtDistance, "ShouldReplicateAtDistance" }, // 2634593703
		{ &Z_Construct_UFunction_USigilNetworkConfig_ValidateConfiguration, "ValidateConfiguration" }, // 4106944304
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilNetworkConfig>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_OptimizationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_OptimizationType = { "OptimizationType", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNetworkConfig, OptimizationType), Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationType_MetaData), NewProp_OptimizationType_MetaData) }; // 3974440704
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_MaxPlayers = { "MaxPlayers", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNetworkConfig, MaxPlayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPlayers_MetaData), NewProp_MaxPlayers_MetaData) };
void Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bEnableNetworkOptimizations_SetBit(void* Obj)
{
	((USigilNetworkConfig*)Obj)->bEnableNetworkOptimizations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bEnableNetworkOptimizations = { "bEnableNetworkOptimizations", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilNetworkConfig), &Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bEnableNetworkOptimizations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNetworkOptimizations_MetaData), NewProp_bEnableNetworkOptimizations_MetaData) };
void Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((USigilNetworkConfig*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilNetworkConfig), &Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_ReplicationPriorities = { "ReplicationPriorities", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNetworkConfig, ReplicationPriorities), Z_Construct_UScriptStruct_FSigilReplicationPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReplicationPriorities_MetaData), NewProp_ReplicationPriorities_MetaData) }; // 40167363
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_DistanceSettings = { "DistanceSettings", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNetworkConfig, DistanceSettings), Z_Construct_UScriptStruct_FSigilDistanceConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceSettings_MetaData), NewProp_DistanceSettings_MetaData) }; // 2481910771
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_FrequencySettings = { "FrequencySettings", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNetworkConfig, FrequencySettings), Z_Construct_UScriptStruct_FSigilFrequencyConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrequencySettings_MetaData), NewProp_FrequencySettings_MetaData) }; // 354549178
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_BandwidthSettings = { "BandwidthSettings", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNetworkConfig, BandwidthSettings), Z_Construct_UScriptStruct_FSigilBandwidthConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BandwidthSettings_MetaData), NewProp_BandwidthSettings_MetaData) }; // 3330406008
void Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bPrioritizeTeammates_SetBit(void* Obj)
{
	((USigilNetworkConfig*)Obj)->bPrioritizeTeammates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bPrioritizeTeammates = { "bPrioritizeTeammates", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilNetworkConfig), &Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bPrioritizeTeammates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPrioritizeTeammates_MetaData), NewProp_bPrioritizeTeammates_MetaData) };
void Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bReduceEnemyDetails_SetBit(void* Obj)
{
	((USigilNetworkConfig*)Obj)->bReduceEnemyDetails = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bReduceEnemyDetails = { "bReduceEnemyDetails", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilNetworkConfig), &Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bReduceEnemyDetails_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReduceEnemyDetails_MetaData), NewProp_bReduceEnemyDetails_MetaData) };
void Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bOptimizeForObjectives_SetBit(void* Obj)
{
	((USigilNetworkConfig*)Obj)->bOptimizeForObjectives = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bOptimizeForObjectives = { "bOptimizeForObjectives", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilNetworkConfig), &Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bOptimizeForObjectives_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeForObjectives_MetaData), NewProp_bOptimizeForObjectives_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_ObjectiveReplicationRadius = { "ObjectiveReplicationRadius", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNetworkConfig, ObjectiveReplicationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveReplicationRadius_MetaData), NewProp_ObjectiveReplicationRadius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_HighPriorityTags = { "HighPriorityTags", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNetworkConfig, HighPriorityTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighPriorityTags_MetaData), NewProp_HighPriorityTags_MetaData) }; // 2104890724
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_LowPriorityTags = { "LowPriorityTags", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNetworkConfig, LowPriorityTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LowPriorityTags_MetaData), NewProp_LowPriorityTags_MetaData) }; // 2104890724
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_CriticalTags = { "CriticalTags", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNetworkConfig, CriticalTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalTags_MetaData), NewProp_CriticalTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilNetworkConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_OptimizationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_OptimizationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_MaxPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bEnableNetworkOptimizations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_ReplicationPriorities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_DistanceSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_FrequencySettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_BandwidthSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bPrioritizeTeammates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bReduceEnemyDetails,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_bOptimizeForObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_ObjectiveReplicationRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_HighPriorityTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_LowPriorityTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNetworkConfig_Statics::NewProp_CriticalTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilNetworkConfig_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilNetworkConfig_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UDeveloperSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilNetworkConfig_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilNetworkConfig_Statics::ClassParams = {
	&USigilNetworkConfig::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilNetworkConfig_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilNetworkConfig_Statics::PropPointers),
	0,
	0x001000A6u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilNetworkConfig_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilNetworkConfig_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilNetworkConfig()
{
	if (!Z_Registration_Info_UClass_USigilNetworkConfig.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilNetworkConfig.OuterSingleton, Z_Construct_UClass_USigilNetworkConfig_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilNetworkConfig.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilNetworkConfig);
USigilNetworkConfig::~USigilNetworkConfig() {}
// ********** End Class USigilNetworkConfig ********************************************************

// ********** Begin ScriptStruct FSigilNetworkStats ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilNetworkStats;
class UScriptStruct* FSigilNetworkStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilNetworkStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilNetworkStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilNetworkStats, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilNetworkStats"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilNetworkStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilNetworkStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para estat\xc3\xadsticas de rede em tempo real\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para estat\xc3\xadsticas de rede em tempo real" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentBandwidthUsage_MetaData[] = {
		{ "Category", "SigilNetworkStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReplicationsPerSecond_MetaData[] = {
		{ "Category", "SigilNetworkStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveConnections_MetaData[] = {
		{ "Category", "SigilNetworkStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLatency_MetaData[] = {
		{ "Category", "SigilNetworkStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PacketLossPercentage_MetaData[] = {
		{ "Category", "SigilNetworkStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DroppedReplications_MetaData[] = {
		{ "Category", "SigilNetworkStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionRatio_MetaData[] = {
		{ "Category", "SigilNetworkStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentBandwidthUsage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReplicationsPerSecond;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveConnections;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLatency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PacketLossPercentage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DroppedReplications;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompressionRatio;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilNetworkStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_CurrentBandwidthUsage = { "CurrentBandwidthUsage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNetworkStats, CurrentBandwidthUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentBandwidthUsage_MetaData), NewProp_CurrentBandwidthUsage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_ReplicationsPerSecond = { "ReplicationsPerSecond", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNetworkStats, ReplicationsPerSecond), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReplicationsPerSecond_MetaData), NewProp_ReplicationsPerSecond_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_ActiveConnections = { "ActiveConnections", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNetworkStats, ActiveConnections), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveConnections_MetaData), NewProp_ActiveConnections_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_AverageLatency = { "AverageLatency", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNetworkStats, AverageLatency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLatency_MetaData), NewProp_AverageLatency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_PacketLossPercentage = { "PacketLossPercentage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNetworkStats, PacketLossPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PacketLossPercentage_MetaData), NewProp_PacketLossPercentage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_DroppedReplications = { "DroppedReplications", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNetworkStats, DroppedReplications), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DroppedReplications_MetaData), NewProp_DroppedReplications_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_CompressionRatio = { "CompressionRatio", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNetworkStats, CompressionRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionRatio_MetaData), NewProp_CompressionRatio_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_CurrentBandwidthUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_ReplicationsPerSecond,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_ActiveConnections,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_AverageLatency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_PacketLossPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_DroppedReplications,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewProp_CompressionRatio,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilNetworkStats",
	Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::PropPointers),
	sizeof(FSigilNetworkStats),
	alignof(FSigilNetworkStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilNetworkStats()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilNetworkStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilNetworkStats.InnerSingleton, Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilNetworkStats.InnerSingleton;
}
// ********** End ScriptStruct FSigilNetworkStats **************************************************

// ********** Begin Delegate FOnNetworkOptimizationChanged *****************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnNetworkOptimizationChanged_Parms
	{
		ESigilNetworkOptimization NewOptimization;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegados para eventos de rede\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegados para eventos de rede" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewOptimization_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewOptimization;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::NewProp_NewOptimization_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::NewProp_NewOptimization = { "NewOptimization", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnNetworkOptimizationChanged_Parms, NewOptimization), Z_Construct_UEnum_AURACRON_ESigilNetworkOptimization, METADATA_PARAMS(0, nullptr) }; // 3974440704
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::NewProp_NewOptimization_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::NewProp_NewOptimization,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnNetworkOptimizationChanged__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnNetworkOptimizationChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnNetworkOptimizationChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnNetworkOptimizationChanged_DelegateWrapper(const FMulticastScriptDelegate& OnNetworkOptimizationChanged, ESigilNetworkOptimization NewOptimization)
{
	struct _Script_AURACRON_eventOnNetworkOptimizationChanged_Parms
	{
		ESigilNetworkOptimization NewOptimization;
	};
	_Script_AURACRON_eventOnNetworkOptimizationChanged_Parms Parms;
	Parms.NewOptimization=NewOptimization;
	OnNetworkOptimizationChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnNetworkOptimizationChanged *******************************************

// ********** Begin Delegate FOnBandwidthLimitReached **********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnBandwidthLimitReached_Parms
	{
		float CurrentUsage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentUsage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::NewProp_CurrentUsage = { "CurrentUsage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnBandwidthLimitReached_Parms, CurrentUsage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::NewProp_CurrentUsage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnBandwidthLimitReached__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::_Script_AURACRON_eventOnBandwidthLimitReached_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::_Script_AURACRON_eventOnBandwidthLimitReached_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnBandwidthLimitReached_DelegateWrapper(const FMulticastScriptDelegate& OnBandwidthLimitReached, float CurrentUsage)
{
	struct _Script_AURACRON_eventOnBandwidthLimitReached_Parms
	{
		float CurrentUsage;
	};
	_Script_AURACRON_eventOnBandwidthLimitReached_Parms Parms;
	Parms.CurrentUsage=CurrentUsage;
	OnBandwidthLimitReached.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBandwidthLimitReached ************************************************

// ********** Begin Delegate FOnNetworkStatsUpdated ************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnNetworkStatsUpdated_Parms
	{
		FSigilNetworkStats Stats;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilNetworkConfig.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Stats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Stats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::NewProp_Stats = { "Stats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnNetworkStatsUpdated_Parms, Stats), Z_Construct_UScriptStruct_FSigilNetworkStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Stats_MetaData), NewProp_Stats_MetaData) }; // 577841767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::NewProp_Stats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnNetworkStatsUpdated__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::_Script_AURACRON_eventOnNetworkStatsUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::_Script_AURACRON_eventOnNetworkStatsUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnNetworkStatsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnNetworkStatsUpdated, FSigilNetworkStats const& Stats)
{
	struct _Script_AURACRON_eventOnNetworkStatsUpdated_Parms
	{
		FSigilNetworkStats Stats;
	};
	_Script_AURACRON_eventOnNetworkStatsUpdated_Parms Parms;
	Parms.Stats=Stats;
	OnNetworkStatsUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnNetworkStatsUpdated **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ESigilNetworkOptimization_StaticEnum, TEXT("ESigilNetworkOptimization"), &Z_Registration_Info_UEnum_ESigilNetworkOptimization, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3974440704U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSigilReplicationPriority::StaticStruct, Z_Construct_UScriptStruct_FSigilReplicationPriority_Statics::NewStructOps, TEXT("SigilReplicationPriority"), &Z_Registration_Info_UScriptStruct_FSigilReplicationPriority, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilReplicationPriority), 40167363U) },
		{ FSigilDistanceConfig::StaticStruct, Z_Construct_UScriptStruct_FSigilDistanceConfig_Statics::NewStructOps, TEXT("SigilDistanceConfig"), &Z_Registration_Info_UScriptStruct_FSigilDistanceConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilDistanceConfig), 2481910771U) },
		{ FSigilFrequencyConfig::StaticStruct, Z_Construct_UScriptStruct_FSigilFrequencyConfig_Statics::NewStructOps, TEXT("SigilFrequencyConfig"), &Z_Registration_Info_UScriptStruct_FSigilFrequencyConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilFrequencyConfig), 354549178U) },
		{ FSigilBandwidthConfig::StaticStruct, Z_Construct_UScriptStruct_FSigilBandwidthConfig_Statics::NewStructOps, TEXT("SigilBandwidthConfig"), &Z_Registration_Info_UScriptStruct_FSigilBandwidthConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilBandwidthConfig), 3330406008U) },
		{ FSigilNetworkStats::StaticStruct, Z_Construct_UScriptStruct_FSigilNetworkStats_Statics::NewStructOps, TEXT("SigilNetworkStats"), &Z_Registration_Info_UScriptStruct_FSigilNetworkStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilNetworkStats), 577841767U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilNetworkConfig, USigilNetworkConfig::StaticClass, TEXT("USigilNetworkConfig"), &Z_Registration_Info_UClass_USigilNetworkConfig, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilNetworkConfig), 3938786541U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h__Script_AURACRON_2428519778(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilNetworkConfig_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
