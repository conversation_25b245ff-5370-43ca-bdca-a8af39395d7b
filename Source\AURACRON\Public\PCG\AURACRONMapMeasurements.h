// AURACRONMapMeasurements.h
// Sistema de Medidas e Escalas para AURACRON - UE 5.6
// Definições precisas de dimensões baseadas em LoL/Dota 2 e conversões UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "AURACRONMapMeasurements.generated.h"

/**
 * Estrutura para definir as dimensões principais do mapa AURACRON
 *
 * ANÁLISE DETALHADA DOS CONCORRENTES:
 * - League of Legends (Summoner's Rift): 16,000 x 16,000 units = 160m x 160m
 * - Dota 2: ~11,000 x 11,000 units = 110m x 110m
 * - AURACRON: 15,000 x 15,000 cm = 150m x 150m (meio termo otimizado)
 *
 * UE 5.6: 1 unit = 1 centimeter (padrão oficial Unreal Engine)
 *
 * COORDENADAS DO MAPA:
 * - Centro: (0, 0, altura_ambiente)
 * - Limites: X[-7500, +7500], Y[-7500, +7500]
 * - Team 1 Base: (-6500, -6500, altura_ambiente)
 * - Team 2 Base: (+6500, +6500, altura_ambiente)
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONMapDimensions
{
    GENERATED_BODY()

    // ========================================
    // DIMENSÕES PRINCIPAIS DO MAPA (BASEADAS EM LOL/DOTA 2)
    // ========================================

    /** Raio total do mapa em centímetros (75 metros) */
    static constexpr float MAP_RADIUS_CM = 7500.0f;

    /** Dimensão total do mapa (150m x 150m) */
    static constexpr float MAP_SIZE_CM = 15000.0f;

    /** Centro do mapa */
    static const FVector MAP_CENTER;
    
    // ========================================
    // DIMENSÕES DAS LANES (LAYOUT EXATO DO SUMMONER'S RIFT)
    // ========================================

    /** Largura das lanes principais em centímetros */
    static constexpr float LANE_WIDTH_CM = 800.0f; // 8 metros

    /** Comprimento diagonal das lanes em centímetros */
    static constexpr float LANE_LENGTH_CM = 13000.0f; // 130 metros (diagonal)

    // COORDENADAS DAS LANES (baseadas no layout do LoL)

    /** Top Lane - diagonal superior esquerda para inferior direita */
    static constexpr float TOP_LANE_START_X = -6000.0f; // -60m
    static constexpr float TOP_LANE_START_Y = 6000.0f;  // +60m
    static constexpr float TOP_LANE_END_X = 6000.0f;    // +60m
    static constexpr float TOP_LANE_END_Y = -6000.0f;   // -60m

    /** Mid Lane - diagonal inferior esquerda para superior direita (através do centro) */
    static constexpr float MID_LANE_START_X = -7000.0f; // -70m
    static constexpr float MID_LANE_START_Y = -7000.0f; // -70m
    static constexpr float MID_LANE_END_X = 7000.0f;    // +70m
    static constexpr float MID_LANE_END_Y = 7000.0f;    // +70m

    /** Bot Lane - diagonal inferior direita para superior esquerda */
    static constexpr float BOT_LANE_START_X = 6000.0f;  // +60m
    static constexpr float BOT_LANE_START_Y = -6000.0f; // -60m
    static constexpr float BOT_LANE_END_X = -6000.0f;   // -60m
    static constexpr float BOT_LANE_END_Y = 6000.0f;    // +60m

    // ========================================
    // JUNGLE SYSTEM (BASEADO NO LAYOUT DO LOL)
    // ========================================

    /** Raio dos jungle camps */
    static constexpr float JUNGLE_CAMP_RADIUS_CM = 600.0f; // 6 metros

    /** Distância mínima entre jungle camps */
    static constexpr float JUNGLE_CAMP_SPACING_CM = 1500.0f; // 15 metros

    // BUFF CAMPS (equivalentes ao Blue/Red Buff do LoL)

    /** Blue Buff equivalent - Radiant Essence (Team 1 side) */
    static constexpr float RADIANT_ESSENCE_X = -4000.0f; // -40m
    static constexpr float RADIANT_ESSENCE_Y = 2000.0f;  // +20m

    /** Red Buff equivalent - Chaos Essence (Team 2 side) */
    static constexpr float CHAOS_ESSENCE_X = 4000.0f;  // +40m
    static constexpr float CHAOS_ESSENCE_Y = -2000.0f; // -20m

    /** Raio dos buff camps (maiores que camps normais) */
    static constexpr float BUFF_CAMP_RADIUS_CM = 800.0f; // 8 metros

    /** Tempo de respawn dos buff camps em segundos */
    static constexpr float BUFF_CAMP_RESPAWN_TIME = 300.0f; // 5 minutos

    /** Tempo de respawn dos camps normais em segundos */
    static constexpr float NORMAL_CAMP_RESPAWN_TIME = 120.0f; // 2 minutos

    // ========================================
    // SISTEMA DE 3 AMBIENTES DINÂMICOS (ÚNICO DO AURACRON)
    // ========================================

    // PLANÍCIE RADIANTE (Ambiente Base - Terrestre)

    /** Altura da Planície Radiante (nível base) */
    static constexpr float RADIANT_PLAINS_HEIGHT_CM = 0.0f; // 0m (referência)

    /** Características: terreno plano, vegetação exuberante, visibilidade clara */
    static constexpr float RADIANT_PLAINS_VISIBILITY_RANGE_CM = 2000.0f; // 20m visibilidade

    // FIRMAMENTO ZEPHYR (Ambiente Elevado - Celestial/Aéreo)

    /** Altura do Firmamento Zephyr (elevado) */
    static constexpr float ZEPHYR_FIRMAMENT_HEIGHT_CM = 2000.0f; // +20m acima do base

    /** Características: plataformas flutuantes, correntes de ar */
    static constexpr float ZEPHYR_PLATFORM_SPACING_CM = 800.0f; // 8m entre plataformas

    // REINO PURGATÓRIO (Ambiente Subterrâneo - Espectral)

    /** Altura do Reino Purgatório (subterrâneo) */
    static constexpr float PURGATORY_REALM_HEIGHT_CM = -1500.0f; // -15m abaixo do base

    /** Características: túneis, portais, visibilidade reduzida, invisibilidade parcial */
    static constexpr float PURGATORY_VISIBILITY_RANGE_CM = 1000.0f; // 10m visibilidade reduzida
    static constexpr float PURGATORY_PORTAL_RANGE_CM = 3000.0f; // 30m alcance de teletransporte

    // TRANSIÇÕES ENTRE AMBIENTES

    /** Duração de cada ambiente em segundos */
    static constexpr float ENVIRONMENT_DURATION_SECONDS = 480.0f; // 8 minutos

    /** Duração da transição entre ambientes */
    static constexpr float ENVIRONMENT_TRANSITION_SECONDS = 30.0f; // 30 segundos

    /** Ordem de rotação: Radiante → Zephyr → Purgatório → Radiante */
    
    /** Altura do Zephyr Firmament acima do Radiant Plains (10 metros) */
    static constexpr float ZEPHYR_HEIGHT_OFFSET_CM = 1000.0f;
    
    /** Profundidade do Purgatory Realm abaixo do Radiant Plains (5 metros) */
    static constexpr float PURGATORY_DEPTH_OFFSET_CM = -500.0f;
    
    /** Raio do Radiant Plains (ambiente base) */
    static constexpr float RADIANT_PLAINS_RADIUS_CM = 10000.0f;
    
    /** Raio do Zephyr Firmament (80% do mapa base) */
    static constexpr float ZEPHYR_FIRMAMENT_RADIUS_CM = 8000.0f;
    
    /** Raio do Purgatory Realm (70% do mapa base) */
    static constexpr float PURGATORY_REALM_RADIUS_CM = 7000.0f;
    
    // ========================================
    // DIMENSÕES DO PRISMAL FLOW
    // ========================================
    
    /** Largura mínima do Prismal Flow (20 metros) */
    static constexpr float PRISMAL_FLOW_MIN_WIDTH_CM = 2000.0f;
    
    /** Largura máxima do Prismal Flow (50 metros) */
    static constexpr float PRISMAL_FLOW_MAX_WIDTH_CM = 5000.0f;
    
    /** Comprimento total aproximado do Prismal Flow serpenteando */
    static constexpr float PRISMAL_FLOW_LENGTH_CM = 40000.0f;
    
    /** Número de pontos de controle para curvas suaves */
    static constexpr int32 PRISMAL_FLOW_CONTROL_POINTS = 20;
    
    // ========================================
    // DIMENSÕES DAS TRILHAS
    // ========================================
    
    /** Raio das Solar Trails (60 metros do centro) */
    static constexpr float SOLAR_TRAIL_RADIUS_CM = 6000.0f;
    
    /** Raio das Lunar Trails (40 metros do centro) */
    static constexpr float LUNAR_TRAIL_RADIUS_CM = 4000.0f;
    
    /** Largura padrão das trilhas (5 metros) */
    static constexpr float TRAIL_WIDTH_CM = 500.0f;
    
    /** Altura das trilhas acima do terreno (0.5 metros) */
    static constexpr float TRAIL_HEIGHT_OFFSET_CM = 50.0f;
    
    // ========================================
    // DIMENSÕES DAS ILHAS ESTRATÉGICAS
    // ========================================

    /** Altura padrão das ilhas flutuantes */
    static constexpr float ISLAND_HEIGHT_CM = 300.0f; // 3 metros

    /** Raio das Nexus Islands (1.5 metros) */
    static constexpr float NEXUS_ISLAND_RADIUS_CM = 150.0f;
    
    /** Raio das Sanctuary Islands (1 metro) */
    static constexpr float SANCTUARY_ISLAND_RADIUS_CM = 100.0f;
    
    /** Raio das Arsenal Islands (1.2 metros) */
    static constexpr float ARSENAL_ISLAND_RADIUS_CM = 120.0f;
    
    /** Raio das Chaos Islands (1.3 metros) */
    static constexpr float CHAOS_ISLAND_RADIUS_CM = 130.0f;
    
    /** Número de cada tipo de ilha */
    static constexpr int32 NEXUS_ISLAND_COUNT = 5;
    static constexpr int32 SANCTUARY_ISLAND_COUNT = 8;
    static constexpr int32 ARSENAL_ISLAND_COUNT = 6;
    static constexpr int32 CHAOS_ISLAND_COUNT = 4;
    
    // ========================================
    // CARACTERÍSTICAS ESPECÍFICAS DOS AMBIENTES
    // ========================================
    
    // Radiant Plains
    static constexpr int32 CRYSTALLINE_PLATEAUS_COUNT_MIN = 8;
    static constexpr int32 CRYSTALLINE_PLATEAUS_COUNT_MAX = 12;
    static constexpr float CRYSTALLINE_PLATEAU_HEIGHT_MIN_CM = 200.0f;
    static constexpr float CRYSTALLINE_PLATEAU_HEIGHT_MAX_CM = 500.0f;
    
    static constexpr int32 LIVING_CANYONS_COUNT_MIN = 4;
    static constexpr int32 LIVING_CANYONS_COUNT_MAX = 6;
    static constexpr float LIVING_CANYON_DEPTH_MIN_CM = 300.0f;
    static constexpr float LIVING_CANYON_DEPTH_MAX_CM = 800.0f;
    
    static constexpr int32 BREATHING_FORESTS_COUNT_MIN = 15;
    static constexpr int32 BREATHING_FORESTS_COUNT_MAX = 20;
    static constexpr float BREATHING_FOREST_RADIUS_MIN_CM = 300.0f;
    static constexpr float BREATHING_FOREST_RADIUS_MAX_CM = 600.0f;
    
    static constexpr int32 TECTONIC_BRIDGES_COUNT_MIN = 6;
    static constexpr int32 TECTONIC_BRIDGES_COUNT_MAX = 8;
    static constexpr float TECTONIC_BRIDGE_LENGTH_MIN_CM = 1000.0f;
    static constexpr float TECTONIC_BRIDGE_LENGTH_MAX_CM = 2000.0f;
    
    // Zephyr Firmament
    static constexpr int32 ORBITAL_ARCHIPELAGOS_COUNT_MIN = 10;
    static constexpr int32 ORBITAL_ARCHIPELAGOS_COUNT_MAX = 15;
    
    static constexpr int32 AURORA_BRIDGES_COUNT_MIN = 8;
    static constexpr int32 AURORA_BRIDGES_COUNT_MAX = 12;
    
    static constexpr int32 CLOUD_FORTRESSES_COUNT_MIN = 4;
    static constexpr int32 CLOUD_FORTRESSES_COUNT_MAX = 6;
    
    static constexpr int32 STELLAR_GARDENS_COUNT_MIN = 12;
    static constexpr int32 STELLAR_GARDENS_COUNT_MAX = 18;
    
    static constexpr int32 VOID_RIFTS_COUNT_MIN = 5;
    static constexpr int32 VOID_RIFTS_COUNT_MAX = 8;
    static constexpr float VOID_RIFT_RADIUS_MIN_CM = 200.0f;
    static constexpr float VOID_RIFT_RADIUS_MAX_CM = 400.0f;
    
    // Purgatory Realm
    static constexpr int32 TEMPORAL_DISTORTION_ZONES_COUNT_MIN = 6;
    static constexpr int32 TEMPORAL_DISTORTION_ZONES_COUNT_MAX = 8;
    
    // ========================================
    // OBJETIVOS ESTRATÉGICOS (BARON/DRAGON EQUIVALENTS)
    // ========================================

    // PRISMAL NEXUS (Baron equivalent) - objetivo principal superior

    /** Posição do Prismal Nexus (Baron equivalent) */
    static constexpr float PRISMAL_NEXUS_X = 0.0f;    // Centro X
    static constexpr float PRISMAL_NEXUS_Y = 6000.0f; // +60m do centro

    /** Dimensões do pit do Prismal Nexus */
    static constexpr float PRISMAL_NEXUS_PIT_RADIUS_CM = 800.0f; // 8 metros
    static constexpr float PRISMAL_NEXUS_PIT_DEPTH_CM = 200.0f;  // 2 metros

    /** Tempo de respawn do Prismal Nexus */
    static constexpr float PRISMAL_NEXUS_RESPAWN_TIME = 420.0f; // 7 minutos (como Baron)

    // ELEMENTAL ANCHORS (Dragon equivalents) - objetivos secundários

    /** Radiant Anchor (Fire/Earth element) - inferior esquerdo */
    static constexpr float RADIANT_ANCHOR_X = -4500.0f; // -45m
    static constexpr float RADIANT_ANCHOR_Y = -4500.0f; // -45m

    /** Zephyr Anchor (Air/Lightning element) - direita */
    static constexpr float ZEPHYR_ANCHOR_X = 4500.0f; // +45m
    static constexpr float ZEPHYR_ANCHOR_Y = 0.0f;    // Centro Y

    /** Purgatory Anchor (Shadow/Spectral element) - inferior direito */
    static constexpr float PURGATORY_ANCHOR_X = 2000.0f;  // +20m
    static constexpr float PURGATORY_ANCHOR_Y = -4500.0f; // -45m

    /** Dimensões dos pits dos Elemental Anchors */
    static constexpr float ELEMENTAL_ANCHOR_PIT_RADIUS_CM = 600.0f; // 6 metros
    static constexpr float ELEMENTAL_ANCHOR_PIT_DEPTH_CM = 150.0f;  // 1.5 metros

    /** Tempo de respawn dos Elemental Anchors */
    static constexpr float ELEMENTAL_ANCHOR_RESPAWN_TIME = 300.0f; // 5 minutos (como Dragons)

    // ========================================
    // BASES E ESTRUTURAS DEFENSIVAS (SISTEMA LOL)
    // ========================================

    // BASES PRINCIPAIS (Nexus equivalents)

    /** Team 1 Base (canto inferior esquerdo) */
    static constexpr float TEAM1_BASE_X = -6500.0f; // -65m
    static constexpr float TEAM1_BASE_Y = -6500.0f; // -65m

    /** Team 2 Base (canto superior direito) */
    static constexpr float TEAM2_BASE_X = 6500.0f; // +65m
    static constexpr float TEAM2_BASE_Y = 6500.0f; // +65m

    /** Raio da base principal (Nexus) */
    static constexpr float BASE_RADIUS_CM = 1000.0f; // 10 metros

    /** Altura da base principal */
    static constexpr float BASE_HEIGHT_CM = 600.0f; // 6 metros

    // TORRES DEFENSIVAS (3 por lane como no LoL)

    /** Dimensões das torres defensivas */
    static constexpr float TOWER_RADIUS_CM = 150.0f; // 1.5 metros
    static constexpr float TOWER_HEIGHT_CM = 800.0f; // 8 metros
    static constexpr float TOWER_ATTACK_RANGE_CM = 1200.0f; // 12 metros

    /** Posições das torres em % da lane (LoL style) */
    static constexpr float OUTER_TOWER_POSITION = 0.25f;     // 25% da lane
    static constexpr float INNER_TOWER_POSITION = 0.50f;     // 50% da lane
    static constexpr float INHIBITOR_TOWER_POSITION = 0.75f; // 75% da lane

    // INIBIDORES (entre Inhibitor Tower e Nexus)

    /** Dimensões dos inibidores */
    static constexpr float INHIBITOR_RADIUS_CM = 200.0f; // 2 metros
    static constexpr float INHIBITOR_HEIGHT_CM = 400.0f; // 4 metros

    /** Posição dos inibidores em % da lane */
    static constexpr float INHIBITOR_POSITION = 0.85f; // 85% da lane

    // ========================================
    // SISTEMA DE 4 FASES EVOLUTIVAS (BASEADO NO GDD)
    // ========================================

    // FASE 1 - AWAKENING (0-10 minutos)
    /** Duração da Fase 1 - Awakening */
    static constexpr float PHASE_AWAKENING_DURATION_SECONDS = 600.0f; // 10 minutos
    /** Características: mapa completo, jungle básico, objetivos inativos */

    // FASE 2 - CONVERGENCE (10-20 minutos)
    /** Duração da Fase 2 - Convergence */
    static constexpr float PHASE_CONVERGENCE_DURATION_SECONDS = 600.0f; // 10 minutos
    /** Características: objetivos ativam, jungle evolui, ilhas aparecem */

    // FASE 3 - INTENSIFICATION (20-35 minutos)
    /** Duração da Fase 3 - Intensification */
    static constexpr float PHASE_INTENSIFICATION_DURATION_SECONDS = 900.0f; // 15 minutos
    /** Características: todos objetivos ativos, jungle elite, eventos especiais */

    // FASE 4 - RESOLUTION (35+ minutos)
    /** Início da Fase 4 - Resolution */
    static constexpr float PHASE_RESOLUTION_START_SECONDS = 2100.0f; // 35 minutos
    /** Características: mapa contrai 20%, objetivos super poderosos, eventos game-changing */

    /** Fator de contração do mapa na fase Resolution */
    static constexpr float MAP_CONTRACTION_FACTOR = 0.8f; // 20% menor = 120m x 120m

    /** Tamanho do mapa contraído na fase Resolution */
    static constexpr float MAP_SIZE_CONTRACTED_CM = MAP_SIZE_CM * MAP_CONTRACTION_FACTOR; // 12000cm = 120m

    // ========================================
    // ILHAS ESTRATÉGICAS (ÚNICAS DO AURACRON)
    // ========================================

    // NEXUS ISLAND (centro do mapa)
    /** Posição da Nexus Island */
    static constexpr float NEXUS_ISLAND_X = 0.0f; // Centro
    static constexpr float NEXUS_ISLAND_Y = 0.0f; // Centro
    // Raio já definido acima como 150.0f

    // SANCTUARY ISLANDS (4 posições simétricas - pontos de cura/buff)
    // Raio já definido acima como 100.0f
    /** Distância das Sanctuary Islands do centro */
    static constexpr float SANCTUARY_ISLAND_DISTANCE_CM = 3500.0f; // 35 metros

    // ARSENAL ISLANDS (2 posições - pontos de equipamento/upgrade)
    // Raio já definido acima como 120.0f
    /** Posições das Arsenal Islands */
    static constexpr float ARSENAL_ISLAND_1_X = -2500.0f; // -25m
    static constexpr float ARSENAL_ISLAND_1_Y = 3000.0f;  // +30m
    static constexpr float ARSENAL_ISLAND_2_X = 2500.0f;  // +25m
    static constexpr float ARSENAL_ISLAND_2_Y = -3000.0f; // -30m

    // CHAOS ISLANDS (3 posições rotativas - eventos aleatórios)
    // Raio já definido acima como 130.0f
    /** Tempo de rotação das Chaos Islands */
    static constexpr float CHAOS_ISLAND_ROTATION_TIME = 180.0f; // 3 minutos

    // ========================================
    // FLUXO PRISMAL (RIVER EQUIVALENT)
    // ========================================

    /** Largura do Fluxo Prismal */
    static constexpr float PRISMAL_FLOW_WIDTH_CM = 1200.0f; // 12 metros (mais largo que lanes)

    /** Profundidade do Fluxo Prismal */
    static constexpr float PRISMAL_FLOW_DEPTH_CM = 100.0f; // 1 metro

    /** Velocidade de movimento no Fluxo Prismal (bonus) */
    static constexpr float PRISMAL_FLOW_SPEED_MULTIPLIER = 1.3f; // 30% mais rápido

    /** Pontos de controle do Fluxo Prismal (curva serpentina) */
    // Conecta: Prismal Nexus → Elemental Anchors → Bases

    // ========================================
    // CONVERSÕES E UTILITÁRIOS
    // ========================================
    
    /** Converter metros para unidades UE (centímetros) */
    static constexpr float MetersToUnrealUnits(float Meters)
    {
        return Meters * 100.0f;
    }
    
    /** Converter unidades UE (centímetros) para metros */
    static constexpr float UnrealUnitsToMeters(float UnrealUnits)
    {
        return UnrealUnits / 100.0f;
    }
    
    /** Obter posição do ambiente baseado no tipo */
    static FVector GetEnvironmentCenter(int32 EnvironmentType);
    
    /** Obter raio do ambiente baseado no tipo */
    static float GetEnvironmentRadius(int32 EnvironmentType);
    
    /** Calcular posição na curva do Prismal Flow */
    static FVector GetPrismalFlowPosition(float T, const FVector& MapCenter);
    
    /** Calcular largura do Prismal Flow baseada na posição */
    static float GetPrismalFlowWidth(float T);
    
    /** Obter posições das trilhas baseadas no tipo e tempo */
    static TArray<FVector> GetTrailPositions(int32 TrailType, float TimeOfDay, const FVector& MapCenter);
    
    /** Calcular posições das ilhas estratégicas */
    static TArray<FVector> GetIslandPositions(int32 IslandType, const TArray<FVector>& FlowPoints);
};

/**
 * Biblioteca de funções utilitárias para medidas e conversões do mapa AURACRON
 */
UCLASS()
class AURACRON_API UAURACRONMapMeasurements : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // ========================================
    // FUNÇÕES DE CONVERSÃO
    // ========================================
    
    /** Converter metros para unidades Unreal (centímetros) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static float MetersToUnrealUnits(float Meters);
    
    /** Converter unidades Unreal (centímetros) para metros */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static float UnrealUnitsToMeters(float UnrealUnits);
    
    // ========================================
    // FUNÇÕES DE POSICIONAMENTO
    // ========================================
    
    /** Obter centro do ambiente baseado no tipo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static FVector GetEnvironmentCenter(int32 EnvironmentType);
    
    /** Obter raio do ambiente baseado no tipo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static float GetEnvironmentRadius(int32 EnvironmentType);
    
    /** Calcular posição na curva do Prismal Flow */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static FVector GetPrismalFlowPosition(float T, const FVector& MapCenter);
    
    /** Calcular largura do Prismal Flow baseada na posição */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static float GetPrismalFlowWidth(float T);
    
    /** Obter posições das trilhas baseadas no tipo e tempo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static TArray<FVector> GetTrailPositions(int32 TrailType, float TimeOfDay, const FVector& MapCenter);
    
    /** Calcular posições das ilhas estratégicas */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static TArray<FVector> GetIslandPositions(int32 IslandType, const TArray<FVector>& FlowPoints);
    
    // ========================================
    // FUNÇÕES DE VALIDAÇÃO
    // ========================================
    
    /** Verificar se uma posição está dentro dos limites do mapa */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static bool IsPositionWithinMapBounds(const FVector& Position, const FVector& MapCenter);
    
    /** Calcular distância entre dois pontos em metros */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static float GetDistanceInMeters(const FVector& PointA, const FVector& PointB);
    
    /** Obter fase do mapa baseada no tempo decorrido */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static int32 GetMapPhaseFromTime(float ElapsedTimeSeconds);
    
    /** Calcular fator de escala baseado na fase do mapa */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Measurements")
    static float GetMapScaleFactorForPhase(int32 MapPhase);

    // ========================================
    // FUNÇÕES PARA LANES E JUNGLE (BASEADAS EM LOL/DOTA 2)
    // ========================================

    /** Obter pontos da lane superior (Top Lane) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|MapMeasurements")
    static TArray<FVector> GetTopLanePoints();

    /** Obter pontos da lane do meio (Mid Lane) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|MapMeasurements")
    static TArray<FVector> GetMidLanePoints();

    /** Obter pontos da lane inferior (Bot Lane) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|MapMeasurements")
    static TArray<FVector> GetBotLanePoints();

    /** Obter posições dos camps da jungle */
    UFUNCTION(BlueprintPure, Category = "AURACRON|MapMeasurements")
    static TArray<FVector> GetJungleCampPositions();

    /** Obter posições das torres por lane */
    UFUNCTION(BlueprintPure, Category = "AURACRON|MapMeasurements")
    static TArray<FVector> GetTowerPositions(int32 LaneIndex); // 0=Top, 1=Mid, 2=Bot

    /** Obter posições dos objetivos estratégicos */
    UFUNCTION(BlueprintPure, Category = "AURACRON|MapMeasurements")
    static TArray<FVector> GetStrategicObjectivePositions();

    /** Obter posições das bases */
    UFUNCTION(BlueprintPure, Category = "AURACRON|MapMeasurements")
    static TArray<FVector> GetBasePositions();

    /** Calcular posição na lane baseada em porcentagem (0.0 = início, 1.0 = fim) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|MapMeasurements")
    static FVector GetLanePosition(int32 LaneIndex, float LaneProgress);

    /** Verificar se posição está dentro de uma lane */
    UFUNCTION(BlueprintPure, Category = "AURACRON|MapMeasurements")
    static bool IsPositionInLane(const FVector& Position, int32 LaneIndex, float Tolerance = 400.0f);

    /** Obter lane mais próxima de uma posição */
    UFUNCTION(BlueprintPure, Category = "AURACRON|MapMeasurements")
    static int32 GetClosestLane(const FVector& Position);
};
