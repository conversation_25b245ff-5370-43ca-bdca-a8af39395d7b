// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGNexusIsland.h"
#include "ActiveGameplayEffectHandle.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGNexusIsland() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_ANexusIsland();
AURACRON_API UClass* Z_Construct_UClass_ANexusIsland_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_APrismalFlowIsland();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FActiveGameplayEffectHandle();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class ANexusIsland Function GrantFlowManipulationAbility ***********************
struct Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics
{
	struct NexusIsland_eventGrantFlowManipulationAbility_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Nexus Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Concede habilidade de manipula\xc3\xa7\xc3\xa3o do flow ao jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Concede habilidade de manipula\xc3\xa7\xc3\xa3o do flow ao jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(NexusIsland_eventGrantFlowManipulationAbility_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ANexusIsland, nullptr, "GrantFlowManipulationAbility", Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::PropPointers), sizeof(Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::NexusIsland_eventGrantFlowManipulationAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::NexusIsland_eventGrantFlowManipulationAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ANexusIsland::execGrantFlowManipulationAbility)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GrantFlowManipulationAbility(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class ANexusIsland Function GrantFlowManipulationAbility *************************

// ********** Begin Class ANexusIsland Function RemoveFlowManipulationAbility **********************
struct Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics
{
	struct NexusIsland_eventRemoveFlowManipulationAbility_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove a habilidade de manipula\xc3\xa7\xc3\xa3o do flow\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove a habilidade de manipula\xc3\xa7\xc3\xa3o do flow" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(NexusIsland_eventRemoveFlowManipulationAbility_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ANexusIsland, nullptr, "RemoveFlowManipulationAbility", Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::PropPointers), sizeof(Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::NexusIsland_eventRemoveFlowManipulationAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::NexusIsland_eventRemoveFlowManipulationAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ANexusIsland::execRemoveFlowManipulationAbility)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveFlowManipulationAbility(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class ANexusIsland Function RemoveFlowManipulationAbility ************************

// ********** Begin Class ANexusIsland *************************************************************
void ANexusIsland::StaticRegisterNativesANexusIsland()
{
	UClass* Class = ANexusIsland::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GrantFlowManipulationAbility", &ANexusIsland::execGrantFlowManipulationAbility },
		{ "RemoveFlowManipulationAbility", &ANexusIsland::execRemoveFlowManipulationAbility },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ANexusIsland;
UClass* ANexusIsland::GetPrivateStaticClass()
{
	using TClass = ANexusIsland;
	if (!Z_Registration_Info_UClass_ANexusIsland.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("NexusIsland"),
			Z_Registration_Info_UClass_ANexusIsland.InnerSingleton,
			StaticRegisterNativesANexusIsland,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ANexusIsland.InnerSingleton;
}
UClass* Z_Construct_UClass_ANexusIsland_NoRegister()
{
	return ANexusIsland::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ANexusIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica da Nexus Island\n * Ilha central com poderes especiais que concede habilidades de manipula\xc3\xa7\xc3\xa3o do flow\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGNexusIsland.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica da Nexus Island\nIlha central com poderes especiais que concede habilidades de manipula\xc3\xa7\xc3\xa3o do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PowerIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Nexus Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade do poder concedido\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do poder concedido" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PowerDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Nexus Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o do poder concedido\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do poder concedido" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PowerEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Nexus Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual do poder\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual do poder" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowManipulationEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Nexus Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para manipula\xc3\xa7\xc3\xa3o de fluxo\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para manipula\xc3\xa7\xc3\xa3o de fluxo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveFlowManipulationEffects_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mapa de efeitos ativos de manipula\xc3\xa7\xc3\xa3o de fluxo por ator\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mapa de efeitos ativos de manipula\xc3\xa7\xc3\xa3o de fluxo por ator" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CentralTower_MetaData[] = {
		{ "Category", "Prismal Flow|Nexus Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Torre de controle central\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Torre de controle central" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerEnergyEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Nexus Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de energia da torre\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de energia da torre" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefensivePlatforms_MetaData[] = {
		{ "Category", "Prismal Flow|Nexus Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Plataformas defensivas em m\xc3\xbaltiplos n\xc3\xadveis\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Plataformas defensivas em m\xc3\xbaltiplos n\xc3\xadveis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceGenerator_MetaData[] = {
		{ "Category", "Prismal Flow|Nexus Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gerador de recursos\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerador de recursos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Nexus Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito do gerador de recursos\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito do gerador de recursos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tempo acumulado para efeitos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGNexusIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo acumulado para efeitos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PowerIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PowerDuration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PowerEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_FlowManipulationEffect;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveFlowManipulationEffects_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveFlowManipulationEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveFlowManipulationEffects;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CentralTower;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TowerEnergyEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DefensivePlatforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DefensivePlatforms;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ResourceGenerator;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ResourceEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ANexusIsland_GrantFlowManipulationAbility, "GrantFlowManipulationAbility" }, // 1922345481
		{ &Z_Construct_UFunction_ANexusIsland_RemoveFlowManipulationAbility, "RemoveFlowManipulationAbility" }, // 3283790335
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ANexusIsland>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_PowerIntensity = { "PowerIntensity", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, PowerIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PowerIntensity_MetaData), NewProp_PowerIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_PowerDuration = { "PowerDuration", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, PowerDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PowerDuration_MetaData), NewProp_PowerDuration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_PowerEffect = { "PowerEffect", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, PowerEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PowerEffect_MetaData), NewProp_PowerEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_FlowManipulationEffect = { "FlowManipulationEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, FlowManipulationEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowManipulationEffect_MetaData), NewProp_FlowManipulationEffect_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_ActiveFlowManipulationEffects_ValueProp = { "ActiveFlowManipulationEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_ActiveFlowManipulationEffects_Key_KeyProp = { "ActiveFlowManipulationEffects_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_ActiveFlowManipulationEffects = { "ActiveFlowManipulationEffects", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, ActiveFlowManipulationEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveFlowManipulationEffects_MetaData), NewProp_ActiveFlowManipulationEffects_MetaData) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_CentralTower = { "CentralTower", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, CentralTower), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CentralTower_MetaData), NewProp_CentralTower_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_TowerEnergyEffect = { "TowerEnergyEffect", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, TowerEnergyEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerEnergyEffect_MetaData), NewProp_TowerEnergyEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_DefensivePlatforms_Inner = { "DefensivePlatforms", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_DefensivePlatforms = { "DefensivePlatforms", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, DefensivePlatforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefensivePlatforms_MetaData), NewProp_DefensivePlatforms_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_ResourceGenerator = { "ResourceGenerator", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, ResourceGenerator), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceGenerator_MetaData), NewProp_ResourceGenerator_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_ResourceEffect = { "ResourceEffect", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, ResourceEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceEffect_MetaData), NewProp_ResourceEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ANexusIsland_Statics::NewProp_AccumulatedTime = { "AccumulatedTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ANexusIsland, AccumulatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedTime_MetaData), NewProp_AccumulatedTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ANexusIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_PowerIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_PowerDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_PowerEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_FlowManipulationEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_ActiveFlowManipulationEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_ActiveFlowManipulationEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_ActiveFlowManipulationEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_CentralTower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_TowerEnergyEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_DefensivePlatforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_DefensivePlatforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_ResourceGenerator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_ResourceEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ANexusIsland_Statics::NewProp_AccumulatedTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ANexusIsland_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ANexusIsland_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_APrismalFlowIsland,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ANexusIsland_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ANexusIsland_Statics::ClassParams = {
	&ANexusIsland::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ANexusIsland_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ANexusIsland_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ANexusIsland_Statics::Class_MetaDataParams), Z_Construct_UClass_ANexusIsland_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ANexusIsland()
{
	if (!Z_Registration_Info_UClass_ANexusIsland.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ANexusIsland.OuterSingleton, Z_Construct_UClass_ANexusIsland_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ANexusIsland.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void ANexusIsland::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_PowerIntensity(TEXT("PowerIntensity"));
	static FName Name_PowerDuration(TEXT("PowerDuration"));
	static FName Name_FlowManipulationEffect(TEXT("FlowManipulationEffect"));
	const bool bIsValid = true
		&& Name_PowerIntensity == ClassReps[(int32)ENetFields_Private::PowerIntensity].Property->GetFName()
		&& Name_PowerDuration == ClassReps[(int32)ENetFields_Private::PowerDuration].Property->GetFName()
		&& Name_FlowManipulationEffect == ClassReps[(int32)ENetFields_Private::FlowManipulationEffect].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in ANexusIsland"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(ANexusIsland);
ANexusIsland::~ANexusIsland() {}
// ********** End Class ANexusIsland ***************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ANexusIsland, ANexusIsland::StaticClass, TEXT("ANexusIsland"), &Z_Registration_Info_UClass_ANexusIsland, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ANexusIsland), 1243297791U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h__Script_AURACRON_3204054121(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
