// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGJungleSystem.h"

#ifdef AURACRON_AURACRONPCGJungleSystem_generated_h
#error "AURACRONPCGJungleSystem.generated.h already included, missing '#pragma once' in AURACRONPCGJungleSystem.h"
#endif
#define AURACRON_AURACRONPCGJungleSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONJungleAdaptiveBehavior : uint8;
enum class EAURACRONJungleCampType : uint8;
enum class EAURACRONJunglePlayerProfile : uint8;
enum class EAURACRONMapPhase : uint8;
struct FAURACRONJungleAdaptiveData;
struct FAURACRONJungleCampInfo;

// ********** Begin ScriptStruct FAURACRONJungleAdaptiveData ***************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_82_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONJungleAdaptiveData;
// ********** End ScriptStruct FAURACRONJungleAdaptiveData *****************************************

// ********** Begin ScriptStruct FAURACRONMeshComponentArray ***************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_142_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONMeshComponentArray;
// ********** End ScriptStruct FAURACRONMeshComponentArray *****************************************

// ********** Begin ScriptStruct FAURACRONJungleCampInfo *******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_160_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONJungleCampInfo;
// ********** End ScriptStruct FAURACRONJungleCampInfo *********************************************

// ********** Begin Class AAURACRONPCGJungleSystem *************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_266_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execResetAdaptiveData); \
	DECLARE_FUNCTION(execGetRecommendedBehaviorForCamp); \
	DECLARE_FUNCTION(execSetAdaptiveDifficulty); \
	DECLARE_FUNCTION(execGetCampRewardMultiplier); \
	DECLARE_FUNCTION(execGetCampDifficultyMultiplier); \
	DECLARE_FUNCTION(execAdaptCampBehaviors); \
	DECLARE_FUNCTION(execRegisterObjectiveSecured); \
	DECLARE_FUNCTION(execRegisterJungleInvasion); \
	DECLARE_FUNCTION(execRegisterCampInteraction); \
	DECLARE_FUNCTION(execSetPlayerProfile); \
	DECLARE_FUNCTION(execGetAdaptiveData); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execUpdateForEnvironment); \
	DECLARE_FUNCTION(execIsCampAvailable); \
	DECLARE_FUNCTION(execClearCamp); \
	DECLARE_FUNCTION(execGetCampsBySide); \
	DECLARE_FUNCTION(execGetCampsByType); \
	DECLARE_FUNCTION(execGetAllCamps); \
	DECLARE_FUNCTION(execGenerateCampsForEnvironment); \
	DECLARE_FUNCTION(execGenerateJungleCamps);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_266_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGJungleSystem(); \
	friend struct Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGJungleSystem, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGJungleSystem)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_266_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGJungleSystem(AAURACRONPCGJungleSystem&&) = delete; \
	AAURACRONPCGJungleSystem(const AAURACRONPCGJungleSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGJungleSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGJungleSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGJungleSystem) \
	NO_API virtual ~AAURACRONPCGJungleSystem();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_263_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_266_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_266_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_266_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h_266_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGJungleSystem;

// ********** End Class AAURACRONPCGJungleSystem ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h

// ********** Begin Enum EAURACRONJunglePlayerProfile **********************************************
#define FOREACH_ENUM_EAURACRONJUNGLEPLAYERPROFILE(op) \
	op(EAURACRONJunglePlayerProfile::Aggressive) \
	op(EAURACRONJunglePlayerProfile::Farming) \
	op(EAURACRONJunglePlayerProfile::Objective) \
	op(EAURACRONJunglePlayerProfile::Supportive) \
	op(EAURACRONJunglePlayerProfile::Balanced) 

enum class EAURACRONJunglePlayerProfile : uint8;
template<> struct TIsUEnumClass<EAURACRONJunglePlayerProfile> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJunglePlayerProfile>();
// ********** End Enum EAURACRONJunglePlayerProfile ************************************************

// ********** Begin Enum EAURACRONJungleCampType ***************************************************
#define FOREACH_ENUM_EAURACRONJUNGLECAMPTYPE(op) \
	op(EAURACRONJungleCampType::RadiantEssence) \
	op(EAURACRONJungleCampType::ChaosEssence) \
	op(EAURACRONJungleCampType::SpectralPack) \
	op(EAURACRONJungleCampType::EtherealGrove) \
	op(EAURACRONJungleCampType::VoidRaptors) \
	op(EAURACRONJungleCampType::CrystalWolves) \
	op(EAURACRONJungleCampType::PrismalDragon) \
	op(EAURACRONJungleCampType::AncientGuardian) 

enum class EAURACRONJungleCampType : uint8;
template<> struct TIsUEnumClass<EAURACRONJungleCampType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJungleCampType>();
// ********** End Enum EAURACRONJungleCampType *****************************************************

// ********** Begin Enum EAURACRONJungleAdaptiveBehavior *******************************************
#define FOREACH_ENUM_EAURACRONJUNGLEADAPTIVEBEHAVIOR(op) \
	op(EAURACRONJungleAdaptiveBehavior::Standard) \
	op(EAURACRONJungleAdaptiveBehavior::Defensive) \
	op(EAURACRONJungleAdaptiveBehavior::Aggressive) \
	op(EAURACRONJungleAdaptiveBehavior::Rewarding) \
	op(EAURACRONJungleAdaptiveBehavior::Challenging) 

enum class EAURACRONJungleAdaptiveBehavior : uint8;
template<> struct TIsUEnumClass<EAURACRONJungleAdaptiveBehavior> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJungleAdaptiveBehavior>();
// ********** End Enum EAURACRONJungleAdaptiveBehavior *********************************************

// ********** Begin Enum EAURACRONJungleStrategy ***************************************************
#define FOREACH_ENUM_EAURACRONJUNGLESTRATEGY(op) \
	op(EAURACRONJungleStrategy::Balanced) \
	op(EAURACRONJungleStrategy::Aggressive) \
	op(EAURACRONJungleStrategy::Farming) \
	op(EAURACRONJungleStrategy::Objective) 

enum class EAURACRONJungleStrategy : uint8;
template<> struct TIsUEnumClass<EAURACRONJungleStrategy> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJungleStrategy>();
// ********** End Enum EAURACRONJungleStrategy *****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
