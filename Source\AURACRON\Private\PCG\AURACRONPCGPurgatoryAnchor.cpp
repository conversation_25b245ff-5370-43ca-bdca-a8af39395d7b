// AURACRONPCGPurgatoryAnchor.cpp
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Implementação da Âncora do Purgatório no Reino Purgatório

#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGSettings.h"
#include "PCGVolume.h"
#include "Engine/World.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraFunctionLibrary.h"
#include "Kismet/GameplayStatics.h"

void AAURACRONPCGEnvironment::GeneratePurgatoryAnchor()
{
    // Limpar dados existentes
    if (PurgatoryAnchorData.AnchorActor)
    {
        if (PurgatoryAnchorData.AuraComponent)
        {
            PurgatoryAnchorData.AuraComponent->DestroyComponent();
            PurgatoryAnchorData.AuraComponent = nullptr;
        }
        
        if (IsValid(PurgatoryAnchorData.AnchorActor))
        {
            PurgatoryAnchorData.AnchorActor->Destroy();
            PurgatoryAnchorData.AnchorActor = nullptr;
        }
    }
    
    // Verificar se o componente PCG é válido
    if (!PCGComponent || !GetWorld())
    {
        UE_LOG(LogTemp, Warning, TEXT("PCGComponent ou World inválido ao gerar Âncora do Purgatório"));
        return;
    }
    
    // Verificar se estamos no Reino Purgatório
    if (EnvironmentType != EAURACRONEnvironmentType::PurgatoryRealm)
    {
        return; // A Âncora do Purgatório só existe no Reino Purgatório
    }
    
    // Obter posição da Âncora do Purgatório a partir das medidas do mapa
    FVector Position = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::PURGATORY_ANCHOR_X,
        FAURACRONMapDimensions::PURGATORY_ANCHOR_Y,
        0.0f);
    
    // Ajustar altura com base na topografia
    FVector TraceStart = Position + FVector(0, 0, 5000.0f);
    FVector TraceEnd = Position - FVector(0, 0, 5000.0f);
    FHitResult HitResult;
    
    if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_Visibility))
    {
        Position.Z = HitResult.ImpactPoint.Z + 100.0f; // Elevar acima do solo
    }
    else
    {
        Position.Z = 100.0f; // Altura padrão se não encontrar o solo
    }
    
    // Adicionar profundidade para o Reino Purgatório
    Position.Z += FAURACRONMapDimensions::PURGATORY_DEPTH_OFFSET_CM;
    
    // Configurar dados da Âncora do Purgatório
    PurgatoryAnchorData.Position = Position;
    PurgatoryAnchorData.Radius = 600.0f;
    PurgatoryAnchorData.RotationSpeed = 0.15f;
    PurgatoryAnchorData.PulsationAmplitude = 0.25f;
    PurgatoryAnchorData.PulsationSpeed = 0.2f;
    PurgatoryAnchorData.IsActive = true; // Inicialmente ativo
    
    // Configurar parâmetros PCG
    UPCGGraph* PCGGraph = PCGComponent->GetGraph();
    if (PCGGraph)
    {
        // Adicionar ponto para a Âncora do Purgatório
        PCGGraph->SetPointData("PurgatoryAnchorPosition", Position);
        PCGGraph->SetFloatData("PurgatoryAnchorRadius", PurgatoryAnchorData.Radius);
        PCGGraph->SetBoolData("PurgatoryAnchorActive", PurgatoryAnchorData.IsActive);
    }
    
    // Criar ator para a Âncora do Purgatório
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    
    AStaticMeshActor* AnchorActor = GetWorld()->SpawnActor<AStaticMeshActor>(Position, FRotator::ZeroRotator, SpawnParams);
    if (AnchorActor)
    {
        // Configurar o ator
        AnchorActor->SetMobility(EComponentMobility::Movable);
        UStaticMeshComponent* MeshComponent = AnchorActor->GetStaticMeshComponent();
        
        if (MeshComponent)
        {
            // Configurar a malha (deve ser definida no Blueprint)
            // MeshComponent->SetStaticMesh(...); // Será configurado via Blueprint
            MeshComponent->SetRelativeScale3D(FVector(1.5f));
            MeshComponent->SetCollisionProfileName(TEXT("BlockAll"));
        }
        
        PurgatoryAnchorData.AnchorActor = AnchorActor;
    }
    
    // Criar sistema de partículas para a aura da Âncora do Purgatório
    if (PurgatoryAnchorParticleSystem && AnchorActor)
    {
        UNiagaraComponent* NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            PurgatoryAnchorParticleSystem,
            AnchorActor->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::SnapToTarget,
            true
        );
        
        if (NiagaraComponent)
        {
            // Configurar parâmetros do sistema de partículas
            NiagaraComponent->SetVariableFloat(TEXT("Radius"), PurgatoryAnchorData.Radius);
            NiagaraComponent->SetVariableFloat(TEXT("PulsationSpeed"), PurgatoryAnchorData.PulsationSpeed);
            NiagaraComponent->SetVariableFloat(TEXT("PulsationAmplitude"), PurgatoryAnchorData.PulsationAmplitude);
            NiagaraComponent->SetVariableFloat(TEXT("EnergyIntensity"), 1.0f);
            NiagaraComponent->SetVariableLinearColor(TEXT("AuraColor"), FLinearColor(0.3f, 0.0f, 0.5f, 1.0f)); // Cor roxa escura
            
            PurgatoryAnchorData.AuraComponent = NiagaraComponent;
        }
    }
}

void AAURACRONPCGEnvironment::UpdatePurgatoryAnchor(float CurrentTime)
{
    // Verificar se estamos no Reino Purgatório
    if (EnvironmentType != EAURACRONEnvironmentType::PurgatoryRealm)
    {
        return;
    }
    
    // Verificar se a Âncora do Purgatório está configurada
    if (!PurgatoryAnchorData.AnchorActor || !PurgatoryAnchorData.AuraComponent)
    {
        return;
    }
    
    // Verificar se a Âncora do Purgatório deve estar ativa com base na fase atual
    UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>();
    if (PCGSubsystem)
    {
        EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
        bool ShouldBeActive = true; // A Âncora do Purgatório está sempre ativa
        
        // Atualizar estado de ativação se necessário
        if (PurgatoryAnchorData.IsActive != ShouldBeActive)
        {
            PurgatoryAnchorData.IsActive = ShouldBeActive;
            
            // Atualizar visibilidade do ator
            if (PurgatoryAnchorData.AnchorActor)
            {
                PurgatoryAnchorData.AnchorActor->SetActorHiddenInGame(!ShouldBeActive);
                
                // Atualizar colisão
                UStaticMeshComponent* MeshComponent = PurgatoryAnchorData.AnchorActor->GetStaticMeshComponent();
                if (MeshComponent)
                {
                    MeshComponent->SetCollisionEnabled(ShouldBeActive ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::NoCollision);
                }
            }
            
            // Atualizar sistema de partículas
            if (PurgatoryAnchorData.AuraComponent)
            {
                if (ShouldBeActive)
                {
                    PurgatoryAnchorData.AuraComponent->Activate(true);
                }
                else
                {
                    PurgatoryAnchorData.AuraComponent->Deactivate();
                }
            }
            
            // Atualizar parâmetros PCG
            UPCGGraph* PCGGraph = PCGComponent->GetGraph();
            if (PCGGraph)
            {
                PCGGraph->SetBoolData("PurgatoryAnchorActive", PurgatoryAnchorData.IsActive);
            }
        }
    }
    
    // Se não estiver ativo, não continuar com as atualizações visuais
    if (!PurgatoryAnchorData.IsActive)
    {
        return;
    }
    
    // Calcular pulsação de energia baseada no tempo
    float TimeFactor = CurrentTime * PurgatoryAnchorData.PulsationSpeed;
    float EnergyPulsation = 1.0f + FMath::Sin(TimeFactor) * PurgatoryAnchorData.PulsationAmplitude;
    
    // Atualizar variáveis do sistema de partículas
    if (PurgatoryAnchorData.AuraComponent)
    {
        PurgatoryAnchorData.AuraComponent->SetVariableFloat(TEXT("EnergyIntensity"), EnergyPulsation);
    }
    
    // Aplicar rotação lenta à Âncora do Purgatório
    if (PurgatoryAnchorData.AnchorActor)
    {
        FRotator CurrentRotation = PurgatoryAnchorData.AnchorActor->GetActorRotation();
        float RotationDelta = PurgatoryAnchorData.RotationSpeed * GetWorld()->GetDeltaSeconds();
        FRotator NewRotation = CurrentRotation + FRotator(0.0f, RotationDelta, 0.0f);
        PurgatoryAnchorData.AnchorActor->SetActorRotation(NewRotation);
        
        // Aplicar oscilação vertical suave
        FVector CurrentLocation = PurgatoryAnchorData.AnchorActor->GetActorLocation();
        float HeightOffset = FMath::Sin(CurrentTime * 0.5f) * 20.0f; // Oscilação suave de 20 unidades
        FVector NewLocation = FVector(CurrentLocation.X, CurrentLocation.Y, PurgatoryAnchorData.Position.Z + HeightOffset);
        PurgatoryAnchorData.AnchorActor->SetActorLocation(NewLocation);
    }
}