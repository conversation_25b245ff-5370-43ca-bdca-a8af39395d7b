// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGPhaseManager.h"

#ifdef AURACRON_AURACRONPCGPhaseManager_generated_h
#error "AURACRONPCGPhaseManager.generated.h already included, missing '#pragma once' in AURACRONPCGPhaseManager.h"
#endif
#define AURACRON_AURACRONPCGPhaseManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAURACRONMapPhase : uint8;
struct FAURACRONPhaseSettings;

// ********** Begin ScriptStruct FAURACRONPhaseSettings ********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_34_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONPhaseSettings;
// ********** End ScriptStruct FAURACRONPhaseSettings **********************************************

// ********** Begin Delegate FOnMapPhaseChanged ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_162_DELEGATE \
AURACRON_API void FOnMapPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMapPhaseChanged, EAURACRONMapPhase OldPhase, EAURACRONMapPhase NewPhase);


// ********** End Delegate FOnMapPhaseChanged ******************************************************

// ********** Begin Delegate FOnPhaseTransitionProgress ********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_163_DELEGATE \
AURACRON_API void FOnPhaseTransitionProgress_DelegateWrapper(const FMulticastScriptDelegate& OnPhaseTransitionProgress, EAURACRONMapPhase CurrentPhase, float TransitionProgress);


// ********** End Delegate FOnPhaseTransitionProgress **********************************************

// ********** Begin Delegate FOnMapContraction *****************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_164_DELEGATE \
AURACRON_API void FOnMapContraction_DelegateWrapper(const FMulticastScriptDelegate& OnMapContraction, float ContractionFactor, float ContractedRadius);


// ********** End Delegate FOnMapContraction *******************************************************

// ********** Begin Class AAURACRONPCGPhaseManager *************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_174_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnPhaseTimerExpired); \
	DECLARE_FUNCTION(execAcceleratePhaseProgression); \
	DECLARE_FUNCTION(execFixDocumentationComplianceIssues); \
	DECLARE_FUNCTION(execVerifyDocumentationCompliance); \
	DECLARE_FUNCTION(execConfigureEnvironmentsForDeviceType); \
	DECLARE_FUNCTION(execIntegrateSystemsForConvergencePhase); \
	DECLARE_FUNCTION(execApplyConvergencePhaseSettings); \
	DECLARE_FUNCTION(execIntegrateSystemsForAwakeningPhase); \
	DECLARE_FUNCTION(execApplyAwakeningPhaseSettings); \
	DECLARE_FUNCTION(execApplyTemporaryPhaseEffect); \
	DECLARE_FUNCTION(execGetTransitionProgress); \
	DECLARE_FUNCTION(execIsInTransition); \
	DECLARE_FUNCTION(execGetPhaseSettings); \
	DECLARE_FUNCTION(execGetTotalElapsedTime); \
	DECLARE_FUNCTION(execGetTimeRemainingInCurrentPhase); \
	DECLARE_FUNCTION(execGetNextPhase); \
	DECLARE_FUNCTION(execGetCurrentPhase); \
	DECLARE_FUNCTION(execForceTransitionToPhase); \
	DECLARE_FUNCTION(execSetPhaseSystemPaused); \
	DECLARE_FUNCTION(execForcePhaseTransition); \
	DECLARE_FUNCTION(execStopPhaseProgression); \
	DECLARE_FUNCTION(execStartMapPhases); \
	DECLARE_FUNCTION(execStartPhaseProgression); \
	DECLARE_FUNCTION(execConfigureForEntryDevice); \
	DECLARE_FUNCTION(execInitializePhaseSystem);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPhaseManager_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_174_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGPhaseManager(); \
	friend struct Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPhaseManager_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGPhaseManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGPhaseManager_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGPhaseManager)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_174_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGPhaseManager(AAURACRONPCGPhaseManager&&) = delete; \
	AAURACRONPCGPhaseManager(const AAURACRONPCGPhaseManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGPhaseManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGPhaseManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGPhaseManager) \
	NO_API virtual ~AAURACRONPCGPhaseManager();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_171_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_174_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_174_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_174_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h_174_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGPhaseManager;

// ********** End Class AAURACRONPCGPhaseManager ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
