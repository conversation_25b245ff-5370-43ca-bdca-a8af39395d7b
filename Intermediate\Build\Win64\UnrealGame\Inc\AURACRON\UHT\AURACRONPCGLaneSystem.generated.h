// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGLaneSystem.h"

#ifdef AURACRON_AURACRONPCGLaneSystem_generated_h
#error "AURACRONPCGLaneSystem.generated.h already included, missing '#pragma once' in AURACRONPCGLaneSystem.h"
#endif
#define AURACRON_AURACRONPCGLaneSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAURACRONDeviceType : uint8;
enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONLaneType : uint8;
enum class EAURACRONMapPhase : uint8;
struct FAURACRONJungleCamp;
struct FAURACRONLaneInfo;

// ********** Begin ScriptStruct FAURACRONVectorArray **********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_55_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONVectorArray;
// ********** End ScriptStruct FAURACRONVectorArray ************************************************

// ********** Begin ScriptStruct FAURACRONActorArray ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_71_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONActorArray_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONActorArray;
// ********** End ScriptStruct FAURACRONActorArray *************************************************

// ********** Begin ScriptStruct FAURACRONComponentArray *******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_87_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONComponentArray;
// ********** End ScriptStruct FAURACRONComponentArray *********************************************

// ********** Begin ScriptStruct FAURACRONLaneInfo *************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_105_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONLaneInfo;
// ********** End ScriptStruct FAURACRONLaneInfo ***************************************************

// ********** Begin ScriptStruct FAURACRONJungleCamp ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_154_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONJungleCamp;
// ********** End ScriptStruct FAURACRONJungleCamp *************************************************

// ********** Begin Class AAURACRONPCGLaneSystem ***************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_198_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCalculateLaneIntersectionPoints); \
	DECLARE_FUNCTION(execGenerateLaneIntersectionsBasedOnRenderingCapacity); \
	DECLARE_FUNCTION(execGetCurrentEnvironment); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGetClosestLane); \
	DECLARE_FUNCTION(execIsPositionInLane); \
	DECLARE_FUNCTION(execGetJungleCamps); \
	DECLARE_FUNCTION(execGetLanePointsForEnvironment); \
	DECLARE_FUNCTION(execGetLaneInfo); \
	DECLARE_FUNCTION(execGenerateDefensiveStructures); \
	DECLARE_FUNCTION(execGenerateJungleCamps); \
	DECLARE_FUNCTION(execTransitionToEnvironment); \
	DECLARE_FUNCTION(execGenerateLanesForEnvironment); \
	DECLARE_FUNCTION(execGenerateLaneSystem);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGLaneSystem_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_198_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGLaneSystem(); \
	friend struct Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGLaneSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGLaneSystem, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGLaneSystem_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGLaneSystem)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_198_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGLaneSystem(AAURACRONPCGLaneSystem&&) = delete; \
	AAURACRONPCGLaneSystem(const AAURACRONPCGLaneSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGLaneSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGLaneSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGLaneSystem) \
	NO_API virtual ~AAURACRONPCGLaneSystem();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_195_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_198_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_198_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_198_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h_198_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGLaneSystem;

// ********** End Class AAURACRONPCGLaneSystem *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h

// ********** Begin Enum EAURACRONLaneType *********************************************************
#define FOREACH_ENUM_EAURACRONLANETYPE(op) \
	op(EAURACRONLaneType::TopLane) \
	op(EAURACRONLaneType::MidLane) \
	op(EAURACRONLaneType::BotLane) \
	op(EAURACRONLaneType::PrismalFlow) \
	op(EAURACRONLaneType::River) \
	op(EAURACRONLaneType::Jungle) 

enum class EAURACRONLaneType : uint8;
template<> struct TIsUEnumClass<EAURACRONLaneType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONLaneType>();
// ********** End Enum EAURACRONLaneType ***********************************************************

// ********** Begin Enum EAURACRONDefensiveStructure ***********************************************
#define FOREACH_ENUM_EAURACRONDEFENSIVESTRUCTURE(op) \
	op(EAURACRONDefensiveStructure::OuterTower) \
	op(EAURACRONDefensiveStructure::InnerTower) \
	op(EAURACRONDefensiveStructure::InhibitorTower) \
	op(EAURACRONDefensiveStructure::Inhibitor) \
	op(EAURACRONDefensiveStructure::Nexus) 

enum class EAURACRONDefensiveStructure : uint8;
template<> struct TIsUEnumClass<EAURACRONDefensiveStructure> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONDefensiveStructure>();
// ********** End Enum EAURACRONDefensiveStructure *************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
