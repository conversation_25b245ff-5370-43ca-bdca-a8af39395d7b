// AURACRONMovementComponent.cpp
// Sistema de Sígilos AURACRON - Implementação do Componente de Movimento Customizado UE 5.6

#include "Components/AURACRONMovementComponent.h"
#include "Components/AURACRONSigilComponent.h"
#include "Net/UnrealNetwork.h"
#include "GameFramework/Character.h"
#include "Engine/Engine.h"

UAURACRONMovementComponent::UAURACRONMovementComponent()
{
    // Configurações básicas
    CurrentMovementState = EAURACRONMovementState::Normal;
    CurrentEnvironment = EAURACRONEnvironmentType::RadiantPlains;
    
    // Fluxo Prismal
    bIsInPrismalFlow = false;
    PrismalFlowDirection = FVector::ZeroVector;
    PrismalFlowSpeed = 1.0f;
    FlowSpeedMultiplier = 1.5f;
    
    // Dash
    bIsDashing = false;
    DashTimeRemaining = 0.0f;
    DashDirection = FVector::ZeroVector;
    DashSpeed = 0.0f;
    DashCooldown = 3.0f;
    DashCooldownRemaining = 0.0f;
    
    // Configurações padrão de ambiente
    FAURACRONEnvironmentMovementConfig RadiantConfig;
    RadiantConfig.SpeedMultiplier = 1.0f;
    RadiantConfig.AccelerationMultiplier = 1.0f;
    RadiantConfig.JumpForceMultiplier = 1.0f;
    RadiantConfig.bAllowsFlight = false;
    EnvironmentConfigs.Add(EAURACRONEnvironmentType::RadiantPlains, RadiantConfig);
    
    FAURACRONEnvironmentMovementConfig ZephyrConfig;
    ZephyrConfig.SpeedMultiplier = 1.2f;
    ZephyrConfig.AccelerationMultiplier = 1.3f;
    ZephyrConfig.JumpForceMultiplier = 1.5f;
    ZephyrConfig.bAllowsFlight = true;
    EnvironmentConfigs.Add(EAURACRONEnvironmentType::ZephyrFirmament, ZephyrConfig);
    
    FAURACRONEnvironmentMovementConfig PurgatoryConfig;
    PurgatoryConfig.SpeedMultiplier = 0.9f;
    PurgatoryConfig.AccelerationMultiplier = 0.8f;
    PurgatoryConfig.JumpForceMultiplier = 0.8f;
    PurgatoryConfig.bAllowsFlight = false;
    EnvironmentConfigs.Add(EAURACRONEnvironmentType::PurgatoryRealm, PurgatoryConfig);
    
    // Inicializar arrays
    ActiveSpeedModifiers.Empty();
}

void UAURACRONMovementComponent::BeginPlay()
{
    Super::BeginPlay();
    
    // Armazenar valores base
    BaseMaxWalkSpeed = MaxWalkSpeed;
    BaseMaxAcceleration = MaxAcceleration;
    
    // Obter referência ao componente de Sígilos
    if (ACharacter* Character = Cast<ACharacter>(GetOwner()))
    {
        SigilComponent = Character->FindComponentByClass<UAURACRONSigilComponent>();
    }
    
    // Aplicar configurações do ambiente inicial
    ApplyEnvironmentSettings();
}

void UAURACRONMovementComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    // Atualizar apenas no servidor
    if (GetOwner()->HasAuthority())
    {
        UpdateMovementForState(DeltaTime);
        UpdateSpeedModifiers(DeltaTime);
    }
}

void UAURACRONMovementComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    DOREPLIFETIME(UAURACRONMovementComponent, CurrentMovementState);
    DOREPLIFETIME(UAURACRONMovementComponent, CurrentEnvironment);
    DOREPLIFETIME(UAURACRONMovementComponent, bIsInPrismalFlow);
    DOREPLIFETIME(UAURACRONMovementComponent, PrismalFlowDirection);
    DOREPLIFETIME(UAURACRONMovementComponent, PrismalFlowSpeed);
    DOREPLIFETIME(UAURACRONMovementComponent, bIsDashing);
    DOREPLIFETIME(UAURACRONMovementComponent, DashTimeRemaining);
    DOREPLIFETIME(UAURACRONMovementComponent, DashDirection);
    DOREPLIFETIME(UAURACRONMovementComponent, DashSpeed);
    DOREPLIFETIME(UAURACRONMovementComponent, DashCooldownRemaining);
}

float UAURACRONMovementComponent::GetMaxSpeed() const
{
    float Speed = Super::GetMaxSpeed();
    
    // Aplicar multiplicadores baseados no estado
    switch (CurrentMovementState)
    {
        case EAURACRONMovementState::PrismalFlow:
            Speed *= FlowSpeedMultiplier * PrismalFlowSpeed;
            break;
        case EAURACRONMovementState::SigilDash:
            Speed = DashSpeed;
            break;
        case EAURACRONMovementState::Stunned:
        case EAURACRONMovementState::Rooted:
            Speed = 0.0f;
            break;
        default:
            break;
    }
    
    // Aplicar modificadores de ambiente
    const FAURACRONEnvironmentMovementConfig* Config = EnvironmentConfigs.Find(CurrentEnvironment);
    if (Config)
    {
        Speed *= Config->SpeedMultiplier;
    }
    
    // Aplicar modificadores temporários
    Speed *= CalculateTotalSpeedMultiplier();
    
    return Speed;
}

float UAURACRONMovementComponent::GetMaxAcceleration() const
{
    float CurrentAcceleration = Super::GetMaxAcceleration();

    // Aplicar modificadores de ambiente
    const FAURACRONEnvironmentMovementConfig* Config = EnvironmentConfigs.Find(CurrentEnvironment);
    if (Config)
    {
        CurrentAcceleration *= Config->AccelerationMultiplier;
    }

    return CurrentAcceleration;
}

float UAURACRONMovementComponent::GetMaxBrakingDeceleration() const
{
    float Deceleration = Super::GetMaxBrakingDeceleration();
    
    // Aplicar modificadores baseados no estado
    if (CurrentMovementState == EAURACRONMovementState::PrismalFlow)
    {
        Deceleration *= 0.5f; // Menos frenagem no fluxo
    }
    
    return Deceleration;
}

void UAURACRONMovementComponent::SetMovementState_Implementation(EAURACRONMovementState NewState)
{
    if (CurrentMovementState == NewState)
    {
        return;
    }
    
    EAURACRONMovementState OldState = CurrentMovementState;
    CurrentMovementState = NewState;
    
    // Chamar evento
    OnMovementStateChanged(OldState, NewState);
}

EAURACRONMovementState UAURACRONMovementComponent::GetMovementState() const
{
    return CurrentMovementState;
}

void UAURACRONMovementComponent::SetCurrentEnvironment_Implementation(EAURACRONEnvironmentType Environment)
{
    if (CurrentEnvironment == Environment)
    {
        return;
    }
    
    CurrentEnvironment = Environment;
    ApplyEnvironmentSettings();
}

EAURACRONEnvironmentType UAURACRONMovementComponent::GetCurrentEnvironment() const
{
    return CurrentEnvironment;
}

void UAURACRONMovementComponent::EnterPrismalFlow_Implementation(FVector FlowDirection, float FlowSpeed)
{
    bIsInPrismalFlow = true;
    PrismalFlowDirection = FlowDirection.GetSafeNormal();
    PrismalFlowSpeed = FMath::Max(FlowSpeed, 0.1f);
    
    SetMovementState(EAURACRONMovementState::PrismalFlow);
    
    // Chamar evento
    OnEnteredPrismalFlow(PrismalFlowDirection, PrismalFlowSpeed);
}

void UAURACRONMovementComponent::ExitPrismalFlow_Implementation()
{
    if (!bIsInPrismalFlow)
    {
        return;
    }
    
    bIsInPrismalFlow = false;
    PrismalFlowDirection = FVector::ZeroVector;
    PrismalFlowSpeed = 1.0f;
    
    SetMovementState(EAURACRONMovementState::Normal);
    
    // Chamar evento
    OnExitedPrismalFlow();
}

bool UAURACRONMovementComponent::IsInPrismalFlow() const
{
    return bIsInPrismalFlow;
}

void UAURACRONMovementComponent::PerformSigilDash_Implementation(FVector Direction, float Distance, float Duration)
{
    if (!CanPerformDash())
    {
        return;
    }
    
    // Configurar dash
    bIsDashing = true;
    DashTimeRemaining = Duration;
    DashDirection = Direction.GetSafeNormal();
    DashSpeed = Distance / Duration;
    DashCooldownRemaining = DashCooldown;
    
    SetMovementState(EAURACRONMovementState::SigilDash);
    
    // Chamar evento
    OnSigilDashPerformed(Direction, Distance);
}

bool UAURACRONMovementComponent::CanPerformDash() const
{
    return DashCooldownRemaining <= 0.0f && !bIsDashing && 
           CurrentMovementState != EAURACRONMovementState::Stunned &&
           CurrentMovementState != EAURACRONMovementState::Rooted;
}

void UAURACRONMovementComponent::ApplySpeedModifier_Implementation(float Multiplier, float Duration, FName ModifierName)
{
    // Remover modificador existente com o mesmo nome
    RemoveSpeedModifier(ModifierName);
    
    // Adicionar novo modificador
    ActiveSpeedModifiers.Add(FSpeedModifier(Multiplier, Duration, ModifierName));
}

void UAURACRONMovementComponent::RemoveSpeedModifier_Implementation(FName ModifierName)
{
    ActiveSpeedModifiers.RemoveAll([ModifierName](const FSpeedModifier& Modifier)
    {
        return Modifier.Name == ModifierName;
    });
}

float UAURACRONMovementComponent::GetModifiedSpeed() const
{
    return GetMaxSpeed();
}

void UAURACRONMovementComponent::UpdateMovementForState(float DeltaTime)
{
    // Atualizar dash
    if (bIsDashing)
    {
        DashTimeRemaining = FMath::Max(0.0f, DashTimeRemaining - DeltaTime);
        if (DashTimeRemaining <= 0.0f)
        {
            bIsDashing = false;
            SetMovementState(EAURACRONMovementState::Normal);
        }
    }
    
    // Atualizar cooldown do dash
    if (DashCooldownRemaining > 0.0f)
    {
        DashCooldownRemaining = FMath::Max(0.0f, DashCooldownRemaining - DeltaTime);
    }
}

void UAURACRONMovementComponent::UpdateSpeedModifiers(float DeltaTime)
{
    // Atualizar tempo dos modificadores
    for (int32 i = ActiveSpeedModifiers.Num() - 1; i >= 0; i--)
    {
        ActiveSpeedModifiers[i].TimeRemaining -= DeltaTime;
        if (ActiveSpeedModifiers[i].TimeRemaining <= 0.0f)
        {
            ActiveSpeedModifiers.RemoveAt(i);
        }
    }
}

float UAURACRONMovementComponent::CalculateTotalSpeedMultiplier() const
{
    float TotalMultiplier = 1.0f;
    
    for (const FSpeedModifier& Modifier : ActiveSpeedModifiers)
    {
        TotalMultiplier *= Modifier.Multiplier;
    }
    
    return TotalMultiplier;
}

void UAURACRONMovementComponent::ApplyEnvironmentSettings()
{
    const FAURACRONEnvironmentMovementConfig* Config = EnvironmentConfigs.Find(CurrentEnvironment);
    if (!Config)
    {
        return;
    }
    
    // Aplicar configurações do ambiente
    MaxWalkSpeed = BaseMaxWalkSpeed * Config->SpeedMultiplier;
    MaxAcceleration = BaseMaxAcceleration * Config->AccelerationMultiplier;
    JumpZVelocity = 600.0f * Config->JumpForceMultiplier;
    
    // Configurar voo se permitido
    if (Config->bAllowsFlight)
    {
        SetMovementMode(MOVE_Flying);
    }
    else if (MovementMode == MOVE_Flying)
    {
        SetMovementMode(MOVE_Walking);
    }
}

void UAURACRONMovementComponent::OnRep_MovementState()
{
    // Lógica adicional quando o estado é replicado
}

void UAURACRONMovementComponent::OnRep_CurrentEnvironment()
{
    ApplyEnvironmentSettings();
}
