// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Sígilos MOBA 5x5
// Arquivo: SigilNetworkConfig.cpp
// Descrição: Implementação das configurações de rede otimizadas

#include "Multiplayer/SigilNetworkConfig.h"
#include "GameplayTagsManager.h"
#include "Engine/Engine.h"

// Constantes
const float USigilNetworkConfig::MIN_REPLICATION_FREQUENCY = 1.0f;
const float USigilNetworkConfig::MAX_REPLICATION_FREQUENCY = 120.0f;
const float USigilNetworkConfig::MIN_DISTANCE = 100.0f;
const float USigilNetworkConfig::MAX_DISTANCE = 50000.0f;
const int32 USigilNetworkConfig::MIN_PLAYERS = 2;
const int32 USigilNetworkConfig::MAX_PLAYERS = 20;

USigilNetworkConfig::USigilNetworkConfig()
{
    // Configurações padrão para MOBA 5x5
    OptimizationType = ESigilNetworkOptimization::MOBA;
    MaxPlayers = 10;
    bEnableNetworkOptimizations = true;
    bEnableDebugLogging = false;
    
    // Configurações MOBA específicas
    bPrioritizeTeammates = true;
    bReduceEnemyDetails = true;
    bOptimizeForObjectives = true;
    ObjectiveReplicationRadius = 2000.0f;
    
    // Aplicar otimizações MOBA por padrão
    ApplyMOBAOptimizations();
    
    // Configurar tags de prioridade
    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Sigil.Fusion.Active")))
    {
        HighPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Fusion.Active")));
    }
    
    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Sigil.Equipped")))
    {
        HighPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Equipped")));
    }
    
    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("MOBA.TeamFight")))
    {
        CriticalTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("MOBA.TeamFight")));
    }
    
    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Sigil.VFX")))
    {
        LowPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.VFX")));
    }
}

void USigilNetworkConfig::ApplyOptimizationType(ESigilNetworkOptimization NewType)
{
    OptimizationType = NewType;
    
    switch (NewType)
    {
        case ESigilNetworkOptimization::None:
            ApplyNoOptimizations();
            break;
            
        case ESigilNetworkOptimization::Basic:
            ApplyBasicOptimizations();
            break;
            
        case ESigilNetworkOptimization::MOBA:
            ApplyMOBAOptimizations();
            break;
            
        case ESigilNetworkOptimization::Competitive:
            ApplyCompetitiveOptimizations();
            break;
            
        case ESigilNetworkOptimization::Custom:
            // Manter configurações atuais
            break;
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilNetworkConfig: Applied optimization type %d"), (int32)NewType);
}

void USigilNetworkConfig::SetMOBAOptimizations(bool bEnable)
{
    bPrioritizeTeammates = bEnable;
    bReduceEnemyDetails = bEnable;
    bOptimizeForObjectives = bEnable;
    
    if (bEnable)
    {
        ApplyMOBAOptimizations();
    }
    else
    {
        ApplyBasicOptimizations();
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilNetworkConfig: MOBA optimizations %s"), 
        bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void USigilNetworkConfig::SetMaxPlayers(int32 NewMaxPlayers)
{
    MaxPlayers = FMath::Clamp(NewMaxPlayers, MIN_PLAYERS, MAX_PLAYERS);
    
    // Ajustar configurações baseadas no número de jogadores
    if (MaxPlayers <= 4)
    {
        // Configurações para jogos pequenos
        FrequencySettings.BaseReplicationFrequency = 30.0f;
        BandwidthSettings.MaxBandwidthPerPlayer = 100.0f;
    }
    else if (MaxPlayers <= 10)
    {
        // Configurações para MOBA 5x5
        FrequencySettings.BaseReplicationFrequency = 20.0f;
        BandwidthSettings.MaxBandwidthPerPlayer = 50.0f;
    }
    else
    {
        // Configurações para jogos grandes
        FrequencySettings.BaseReplicationFrequency = 15.0f;
        BandwidthSettings.MaxBandwidthPerPlayer = 30.0f;
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilNetworkConfig: Max players set to %d"), MaxPlayers);
}

void USigilNetworkConfig::ResetToDefaults()
{
    // Resetar para configurações MOBA padrão
    OptimizationType = ESigilNetworkOptimization::MOBA;
    MaxPlayers = 10;
    bEnableNetworkOptimizations = true;
    bEnableDebugLogging = false;
    
    ApplyMOBAOptimizations();
    
    UE_LOG(LogTemp, Log, TEXT("SigilNetworkConfig: Reset to default MOBA settings"));
}

float USigilNetworkConfig::GetPriorityForTag(FGameplayTag Tag) const
{
    if (CriticalTags.HasTag(Tag))
    {
        return 10.0f; // Prioridade máxima
    }
    else if (HighPriorityTags.HasTag(Tag))
    {
        return 7.0f; // Prioridade alta
    }
    else if (LowPriorityTags.HasTag(Tag))
    {
        return 2.0f; // Prioridade baixa
    }
    
    return 5.0f; // Prioridade padrão
}

float USigilNetworkConfig::GetDistanceMultiplier(float Distance) const
{
    if (Distance <= DistanceSettings.LowPriorityDistance)
    {
        return 1.0f; // Multiplicador completo
    }
    else if (Distance <= DistanceSettings.MediumPriorityDistance)
    {
        return 0.7f; // Multiplicador médio
    }
    else if (Distance <= DistanceSettings.MaxReplicationDistance)
    {
        return 0.4f; // Multiplicador baixo
    }
    else if (Distance <= DistanceSettings.CullingDistance)
    {
        return 0.1f; // Multiplicador mínimo
    }
    
    return 0.0f; // Sem replicação
}

float USigilNetworkConfig::GetFrequencyForPriority(float Priority) const
{
    if (Priority >= 8.0f)
    {
        return FrequencySettings.CriticalDataFrequency;
    }
    else if (Priority >= 5.0f)
    {
        return FrequencySettings.BaseReplicationFrequency;
    }
    else
    {
        return FrequencySettings.LowPriorityFrequency;
    }
}

bool USigilNetworkConfig::ShouldReplicateAtDistance(float Distance) const
{
    return Distance <= DistanceSettings.CullingDistance;
}

bool USigilNetworkConfig::ValidateConfiguration() const
{
    return ValidatePriorities() && ValidateDistances() && ValidateFrequencies() && ValidateBandwidth();
}

TArray<FString> USigilNetworkConfig::GetConfigurationWarnings() const
{
    TArray<FString> Warnings;
    
    if (!ValidatePriorities())
    {
        Warnings.Add(TEXT("Invalid priority settings detected"));
    }
    
    if (!ValidateDistances())
    {
        Warnings.Add(TEXT("Invalid distance settings detected"));
    }
    
    if (!ValidateFrequencies())
    {
        Warnings.Add(TEXT("Invalid frequency settings detected"));
    }
    
    if (!ValidateBandwidth())
    {
        Warnings.Add(TEXT("Invalid bandwidth settings detected"));
    }
    
    if (MaxPlayers > 10 && OptimizationType == ESigilNetworkOptimization::MOBA)
    {
        Warnings.Add(TEXT("MOBA optimization is designed for 10 players or less"));
    }
    
    if (BandwidthSettings.MaxBandwidthPerPlayer * MaxPlayers > 1000.0f)
    {
        Warnings.Add(TEXT("Total bandwidth usage may be too high"));
    }
    
    return Warnings;
}

float USigilNetworkConfig::GetEstimatedBandwidthUsage() const
{
    // Estimativa baseada em configurações atuais
    float BaseBandwidth = BandwidthSettings.MaxBandwidthPerPlayer * MaxPlayers;
    
    // Aplicar multiplicadores baseados em otimizações
    switch (OptimizationType)
    {
        case ESigilNetworkOptimization::None:
            return BaseBandwidth;
            
        case ESigilNetworkOptimization::Basic:
            return BaseBandwidth * 0.8f;
            
        case ESigilNetworkOptimization::MOBA:
            return BaseBandwidth * 0.6f;
            
        case ESigilNetworkOptimization::Competitive:
            return BaseBandwidth * 0.5f;
            
        case ESigilNetworkOptimization::Custom:
            return BaseBandwidth * 0.7f;
    }
    
    return BaseBandwidth;
}

int32 USigilNetworkConfig::GetEstimatedReplicationsPerSecond() const
{
    // Estimativa baseada em frequência e número de jogadores
    float BaseReplications = FrequencySettings.BaseReplicationFrequency * MaxPlayers;
    
    // Considerar diferentes tipos de dados
    float CriticalReplications = FrequencySettings.CriticalDataFrequency * MaxPlayers * 0.1f; // 10% críticos
    float LowPriorityReplications = FrequencySettings.LowPriorityFrequency * MaxPlayers * 0.3f; // 30% baixa prioridade
    
    return FMath::RoundToInt(BaseReplications + CriticalReplications + LowPriorityReplications);
}

USigilNetworkConfig* USigilNetworkConfig::GetSigilNetworkConfig()
{
    return GetMutableDefault<USigilNetworkConfig>();
}

// Configurações predefinidas
void USigilNetworkConfig::ApplyBasicOptimizations()
{
    // Configurações básicas de otimização
    ReplicationPriorities.EquippedSigilPriority = 5.0f;
    ReplicationPriorities.FusionPriority = 7.0f;
    ReplicationPriorities.SystemStatsPriority = 3.0f;
    ReplicationPriorities.VFXPriority = 2.0f;
    ReplicationPriorities.TeammatePriority = 5.0f;
    ReplicationPriorities.EnemyPriority = 5.0f;
    
    DistanceSettings.MaxReplicationDistance = 6000.0f;
    DistanceSettings.MediumPriorityDistance = 4000.0f;
    DistanceSettings.LowPriorityDistance = 2000.0f;
    DistanceSettings.CullingDistance = 12000.0f;
    
    FrequencySettings.BaseReplicationFrequency = 20.0f;
    FrequencySettings.CriticalDataFrequency = 40.0f;
    FrequencySettings.LowPriorityFrequency = 10.0f;
    FrequencySettings.OptimizationInterval = 2.0f;
    
    BandwidthSettings.MaxBandwidthPerPlayer = 60.0f;
    BandwidthSettings.MaxReplicationsPerFrame = 40;
    BandwidthSettings.bUseCompression = true;
    BandwidthSettings.bUseDeltaCompression = false;
}

void USigilNetworkConfig::ApplyMOBAOptimizations()
{
    // Configurações otimizadas para MOBA 5x5
    ReplicationPriorities.EquippedSigilPriority = 6.0f;
    ReplicationPriorities.FusionPriority = 8.0f;
    ReplicationPriorities.SystemStatsPriority = 3.0f;
    ReplicationPriorities.VFXPriority = 2.0f;
    ReplicationPriorities.TeammatePriority = 7.0f; // Priorizar companheiros de equipe
    ReplicationPriorities.EnemyPriority = 4.0f; // Reduzir detalhes de inimigos
    
    DistanceSettings.MaxReplicationDistance = 5000.0f; // Distância típica de MOBA
    DistanceSettings.MediumPriorityDistance = 3000.0f;
    DistanceSettings.LowPriorityDistance = 1500.0f;
    DistanceSettings.CullingDistance = 10000.0f;
    
    FrequencySettings.BaseReplicationFrequency = 20.0f;
    FrequencySettings.CriticalDataFrequency = 60.0f; // Alta frequência para dados críticos
    FrequencySettings.LowPriorityFrequency = 5.0f;
    FrequencySettings.OptimizationInterval = 1.0f; // Otimização mais frequente
    
    BandwidthSettings.MaxBandwidthPerPlayer = 50.0f;
    BandwidthSettings.MaxReplicationsPerFrame = 50;
    BandwidthSettings.bUseCompression = true;
    BandwidthSettings.bUseDeltaCompression = true; // Usar delta compression
    
    // Configurações específicas para MOBA
    bPrioritizeTeammates = true;
    bReduceEnemyDetails = true;
    bOptimizeForObjectives = true;
    ObjectiveReplicationRadius = 2000.0f;
}

void USigilNetworkConfig::ApplyCompetitiveOptimizations()
{
    // Configurações para modo competitivo (máxima precisão)
    ReplicationPriorities.EquippedSigilPriority = 8.0f;
    ReplicationPriorities.FusionPriority = 9.0f;
    ReplicationPriorities.SystemStatsPriority = 6.0f;
    ReplicationPriorities.VFXPriority = 3.0f;
    ReplicationPriorities.TeammatePriority = 8.0f;
    ReplicationPriorities.EnemyPriority = 7.0f; // Manter detalhes de inimigos
    
    DistanceSettings.MaxReplicationDistance = 7000.0f; // Maior alcance
    DistanceSettings.MediumPriorityDistance = 5000.0f;
    DistanceSettings.LowPriorityDistance = 2500.0f;
    DistanceSettings.CullingDistance = 15000.0f;
    
    FrequencySettings.BaseReplicationFrequency = 30.0f; // Maior frequência
    FrequencySettings.CriticalDataFrequency = 120.0f; // Frequência máxima
    FrequencySettings.LowPriorityFrequency = 10.0f;
    FrequencySettings.OptimizationInterval = 0.5f; // Otimização muito frequente
    
    BandwidthSettings.MaxBandwidthPerPlayer = 100.0f; // Maior bandwidth
    BandwidthSettings.MaxReplicationsPerFrame = 100;
    BandwidthSettings.bUseCompression = true;
    BandwidthSettings.bUseDeltaCompression = true;
    
    // Configurações competitivas
    bPrioritizeTeammates = false; // Tratar todos igualmente
    bReduceEnemyDetails = false;
    bOptimizeForObjectives = true;
    ObjectiveReplicationRadius = 3000.0f; // Maior raio para objetivos
}

void USigilNetworkConfig::ApplyNoOptimizations()
{
    // Sem otimizações (máxima qualidade, máximo uso de rede)
    ReplicationPriorities.EquippedSigilPriority = 10.0f;
    ReplicationPriorities.FusionPriority = 10.0f;
    ReplicationPriorities.SystemStatsPriority = 10.0f;
    ReplicationPriorities.VFXPriority = 10.0f;
    ReplicationPriorities.TeammatePriority = 10.0f;
    ReplicationPriorities.EnemyPriority = 10.0f;
    
    DistanceSettings.MaxReplicationDistance = 20000.0f;
    DistanceSettings.MediumPriorityDistance = 15000.0f;
    DistanceSettings.LowPriorityDistance = 10000.0f;
    DistanceSettings.CullingDistance = 50000.0f;
    
    FrequencySettings.BaseReplicationFrequency = 60.0f;
    FrequencySettings.CriticalDataFrequency = 120.0f;
    FrequencySettings.LowPriorityFrequency = 30.0f;
    FrequencySettings.OptimizationInterval = 5.0f;
    
    BandwidthSettings.MaxBandwidthPerPlayer = 200.0f;
    BandwidthSettings.MaxReplicationsPerFrame = 200;
    BandwidthSettings.bUseCompression = false;
    BandwidthSettings.bUseDeltaCompression = false;
    
    bPrioritizeTeammates = false;
    bReduceEnemyDetails = false;
    bOptimizeForObjectives = false;
}

// Validação interna
bool USigilNetworkConfig::ValidatePriorities() const
{
    return ReplicationPriorities.EquippedSigilPriority >= 0.0f && ReplicationPriorities.EquippedSigilPriority <= 10.0f &&
           ReplicationPriorities.FusionPriority >= 0.0f && ReplicationPriorities.FusionPriority <= 10.0f &&
           ReplicationPriorities.SystemStatsPriority >= 0.0f && ReplicationPriorities.SystemStatsPriority <= 10.0f &&
           ReplicationPriorities.VFXPriority >= 0.0f && ReplicationPriorities.VFXPriority <= 10.0f &&
           ReplicationPriorities.TeammatePriority >= 0.0f && ReplicationPriorities.TeammatePriority <= 10.0f &&
           ReplicationPriorities.EnemyPriority >= 0.0f && ReplicationPriorities.EnemyPriority <= 10.0f;
}

bool USigilNetworkConfig::ValidateDistances() const
{
    return DistanceSettings.LowPriorityDistance >= MIN_DISTANCE &&
           DistanceSettings.MediumPriorityDistance >= DistanceSettings.LowPriorityDistance &&
           DistanceSettings.MaxReplicationDistance >= DistanceSettings.MediumPriorityDistance &&
           DistanceSettings.CullingDistance >= DistanceSettings.MaxReplicationDistance &&
           DistanceSettings.CullingDistance <= MAX_DISTANCE;
}

bool USigilNetworkConfig::ValidateFrequencies() const
{
    return FrequencySettings.LowPriorityFrequency >= MIN_REPLICATION_FREQUENCY &&
           FrequencySettings.BaseReplicationFrequency >= FrequencySettings.LowPriorityFrequency &&
           FrequencySettings.CriticalDataFrequency >= FrequencySettings.BaseReplicationFrequency &&
           FrequencySettings.CriticalDataFrequency <= MAX_REPLICATION_FREQUENCY &&
           FrequencySettings.OptimizationInterval >= 0.1f && FrequencySettings.OptimizationInterval <= 10.0f;
}

bool USigilNetworkConfig::ValidateBandwidth() const
{
    return BandwidthSettings.MaxBandwidthPerPlayer >= 1.0f && BandwidthSettings.MaxBandwidthPerPlayer <= 1000.0f &&
           BandwidthSettings.MaxReplicationsPerFrame >= 1 && BandwidthSettings.MaxReplicationsPerFrame <= 200 &&
           BandwidthSettings.MaxPacketSize >= 512 && BandwidthSettings.MaxPacketSize <= 8192;
}