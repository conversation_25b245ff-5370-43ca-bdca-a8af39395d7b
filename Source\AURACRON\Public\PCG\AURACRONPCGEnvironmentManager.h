// AURACRONPCGEnvironmentManager.h
// Sistema de Gerenciamento dos 3 Ambientes Dinâmicos do AURACRON - UE 5.6
// <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Zephyr, Reino Purgatório com rotação automática

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGLaneSystem.h"
#include "PCG/AURACRONPCGJungleSystem.h"
#include "PCG/AURACRONPCGObjectiveSystem.h"
#include "PCG/AURACRONPCGPortal.h"
#include "Components/StaticMeshComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Components/PostProcessComponent.h"
#include "Engine/DirectionalLight.h"
#include "Engine/SkyLight.h"
#include "AURACRONPCGEnvironmentManager.generated.h"

// Forward declarations
class AAURACRONPCGEnvironment;
class AAURACRONPCGPortal;

/**
 * Estrutura wrapper para array de ambientes (resolve problema UHT com TMap<Enum, TArray<Type>>)
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONEnvironmentArray
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<AAURACRONPCGEnvironment*> Environments;

    FAURACRONEnvironmentArray()
    {
    }
};

/**
 * Estrutura para armazenar destinos de teletransporte para portais táticos
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONTeleportDestinations
{
    GENERATED_BODY()

    /** Localizações de destino para teletransporte */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FVector> Locations;
    
    /** Rotações de destino para teletransporte */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FRotator> Rotations;

    FAURACRONTeleportDestinations()
    {
    }
};

/**
 * Configurações de ambiente para diferentes tipos de dispositivos
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONDeviceEnvironmentSettings
{
    GENERATED_BODY()
    
    /** Se é um dispositivo de entrada (baixo desempenho) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsEntryDevice;
    
    /** Se todos os ambientes estão acessíveis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bAllEnvironmentsAccessible;
    
    /** Se os ambientes não ativos devem ser mostrados como "preview zones" */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bShowPreviewZones;
    
    FAURACRONDeviceEnvironmentSettings()
        : bIsEntryDevice(false)
        , bAllEnvironmentsAccessible(true)
        , bShowPreviewZones(false)
    {
    }
};

/**
 * Informações específicas de um ambiente
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONEnvironmentSettings
{
    GENERATED_BODY()

    /** Tipo do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONEnvironmentType EnvironmentType;
    
    /** Nome do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString EnvironmentName;
    
    /** Descrição das características */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString Description;
    
    /** Altura base do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float BaseHeight;
    
    /** Cor da iluminação ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor AmbientLightColor;
    
    /** Intensidade da iluminação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float LightIntensity;
    
    /** Cor do fog/neblina */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor FogColor;
    
    /** Densidade do fog */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FogDensity;
    
    /** Configurações de post-processing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<FString, float> PostProcessSettings;
    
    /** Efeitos de partículas específicos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> ParticleEffects;
    
    /** Sons ambiente específicos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> AmbientSounds;
    
    /** Mecânicas únicas do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<FString, float> UniqueMechanics;
    
    /** Duração do ambiente em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float EnvironmentDuration;
    
    /** Se o ambiente está ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsActive;
    
    FAURACRONEnvironmentSettings()
        : EnvironmentType(EAURACRONEnvironmentType::RadiantPlains)
        , EnvironmentName(TEXT("Planície Radiante"))
        , Description(TEXT("Planícies terrestres com vegetação exuberante"))
        , BaseHeight(0.0f)
        , AmbientLightColor(FLinearColor::White)
        , LightIntensity(1.0f)
        , FogColor(FLinearColor::White)
        , FogDensity(0.1f)
        , EnvironmentDuration(480.0f)
        , bIsActive(false)
    {
    }
};

/**
 * Gerenciador global dos 3 ambientes dinâmicos do AURACRON
 *
 * RESPONSABILIDADES CLARIFICADAS:
 * ================================
 *
 * ESTA CLASSE (AURACRONPCGEnvironmentManager) - GERENCIADOR GLOBAL:
 * - Orquestra a rotação automática entre os 3 ambientes principais
 * - Gerencia transições suaves e timing entre ambientes
 * - Coordena múltiplas instâncias de AURACRONPCGEnvironment
 * - Aplica efeitos globais (iluminação, pós-processamento, fog, skybox)
 * - Integra com outros sistemas (PhaseManager, ObjectiveSystem, LaneSystem)
 * - Controla configurações de ambiente por fase do mapa
 * - Gerencia efeitos temporários globais
 * - Notifica todos os sistemas sobre mudanças de ambiente
 * - Controla replicação de estado para multiplayer
 *
 * AURACRONPCGEnvironment - ATOR INDIVIDUAL:
 * - Representa uma instância específica de ambiente local
 * - Executa geração PCG de elementos específicos
 * - Responde a comandos do Manager
 * - Gerencia características locais do ambiente
 *
 * RELAÇÃO: Este Manager (1) -> Environment Instances (N)
 * O Manager é o "maestro", os Environments são os "músicos".
 */
UCLASS()
class AURACRON_API AAURACRONPCGEnvironmentManager : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGEnvironmentManager();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // ========================================
    // FUNÇÕES PÚBLICAS
    // ========================================
    
    /** Inicializar o sistema de ambientes */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void InitializeEnvironmentSystem();
    
    /** Configurar ambientes para dispositivo específico (Entry, Mid, High) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureForDeviceType(bool bIsEntryDevice);
    
    /** Configurar para Fase 1: Despertar */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureForAwakeningPhase(bool bIsEntryDevice);
    
    /** Configurar para Fase 2: Convergência */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureForConvergencePhase(bool bIsEntryDevice, bool bIsMidDevice, bool bIsHighDevice);
    
    /** Configurar transição suave para Firmamento Zephyr (dispositivos Entry) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureSmoothTransitionToZephyr(bool bShowAbyssPreview);
    
    /** Configurar 2 ambientes simultâneos com transições simplificadas (dispositivos Mid) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureSimultaneousEnvironments(bool bSimplifiedTransitions);
    
    /** Configurar fronteiras confusas entre ambientes (dispositivos High) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureBlurredEnvironmentBoundaries(float BlurIntensity);
    
    /** Validar integração com o PCGPhaseManager */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    bool ValidatePhaseManagerIntegration();
    
    /** Corrigir problemas de integração com o PCGPhaseManager */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void FixPhaseManagerIntegrationIssues();
    
    /** Verifica e garante que as Ilhas Santuário sejam distribuídas em seções calmas do fluxo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ValidateSanctuaryIslandDistribution();
    
    /** Retorna o número atual de Ilhas Santuário no ambiente */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    int32 GetCurrentSanctuaryIslandCount() const;
    
    /** Iniciar rotação automática dos ambientes */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void StartEnvironmentRotation();
    
    /** Parar rotação automática */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void StopEnvironmentRotation();
    
    /** Forçar transição para ambiente específico */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ForceTransitionToEnvironment(EAURACRONEnvironmentType TargetEnvironment, float TransitionDuration = 30.0f);
    
    /** Aplicar vantagens táticas do mapa atual aos jogadores */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ApplyMapTacticalAdvantages(AActor* TargetActor);
    
    /** Remover vantagens táticas do mapa anterior */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void RemoveMapTacticalAdvantages(AActor* TargetActor);
    
    /** Obter vantagens táticas do mapa atual */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    FAURACRONMapTacticalAdvantages GetCurrentMapTacticalAdvantages() const;
    
    /** Obter ambiente atualmente ativo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    EAURACRONEnvironmentType GetCurrentEnvironment() const { return CurrentEnvironment; }
    
    /** Obter próximo ambiente na rotação */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    EAURACRONEnvironmentType GetNextEnvironment() const;
    
    /** Obter tempo restante no ambiente atual */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    float GetTimeRemainingInCurrentEnvironment() const;
    
    /** Obter progresso da transição atual (0.0 - 1.0) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    float GetTransitionProgress() const;
    
    /** Verificar se está em transição */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    bool IsInTransition() const { return bIsInTransition; }
    
    /** Obter configurações de um ambiente */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    FAURACRONEnvironmentSettings GetEnvironmentSettings(EAURACRONEnvironmentType Environment) const;
    
    /** Atualizar para nova fase do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);
    
    /** Aplicar efeitos especiais temporários */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ApplyTemporaryEnvironmentEffect(const FString& EffectName, float Duration);

    // ========================================
    // FUNÇÕES DE INTEGRAÇÃO COM ENVIRONMENT INSTANCES
    // ========================================

    /** Registrar uma instância de ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void RegisterEnvironmentInstance(AAURACRONPCGEnvironment* EnvironmentInstance);

    /** Desregistrar uma instância de ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void UnregisterEnvironmentInstance(AAURACRONPCGEnvironment* EnvironmentInstance);

    /** Obter todas as instâncias de um tipo de ambiente */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    TArray<AAURACRONPCGEnvironment*> GetEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType) const;

    /** Ativar todas as instâncias de um ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ActivateEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType);

    /** Desativar todas as instâncias de um ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void DeactivateEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType);

    /** Aplicar transição a todas as instâncias */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ApplyTransitionToInstances(EAURACRONEnvironmentType EnvironmentType, float TransitionProgress, bool bFadingIn);

protected:
    // ========================================
    // CONFIGURAÇÕES
    // ========================================
    
    /** Configurações dos 3 ambientes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    TMap<EAURACRONEnvironmentType, FAURACRONEnvironmentSettings> EnvironmentSettings;
    
    /** Número total de Ilhas Santuário a serem geradas (conforme documentação: 8) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager", meta = (ClampMin = "8", ClampMax = "8", UIMin = "8", UIMax = "8"))
    int32 TotalSanctuaryIslands = 8;
    
    /** Configurações de vantagens táticas para cada tipo de mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    TMap<EAURACRONEnvironmentType, FAURACRONMapTacticalAdvantages> MapTacticalAdvantages;
    
    /** Atores afetados pelas vantagens táticas do mapa atual */
    UPROPERTY()
    TArray<AActor*> ActorsWithTacticalAdvantages;
    
    /** Referências aos sistemas integrados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    AAURACRONPCGLaneSystem* LaneSystem;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    AAURACRONPCGJungleSystem* JungleSystem;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    AAURACRONPCGObjectiveSystem* ObjectiveSystem;
    
    /** Componentes de iluminação */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|EnvironmentManager")
    UDirectionalLightComponent* DirectionalLight;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|EnvironmentManager")
    USkyLightComponent* SkyLight;
    
    /** Componente de post-processing */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|EnvironmentManager")
    UPostProcessComponent* PostProcessComponent;
    
    /** Se deve iniciar rotação automaticamente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    bool bAutoStartRotation;
    
    /** Se a rotação está ativa */
    UPROPERTY(BlueprintReadOnly, Category = "AURACRON|EnvironmentManager")
    bool bRotationActive;

private:
    // ========================================
    // ESTADO INTERNO
    // ========================================
    
    /** Ambiente atualmente ativo */
    UPROPERTY()
    EAURACRONEnvironmentType CurrentEnvironment;
    
    /** Ambiente de destino durante transição */
    UPROPERTY()
    EAURACRONEnvironmentType TargetEnvironment;
    
    /** Se está em transição */
    UPROPERTY()
    bool bIsInTransition;
    
    /** Tempo restante no ambiente atual */
    UPROPERTY()
    float TimeRemainingInEnvironment;
    
    /** Duração da transição atual */
    UPROPERTY()
    float CurrentTransitionDuration;
    
    /** Progresso da transição atual (0.0 - 1.0) */
    UPROPERTY()
    float TransitionProgress;
    
    /** Timer para rotação de ambientes */
    UPROPERTY()
    FTimerHandle EnvironmentRotationTimer;
    
    /** Timer para transições */
    UPROPERTY()
    FTimerHandle TransitionTimer;
    
    /** Fase atual do mapa */
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;
    
    /** Efeitos temporários ativos */
    UPROPERTY()
    TMap<FString, float> ActiveTemporaryEffects;

    /** Instâncias de ambiente registradas por tipo */
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, FAURACRONEnvironmentArray> RegisteredEnvironmentInstances;

    /** Mapa de instâncias para compatibilidade com código existente */
    TMap<EAURACRONEnvironmentType, TArray<AAURACRONPCGEnvironment*>> EnvironmentInstances;

    /** Portais táticos por ambiente (não serializado devido a limitações do TWeakObjectPtr em TArray) */
    TMap<EAURACRONEnvironmentType, TArray<TWeakObjectPtr<AAURACRONPCGPortal>>> TacticalPortals;
    
    /** Distribui as Ilhas Santuário em seções calmas do fluxo */
    void DistributeSanctuaryIslands();
    
    /** Identifica seções calmas do fluxo para posicionar Ilhas Santuário */
    TArray<FPrismalFlowSegment*> FindCalmFlowSections();
    
    /** Destinos de teletransporte por ambiente */
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, FAURACRONTeleportDestinations> TeleportDestinations;

    // ========================================
// FUNÇÕES DE GERENCIAMENTO DE PORTAIS TÁTICOS
// ========================================

/** Criar portais de posicionamento tático */
UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
void CreateTacticalPortals();

/** Atualizar portais de posicionamento */
UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
void UpdateTacticalPortals();

/** Ativar portais para um ambiente específico */
UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
void ActivatePortalsForEnvironment(EAURACRONEnvironmentType Environment);

/** Desativar todos os portais */
UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
void DeactivateAllPortals();

/** Obter portais para um ambiente específico */
UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
TArray<class AAURACRONPCGPortal*> GetPortalsForEnvironment(EAURACRONEnvironmentType Environment) const;

/** Definir pontos de teletransporte para um ambiente */
UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
void SetTeleportDestinations(EAURACRONEnvironmentType Environment, const TArray<FVector>& Locations, const TArray<FRotator>& Rotations);

// ========================================
// FUNÇÕES INTERNAS
// ========================================

/** Inicializar configurações dos ambientes */
void InitializeEnvironmentSettings();

/** Configurar ambiente específico */
void SetupEnvironment(EAURACRONEnvironmentType Environment);

/** Iniciar transição para próximo ambiente */
void StartTransitionToNextEnvironment();

/** Executar transição suave */
void ExecuteTransition();

/** Finalizar transição */
void CompleteTransition();

/** Aplicar configurações visuais do ambiente */
void ApplyEnvironmentVisuals(EAURACRONEnvironmentType Environment, float BlendWeight = 1.0f);

/** Aplicar mecânicas únicas do ambiente */
void ApplyEnvironmentMechanics(EAURACRONEnvironmentType Environment);

/** Notificar sistemas integrados sobre mudança de ambiente */
void NotifySystemsOfEnvironmentChange(EAURACRONEnvironmentType NewEnvironment);

/** Obter próximo ambiente na sequência */
EAURACRONEnvironmentType GetNextEnvironmentInSequence(EAURACRONEnvironmentType Current) const;

/** Aplicar efeitos da fase do mapa nos ambientes */
void ApplyMapPhaseEffects();

/** Atualizar efeitos temporários */
void UpdateTemporaryEffects(float DeltaTime);

/** Interpolar entre configurações de ambientes */
FAURACRONEnvironmentSettings InterpolateEnvironmentSettings(
    const FAURACRONEnvironmentSettings& From, 
    const FAURACRONEnvironmentSettings& To, 
    float Alpha
) const;

/** Criar portal de transição entre ambientes */
class AAURACRONPCGPortal* CreatePortal(EAURACRONEnvironmentType SourceEnvironment, EAURACRONEnvironmentType TargetEnvironment, const FVector& Location);

/** Criar portal tático em uma localização */
class AAURACRONPCGPortal* CreatePortalAtLocation(EAURACRONEnvironmentType Environment, const FVector& Location);

/** Atualizar destinos dos portais */
void UpdatePortalDestinations(EAURACRONEnvironmentType Environment);

/** Pré-carregar assets para próximo ambiente */
void PreloadAssetsForEnvironment(EAURACRONEnvironmentType Environment);

/** Verificar se portal deve ser reposicionado para a fase atual */
bool ShouldRepositionPortalForPhase(AAURACRONPCGPortal* Portal, EAURACRONMapPhase MapPhase) const;

/** Calcular posição ótima do portal baseada na fase do mapa */
FVector CalculateOptimalPortalPosition(AAURACRONPCGPortal* Portal, EAURACRONMapPhase MapPhase) const;
};
