// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Components/AURACRONSigilComponent.h"

#ifdef AURACRON_AURACRONSigilComponent_generated_h
#error "AURACRONSigilComponent.generated.h already included, missing '#pragma once' in AURACRONSigilComponent.h"
#endif
#define AURACRON_AURACRONSigilComponent_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAbilitySystemComponent;
enum class EAURACRONSigilType : uint8;
struct FAURACRONEquippedSigilInfo;

// ********** Begin ScriptStruct FAURACRONEquippedSigilInfo ****************************************
#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_26_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONEquippedSigilInfo;
// ********** End ScriptStruct FAURACRONEquippedSigilInfo ******************************************

// ********** Begin Class UAURACRONSigilComponent **************************************************
#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_76_RPC_WRAPPERS_NO_PURE_DECLS \
	virtual void ActivateSigilFusion_Implementation(); \
	virtual void UnequipSigil_Implementation(EAURACRONSigilType SigilType); \
	virtual void EquipSigil_Implementation(EAURACRONSigilType SigilType, int32 Level, int32 Rarity); \
	DECLARE_FUNCTION(execOnRep_EquippedSigils); \
	DECLARE_FUNCTION(execGetFusionCooldownRemaining); \
	DECLARE_FUNCTION(execIsFusionAvailable); \
	DECLARE_FUNCTION(execActivateSigilFusion); \
	DECLARE_FUNCTION(execCanEquipMoreSigils); \
	DECLARE_FUNCTION(execGetMaxSigilSlots); \
	DECLARE_FUNCTION(execGetEquippedSigilCount); \
	DECLARE_FUNCTION(execGetAllEquippedSigils); \
	DECLARE_FUNCTION(execGetSigilInfo); \
	DECLARE_FUNCTION(execIsSigilEquipped); \
	DECLARE_FUNCTION(execUnequipSigil); \
	DECLARE_FUNCTION(execEquipSigil); \
	DECLARE_FUNCTION(execInitializeWithAbilitySystem);


#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_76_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_UAURACRONSigilComponent_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_76_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAURACRONSigilComponent(); \
	friend struct Z_Construct_UClass_UAURACRONSigilComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UAURACRONSigilComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(UAURACRONSigilComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UAURACRONSigilComponent_NoRegister) \
	DECLARE_SERIALIZER(UAURACRONSigilComponent) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		EquippedSigils=NETFIELD_REP_START, \
		FusionCooldownRemaining, \
		bIsFusionActive, \
		FusionTimeRemaining, \
		NETFIELD_REP_END=FusionTimeRemaining	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_76_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAURACRONSigilComponent(UAURACRONSigilComponent&&) = delete; \
	UAURACRONSigilComponent(const UAURACRONSigilComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAURACRONSigilComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAURACRONSigilComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAURACRONSigilComponent) \
	NO_API virtual ~UAURACRONSigilComponent();


#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_73_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_76_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_76_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_76_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_76_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h_76_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAURACRONSigilComponent;

// ********** End Class UAURACRONSigilComponent ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
