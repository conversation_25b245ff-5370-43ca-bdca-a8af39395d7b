// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Sígilos MOBA 5x5
// Arquivo: SigilReplicationManager.h
// Descrição: Gerenciador de replicação multiplayer para sistema de sígilos

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/NetSerialization.h"
#include "GameplayTagContainer.h"
#include "Net/UnrealNetwork.h"
#include "Net/Serialization/FastArraySerializer.h"
#include "Sigils/SigilItem.h"
#include "SigilReplicationManager.generated.h"

class USigilManagerComponent;
class ASigilItem;

// Estrutura para replicação de dados de sígilo
USTRUCT(BlueprintType)
struct AURACRON_API FSigilReplicationData
{
    GENERATED_BODY()

    UPROPERTY()
    int32 SigilID = -1;

    UPROPERTY()
    ESigilType SigilType = ESigilType::None;

    UPROPERTY()
    ESigilRarity Rarity = ESigilRarity::Common;

    UPROPERTY()
    int32 SlotIndex = -1;

    UPROPERTY()
    float FusionProgress = 0.0f;

    UPROPERTY()
    bool bIsEquipped = false;

    UPROPERTY()
    FGameplayTagContainer SigilTags;

    FSigilReplicationData()
    {
        SigilID = -1;
        SigilType = ESigilType::None;
        Rarity = ESigilRarity::Common;
        SlotIndex = -1;
        FusionProgress = 0.0f;
        bIsEquipped = false;
    }

    FSigilReplicationData(ASigilItem* Sigil, int32 InSlotIndex = -1);

    bool NetSerialize(FArchive& Ar, class UPackageMap* Map, bool& bOutSuccess);
};

template<>
struct TStructOpsTypeTraits<FSigilReplicationData> : public TStructOpsTypeTraitsBase2<FSigilReplicationData>
{
    enum
    {
        WithNetSerializer = true,
    };
};

// Estrutura para replicação de estatísticas do sistema
USTRUCT(BlueprintType)
struct AURACRON_API FSigilReplicationStats
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    int32 TotalSigils = 0;

    UPROPERTY(BlueprintReadOnly)
    int32 EquippedSigils = 0;

    UPROPERTY(BlueprintReadOnly)
    int32 UnlockedSlots = 0;

    UPROPERTY(BlueprintReadOnly)
    float LastReforgeTimestamp = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    bool bSystemActive = true;

    FSigilReplicationStats()
    {
        TotalSigils = 0;
        EquippedSigils = 0;
        UnlockedSlots = 0;
        LastReforgeTimestamp = 0.0f;
        bSystemActive = true;
    }
};

// Estrutura para dados de fusão replicados
USTRUCT(BlueprintType)
struct AURACRON_API FSigilFusionReplicationData
{
    GENERATED_BODY()

    UPROPERTY()
    int32 SigilID = -1;

    UPROPERTY()
    float FusionProgress = 0.0f;

    UPROPERTY()
    float FusionStartTime = 0.0f;

    UPROPERTY()
    bool bIsFusing = false;

    UPROPERTY()
    ESigilRarity TargetRarity = ESigilRarity::Common;

    FSigilFusionReplicationData()
    {
        SigilID = -1;
        FusionProgress = 0.0f;
        FusionStartTime = 0.0f;
        bIsFusing = false;
        TargetRarity = ESigilRarity::Common;
    }
};

// FastArray Item para replicação de dados de Sigil por jogador
USTRUCT(BlueprintType)
struct AURACRON_API FSigilPlayerDataEntry : public FFastArraySerializerItem
{
    GENERATED_BODY()

    UPROPERTY()
    int32 PlayerID = -1;

    UPROPERTY()
    TArray<FSigilReplicationData> SigilData;

    FSigilPlayerDataEntry()
    {
        PlayerID = -1;
    }

    FSigilPlayerDataEntry(int32 InPlayerID, const TArray<FSigilReplicationData>& InSigilData)
        : PlayerID(InPlayerID), SigilData(InSigilData)
    {
    }

    void PreReplicatedRemove(const struct FSigilPlayerDataArray& InArraySerializer);
    void PostReplicatedAdd(const struct FSigilPlayerDataArray& InArraySerializer);
    void PostReplicatedChange(const struct FSigilPlayerDataArray& InArraySerializer);
};

// FastArray Serializer para dados de Sigil por jogador - UE 5.6 PRODUCTION READY
USTRUCT(BlueprintType)
struct AURACRON_API FSigilPlayerDataArray : public FFastArraySerializer
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<FSigilPlayerDataEntry> Items;

    FSigilPlayerDataArray()
    {
        Items.Empty();
    }

    // Métodos para gerenciar dados de jogador
    void AddOrUpdatePlayerData(const FSigilPlayerDataEntry& Entry)
    {
        int32 ExistingIndex = Items.IndexOfByPredicate([&Entry](const FSigilPlayerDataEntry& Item)
        {
            return Item.PlayerID == Entry.PlayerID;
        });

        if (ExistingIndex != INDEX_NONE)
        {
            Items[ExistingIndex] = Entry;
        }
        else
        {
            Items.Add(Entry);
        }
    }

    void RemovePlayerData(int32 PlayerID)
    {
        Items.RemoveAll([PlayerID](const FSigilPlayerDataEntry& Entry)
        {
            return Entry.PlayerID == PlayerID;
        });
    }

    FSigilPlayerDataEntry* FindPlayerData(int32 PlayerID)
    {
        return Items.FindByPredicate([PlayerID](const FSigilPlayerDataEntry& Entry)
        {
            return Entry.PlayerID == PlayerID;
        });
    }

    // Funções helper para manter compatibilidade com TMap
    TArray<FSigilReplicationData>* Find(int32 PlayerID);
    const TArray<FSigilReplicationData>* Find(int32 PlayerID) const;
    TArray<FSigilReplicationData>& FindOrAdd(int32 PlayerID);
    void Add(int32 PlayerID, const TArray<FSigilReplicationData>& SigilData);
    void Remove(int32 PlayerID);
    bool Contains(int32 PlayerID) const;
    void Empty();

    // UE 5.6 MODERNA: FFastArraySerializer funciona automaticamente sem NetDeltaSerialize manual!
    // Baseado na implementação do GameplayAbilities que não usa NetDeltaSerialize explícito
};

// FastArray Item para replicação de estatísticas por jogador
USTRUCT(BlueprintType)
struct AURACRON_API FSigilPlayerStatsEntry : public FFastArraySerializerItem
{
    GENERATED_BODY()

    UPROPERTY()
    int32 PlayerID = -1;

    UPROPERTY()
    FSigilReplicationStats Stats;

    FSigilPlayerStatsEntry()
    {
        PlayerID = -1;
    }

    FSigilPlayerStatsEntry(int32 InPlayerID, const FSigilReplicationStats& InStats)
        : PlayerID(InPlayerID), Stats(InStats)
    {
    }

    void PreReplicatedRemove(const struct FSigilPlayerStatsArray& InArraySerializer);
    void PostReplicatedAdd(const struct FSigilPlayerStatsArray& InArraySerializer);
    void PostReplicatedChange(const struct FSigilPlayerStatsArray& InArraySerializer);
};

// FastArray Serializer para estatísticas por jogador - UE 5.6 PRODUCTION READY
USTRUCT(BlueprintType)
struct AURACRON_API FSigilPlayerStatsArray : public FFastArraySerializer
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<FSigilPlayerStatsEntry> Items;

    FSigilPlayerStatsArray()
    {
        Items.Empty();
    }

    // Métodos para gerenciar estatísticas de jogador
    void AddOrUpdatePlayerStats(const FSigilPlayerStatsEntry& Entry)
    {
        int32 ExistingIndex = Items.IndexOfByPredicate([&Entry](const FSigilPlayerStatsEntry& Item)
        {
            return Item.PlayerID == Entry.PlayerID;
        });

        if (ExistingIndex != INDEX_NONE)
        {
            Items[ExistingIndex] = Entry;
        }
        else
        {
            Items.Add(Entry);
        }
    }

    void RemovePlayerStats(int32 PlayerID)
    {
        Items.RemoveAll([PlayerID](const FSigilPlayerStatsEntry& Entry)
        {
            return Entry.PlayerID == PlayerID;
        });
    }

    FSigilPlayerStatsEntry* FindPlayerStats(int32 PlayerID)
    {
        return Items.FindByPredicate([PlayerID](const FSigilPlayerStatsEntry& Entry)
        {
            return Entry.PlayerID == PlayerID;
        });
    }

    // Funções helper para manter compatibilidade com TMap
    FSigilReplicationStats* Find(int32 PlayerID);
    const FSigilReplicationStats* Find(int32 PlayerID) const;
    FSigilReplicationStats& FindOrAdd(int32 PlayerID);
    void Add(int32 PlayerID, const FSigilReplicationStats& Stats);
    void Remove(int32 PlayerID);
    bool Contains(int32 PlayerID) const;
    void Empty();

    // UE 5.6 MODERNA: FFastArraySerializer funciona automaticamente sem NetDeltaSerialize manual!
    // Baseado na implementação do GameplayAbilities que não usa NetDeltaSerialize explícito
};

// FastArray Item para replicação de fusões ativas por jogador
USTRUCT(BlueprintType)
struct AURACRON_API FSigilActiveFusionsEntry : public FFastArraySerializerItem
{
    GENERATED_BODY()

    UPROPERTY()
    int32 PlayerID = -1;

    UPROPERTY()
    FString FusionID;

    UPROPERTY()
    TArray<FSigilFusionReplicationData> FusionData;

    FSigilActiveFusionsEntry()
    {
        PlayerID = -1;
    }

    FSigilActiveFusionsEntry(int32 InPlayerID, const TArray<FSigilFusionReplicationData>& InFusionData)
        : PlayerID(InPlayerID), FusionData(InFusionData)
    {
    }

    void PreReplicatedRemove(const struct FSigilActiveFusionsArray& InArraySerializer);
    void PostReplicatedAdd(const struct FSigilActiveFusionsArray& InArraySerializer);
    void PostReplicatedChange(const struct FSigilActiveFusionsArray& InArraySerializer);

    // Operador de comparação necessário para TArray::AddUnique
    bool operator==(const FSigilActiveFusionsEntry& Other) const
    {
        return PlayerID == Other.PlayerID && FusionID == Other.FusionID;
    }
};

// FastArray Serializer para fusões ativas por jogador - UE 5.6 PRODUCTION READY
USTRUCT(BlueprintType)
struct AURACRON_API FSigilActiveFusionsArray : public FFastArraySerializer
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<FSigilActiveFusionsEntry> Items;

    FSigilActiveFusionsArray()
    {
        Items.Empty();
    }

    // Métodos para gerenciar fusões ativas
    void AddFusion(const FSigilActiveFusionsEntry& Entry)
    {
        Items.AddUnique(Entry);
    }

    void RemoveFusion(const FString& FusionID)
    {
        Items.RemoveAll([&FusionID](const FSigilActiveFusionsEntry& Entry)
        {
            return Entry.FusionID == FusionID;
        });
    }

    bool HasFusion(const FString& FusionID) const
    {
        return Items.ContainsByPredicate([&FusionID](const FSigilActiveFusionsEntry& Entry)
        {
            return Entry.FusionID == FusionID;
        });
    }

    // Funções helper para manter compatibilidade com TMap
    TArray<FSigilFusionReplicationData>* Find(int32 PlayerID);
    const TArray<FSigilFusionReplicationData>* Find(int32 PlayerID) const;
    TArray<FSigilFusionReplicationData>& FindOrAdd(int32 PlayerID);
    void Add(int32 PlayerID, const TArray<FSigilFusionReplicationData>& FusionData);
    void Remove(int32 PlayerID);
    bool Contains(int32 PlayerID) const;
    void Empty();

    // UE 5.6 MODERNA: FFastArraySerializer funciona automaticamente sem NetDeltaSerialize manual!
    // Baseado na implementação do GameplayAbilities que não usa NetDeltaSerialize explícito
};

// Delegados para eventos de replicação
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilEquipped, int32, PlayerID, const FSigilReplicationData&, SigilData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilUnequipped, int32, PlayerID, int32, SlotIndex);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilReplicationFusionStarted, int32, PlayerID, const FSigilFusionReplicationData&, FusionData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilReplicationFusionCompleted, int32, PlayerID, const FSigilReplicationData&, NewSigilData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSigilSystemStatsUpdated, const FSigilReplicationStats&, Stats);

/**
 * Gerenciador de replicação para o sistema de sígilos em ambiente MOBA 5x5
 * Suporta até 10 jogadores simultâneos com otimizações de rede
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AURACRON_API USigilReplicationManager : public UActorComponent
{
    GENERATED_BODY()

public:
    USigilReplicationManager();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    // Configuração de replicação
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication")
    int32 MaxPlayers = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication")
    float ReplicationFrequency = 20.0f; // Hz

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication")
    bool bOptimizeForMOBA = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication")
    float MaxReplicationDistance = 5000.0f; // Unreal Units

    // Dados replicados usando FFastArraySerializer
    UPROPERTY(Replicated)
    FSigilPlayerDataArray PlayerSigilDataArray;
    
    UPROPERTY(Replicated)
    FSigilPlayerStatsArray PlayerSystemStatsArray;
    
    UPROPERTY(Replicated)
    FSigilActiveFusionsArray ActiveFusionsArray;

    // Eventos de replicação
    UPROPERTY(BlueprintAssignable)
    FOnSigilEquipped OnSigilEquipped;

    UPROPERTY(BlueprintAssignable)
    FOnSigilUnequipped OnSigilUnequipped;

    UPROPERTY(BlueprintAssignable)
    FOnSigilReplicationFusionStarted OnSigilFusionStarted;

    UPROPERTY(BlueprintAssignable)
    FOnSigilReplicationFusionCompleted OnSigilFusionCompleted;

    UPROPERTY(BlueprintAssignable)
    FOnSigilSystemStatsUpdated OnSigilSystemStatsUpdated;

    // Funções principais de replicação
    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void RegisterPlayer(int32 PlayerID, USigilManagerComponent* SigilManager);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void UnregisterPlayer(int32 PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void ReplicateSigilEquip(int32 PlayerID, ASigilItem* Sigil, int32 SlotIndex);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void ReplicateSigilUnequip(int32 PlayerID, int32 SlotIndex);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void ReplicateFusionStart(int32 PlayerID, ASigilItem* Sigil);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void ReplicateFusionComplete(int32 PlayerID, ASigilItem* OldSigil, ASigilItem* NewSigil);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void UpdatePlayerStats(int32 PlayerID, const FSigilReplicationStats& Stats);

    // Funções de consulta
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil Replication")
    TArray<FSigilReplicationData> GetPlayerSigils(int32 PlayerID) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil Replication")
    FSigilReplicationStats GetPlayerStats(int32 PlayerID) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil Replication")
    TArray<FSigilFusionReplicationData> GetPlayerActiveFusions(int32 PlayerID) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil Replication")
    bool IsPlayerRegistered(int32 PlayerID) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil Replication")
    TArray<int32> GetRegisteredPlayers() const;

    // Otimizações de rede
    UFUNCTION(BlueprintCallable, Category = "Optimization")
    void SetReplicationPriority(int32 PlayerID, float Priority);

    UFUNCTION(BlueprintCallable, Category = "Optimization")
    void OptimizeReplicationForDistance(AActor* ViewerActor);

    UFUNCTION(BlueprintCallable, Category = "Optimization")
    void EnableMOBAOptimizations(bool bEnable);

    // RPCs para comunicação cliente-servidor
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerEquipSigil(int32 PlayerID, int32 SigilID, int32 SlotIndex);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerUnequipSigil(int32 PlayerID, int32 SlotIndex);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerStartFusion(int32 PlayerID, int32 SigilID);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerForceFusion(int32 PlayerID, int32 SigilID);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerReforge(int32 PlayerID);

    // Multicast RPCs para notificações
    UFUNCTION(NetMulticast, Reliable)
    void MulticastNotifyEquip(int32 PlayerID, const FSigilReplicationData& SigilData);

    UFUNCTION(NetMulticast, Reliable)
    void MulticastNotifyUnequip(int32 PlayerID, int32 SlotIndex);

    UFUNCTION(NetMulticast, Reliable)
    void MulticastNotifyFusionStart(int32 PlayerID, const FSigilFusionReplicationData& FusionData);

    UFUNCTION(NetMulticast, Reliable)
    void MulticastNotifyFusionComplete(int32 PlayerID, const FSigilReplicationData& NewSigilData);

    // Funções de depuração
    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugPrintReplicationStats();

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DebugSimulateNetworkLag(float LagSeconds);

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DebugForceFullReplication();

protected:
    // Funções de callback de replicação
    UFUNCTION()
    void OnRep_PlayerSigilData();

    UFUNCTION()
    void OnRep_PlayerSystemStats();

    UFUNCTION()
    void OnRep_ActiveFusions();

    // Funções internas
    void InitializeReplicationSettings();
    void CleanupPlayerData(int32 PlayerID);
    bool ValidatePlayerID(int32 PlayerID) const;
    bool ShouldReplicateToClient(int32 PlayerID, AActor* ClientActor) const;
    void UpdateReplicationFrequency();
    void ProcessPendingReplications();

    // Dados internos
    UPROPERTY()
    TMap<int32, USigilManagerComponent*> RegisteredManagers;

    UPROPERTY()
    TMap<int32, float> PlayerReplicationPriorities;

    UPROPERTY()
    TArray<FSigilReplicationData> PendingReplications;

    // Timers
    FTimerHandle ReplicationTimerHandle;
    FTimerHandle OptimizationTimerHandle;

    // Estatísticas de rede
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 TotalReplicationsSent = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 TotalReplicationsReceived = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float AverageReplicationSize = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float NetworkBandwidthUsed = 0.0f;

private:
    // Constantes de otimização
    static const float MOBA_OPTIMIZATION_INTERVAL;
    static const float MAX_REPLICATION_DISTANCE_SQUARED;
    static const int32 MAX_REPLICATIONS_PER_FRAME;
    static const float PRIORITY_UPDATE_INTERVAL;
};