// AURACRONPCGEnvironment.h
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Classe para gerenciar os ambientes procedurais

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
// Forward declaration para evitar dependência direta
class UPCGComponent;
#include "PCGSettings.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCG/AURACRONPCGTypes.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "AURACRONPCGEnvironment.generated.h"

class UPCGComponent;
class AStaticMeshActor;

/**
 * Estrutura para armazenar informações de uma floresta respirante
 */
USTRUCT()
struct FBreathingForestData
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Center;

    UPROPERTY()
    float Radius;

    UPROPERTY()
    TArray<FVector> TreePositions;

    FBreathingForestData()
        : Center(FVector::ZeroVector)
        , Radius(0.0f)
    {
    }

    FBreathingForestData(const FVector& InCenter, float InRadius, const TArray<FVector>& InTreePositions)
        : Center(InCenter)
        , Radius(InRadius)
        , TreePositions(InTreePositions)
    {
    }
};

/**
 * Estrutura para armazenar dados de ilhas flutuantes
 */
USTRUCT()
struct FFloatingIslandData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* IslandActor = nullptr;

    UPROPERTY()
    float OriginalHeight = 0.0f;

    UPROPERTY()
    float FloatHeight = 10.0f;

    UPROPERTY()
    float FloatSpeed = 1.0f;

    UPROPERTY()
    float TimeOffset = 0.0f;
};

/**
 * Estrutura para armazenar dados das fortalezas de nuvem
 */
USTRUCT()
struct FCloudFortressData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* FortressActor = nullptr;

    UPROPERTY()
    FVector OriginalPosition = FVector::ZeroVector;

    UPROPERTY()
    FVector DriftDirection = FVector::ZeroVector;

    UPROPERTY()
    float DriftSpeed = 1.0f;

    UPROPERTY()
    float HorizontalSpeed = 0.5f;

    UPROPERTY()
    float VerticalSpeed = 0.5f;
};

/**
 * Estrutura para armazenar dados dos jardins estelares
 */
USTRUCT()
struct FStellarGardenData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* GardenActor = nullptr;

    UPROPERTY()
    float GravityStrength = 0.5f;

    UPROPERTY()
    float ResourceDensity = 1.0f;

    UPROPERTY()
    float TimeOffset = 0.0f;
};

/**
 * Estrutura para armazenar dados das pontes aurora
 */
USTRUCT()
struct FAuroraBridgeData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* BridgeActor = nullptr;

    UPROPERTY()
    bool bIsVisible = false;

    UPROPERTY()
    float VisibilityTimer = 0.0f;

    UPROPERTY()
    float VisibilityDuration = 30.0f;

    UPROPERTY()
    float InvisibilityDuration = 60.0f;

    UPROPERTY()
    UNiagaraComponent* ParticleComponent = nullptr;
};

/**
 * Estrutura para armazenar dados dos rios de almas
 */
USTRUCT()
struct FRiverOfSoulsData
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<FVector> FlowPoints;

    UPROPERTY()
    FVector FlowDirection = FVector::ZeroVector;

    UPROPERTY()
    float FlowSpeed = 1.0f;

    UPROPERTY()
    float Width = 500.0f;

    UPROPERTY()
    UNiagaraComponent* ParticleComponent = nullptr;
};

/**
 * Estrutura para armazenar dados das estruturas fragmentadas
 */
USTRUCT()
struct FFragmentedStructureData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* StructureActor = nullptr;

    UPROPERTY()
    FVector OriginalPosition = FVector::ZeroVector;

    UPROPERTY()
    float FragmentationLevel = 0.5f;

    UPROPERTY()
    float TimeOffset = 0.0f;
};

/**
 * Estrutura para armazenar dados das zonas de distorção temporal
 */
USTRUCT()
struct FTemporalDistortionData
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Center = FVector::ZeroVector;

    UPROPERTY()
    float Radius = 1000.0f;

    UPROPERTY()
    float TimeScale = 0.5f;

    UPROPERTY()
    UNiagaraComponent* ParticleComponent = nullptr;
};

/**
 * Estrutura para armazenar dados dos nexos sombrios
 */
USTRUCT()
struct FShadowNexusData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* NexusActor = nullptr;

    UPROPERTY()
    FVector Position = FVector::ZeroVector;

    UPROPERTY()
    float ControlRadius = 2000.0f;

    UPROPERTY()
    float EnergyLevel = 1.0f;

    UPROPERTY()
    bool bIsActive = true;

    UPROPERTY()
    TArray<AActor*> ConnectedStructures;
};

/**
 * Estrutura para armazenar dados das torres de lamentação
 */
USTRUCT()
struct FTowerOfLamentationData
{
    GENERATED_BODY()

    UPROPERTY()
    float DrainRadius = 1500.0f;

    UPROPERTY()
    float DrainStrength = 0.1f;

    UPROPERTY()
    float EnergyCapacity = 100.0f;

    UPROPERTY()
    float CurrentEnergy = 0.0f;

    UPROPERTY()
    AStaticMeshActor* TowerActor = nullptr;
};

/**
 * Estrutura para armazenar dados da Âncora do Purgatório
 */
USTRUCT()
struct FPurgatoryAnchorData
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Position = FVector::ZeroVector;

    UPROPERTY()
    float AnchorStrength = 1.0f;

    UPROPERTY()
    float InfluenceRadius = 3000.0f;

    UPROPERTY()
    bool bIsStabilized = false;

    UPROPERTY()
    float StabilityLevel = 0.5f;

    UPROPERTY()
    TArray<FVector> ConnectedPoints;

    UPROPERTY()
    float EnergyOutput = 1.0f;

    UPROPERTY()
    float ResonanceFrequency = 1.0f;

    UPROPERTY()
    bool bHasSpectralGuardian = false;

    UPROPERTY()
    AStaticMeshActor* AnchorActor = nullptr;

    UPROPERTY()
    UNiagaraComponent* EnergyEffect = nullptr;

    UPROPERTY()
    UNiagaraComponent* ResonanceEffect = nullptr;

    UPROPERTY()
    TArray<UNiagaraComponent*> ConnectionEffects;
};

/**
 * Estrutura para armazenar dados do Guardião Espectral
 */
USTRUCT()
struct FSpectralGuardianData
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Position = FVector::ZeroVector;

    UPROPERTY()
    float Health = 100.0f;

    UPROPERTY()
    float MaxHealth = 100.0f;

    UPROPERTY()
    bool bIsActive = false;

    UPROPERTY()
    float SpawnTimer = 0.0f;

    UPROPERTY()
    float RespawnTime = 300.0f;

    UPROPERTY()
    TArray<FVector> PatrolPoints;

    UPROPERTY()
    int32 CurrentPatrolIndex = 0;

    UPROPERTY()
    float MovementSpeed = 600.0f;

    UPROPERTY()
    float AttackRange = 1200.0f;

    UPROPERTY()
    float AttackDamage = 50.0f;

    UPROPERTY()
    float AttackCooldown = 2.0f;

    UPROPERTY()
    float LastAttackTime = 0.0f;

    UPROPERTY()
    AActor* TargetActor = nullptr;

    UPROPERTY()
    AStaticMeshActor* GuardianActor = nullptr;

    UPROPERTY()
    UNiagaraComponent* SpectralEffect = nullptr;
};





/**
 * Ator individual para gerenciar um ambiente procedural específico no AURACRON
 *
 * RESPONSABILIDADES CLARIFICADAS:
 * ================================
 *
 * ESTA CLASSE (AURACRONPCGEnvironment) - ATOR INDIVIDUAL:
 * - Representa UMA INSTÂNCIA específica de ambiente (ex: uma área de Radiant Plains)
 * - Gerencia a geração PCG local de elementos específicos (árvores, rochas, estruturas)
 * - Controla características visuais e físicas do ambiente local
 * - Responde a comandos do EnvironmentManager para ativação/desativação
 * - Implementa lógica específica de cada tipo de ambiente (florestas respirantes, etc.)
 * - Gerencia atores gerados localmente (GeneratedActors array)
 *
 * AURACRONPCGEnvironmentManager - GERENCIADOR GLOBAL:
 * - Controla a rotação automática entre os 3 ambientes principais
 * - Gerencia transições suaves entre ambientes
 * - Coordena múltiplas instâncias de AURACRONPCGEnvironment
 * - Aplica efeitos globais (iluminação, pós-processamento, fog)
 * - Integra com outros sistemas (Phase, Objective, Lane, Jungle)
 * - Controla timing e sequenciamento de mudanças de ambiente
 *
 * RELAÇÃO: Manager (1) -> Environment Instances (N)
 * O Manager orquestra, os Environments executam localmente.
 */
UCLASS()
class AURACRON_API AAURACRONPCGEnvironment : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGEnvironment();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Configurar o tipo de ambiente
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetEnvironmentType(EAURACRONEnvironmentType NewType);

    // Obter o tipo de ambiente
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    EAURACRONEnvironmentType GetEnvironmentType() const { return EnvironmentType; }

    // Gerar o ambiente procedural
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateEnvironment();

    // Atualizar o ambiente com base na fase do mapa
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);
    
    // Gerar a Âncora do Purgatório
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|PurgatoryRealm")
    void GeneratePurgatoryAnchor();
    
    // Atualizar a Âncora do Purgatório
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|PurgatoryRealm")
    void UpdatePurgatoryAnchor(float CurrentTime);

    // Definir a visibilidade do ambiente
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetEnvironmentVisibility(bool bVisible);

    // Definir a escala de atividade do ambiente (0.0 = preview, 1.0 = totalmente ativo)
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetActivityScale(float Scale);

    // Obter escala de atividade atual
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    float GetActivityScale() const { return ActivityScale; }

    // ========================================
    // FUNÇÕES DE INTEGRAÇÃO COM ENVIRONMENTMANAGER
    // ========================================

    /** Registrar este ambiente com o EnvironmentManager */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void RegisterWithEnvironmentManager();

    /** Ativar ambiente (chamado pelo EnvironmentManager) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void ActivateEnvironment();

    /** Desativar ambiente (chamado pelo EnvironmentManager) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void DeactivateEnvironment();

    /** Aplicar transição suave (chamado pelo EnvironmentManager) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void ApplyTransitionEffect(float TransitionProgress, bool bFadingIn);

    /** Obter componente PCG para acesso externo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    UPCGComponent* GetPCGComponent() const { return PCGComponent; }

    /** Verificar se ambiente está ativo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    bool IsEnvironmentActive() const { return ActivityScale > 0.0f; }

public:
    // Componente PCG principal para geração do ambiente
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG")
    UPCGComponent* PCGComponent;

    // Configurações PCG para este ambiente
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG")
    UPCGSettings* EnvironmentSettings;

    // Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|Activity", meta = (ClampMin = "0.0", ClampMax = "3.0"))
    float ActivityScale;

    // Características específicas do ambiente
    // Radiant Plains
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|RadiantPlains", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains"))
    bool bHasCrystallinePlateaus;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|RadiantPlains", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains"))
    bool bHasLivingCanyons;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|RadiantPlains", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains"))
    bool bHasBreathingForests;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|RadiantPlains", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains"))
    bool bHasTectonicBridges;

    // Zephyr Firmament
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|ZephyrFirmament", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament"))
    bool bHasOrbitalArchipelagos;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|ZephyrFirmament", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament"))
    bool bHasAuroraBridges;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|ZephyrFirmament", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament"))
    bool bHasCloudFortresses;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|ZephyrFirmament", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament"))
    bool bHasStellarGardens;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|ZephyrFirmament", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament"))
    bool bHasVoidRifts;

    // Purgatory Realm
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|PurgatoryRealm", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm"))
    bool bHasSpectralPlains;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|PurgatoryRealm", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm"))
    bool bHasRiversOfSouls;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|PurgatoryRealm", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm"))
    bool bHasFragmentedStructures;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|PurgatoryRealm", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm"))
    bool bHasTemporalDistortionZones;

private:
    // Tipo de ambiente
    UPROPERTY(EditAnywhere, Replicated, Category = "AURACRON|PCG")
    EAURACRONEnvironmentType EnvironmentType;

    // Arrays para armazenar atores gerados
    UPROPERTY()
    TArray<AActor*> GeneratedActors;

    // Dados específicos para elementos dinâmicos
    UPROPERTY()
    TArray<FBreathingForestData> BreathingForests;
    









    // Dados para arquipélagos orbitais
    UPROPERTY()
    TArray<FFloatingIslandData> FloatingIslandData;
    
    // Dados para fortalezas de nuvem
    UPROPERTY()
    TArray<FCloudFortressData> CloudFortressData;
    
    // Dados para jardins estelares
    UPROPERTY()
    TArray<FStellarGardenData> StellarGardenData;
    
    // Dados para pontes aurora
    UPROPERTY()
    TArray<FAuroraBridgeData> AuroraBridgeData;
    
    // Dados para rios de almas
    UPROPERTY()
    TArray<FRiverOfSoulsData> RiverOfSoulsData;
    
    // Dados para estruturas fragmentadas
    UPROPERTY()
    TArray<FFragmentedStructureData> FragmentedStructureData;
    
    // Dados para zonas de distorção temporal
    UPROPERTY()
    TArray<FTemporalDistortionData> TemporalDistortionData;
    
    // Dados para nexos sombrios
    UPROPERTY()
    TArray<FShadowNexusData> ShadowNexusData;

    
    // Dados para torres de lamentação
    UPROPERTY()
    TArray<FTowerOfLamentationData> TowerOfLamentationData;
    
    // Dados para o Guardião Espectral
    UPROPERTY()
    FSpectralGuardianData SpectralGuardianData;
    
    // Dados para a Âncora do Purgatório
    UPROPERTY()
    FPurgatoryAnchorData PurgatoryAnchorData;
    
    // Sistema de partículas para as fendas do vazio
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|ZephyrFirmament")
    UNiagaraSystem* VoidRiftParticleSystem;
    
    // Sistema de partículas para as pontes aurora
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|ZephyrFirmament")
    UNiagaraSystem* AuroraBridgeParticleSystem;
    
    // Sistema de partículas para o Guardião Espectral
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PurgatoryRealm")
    UNiagaraSystem* SpectralGuardianParticleSystem;
    
    // Sistema de partículas para a Âncora do Purgatório
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PurgatoryRealm")
    UNiagaraSystem* PurgatoryAnchorParticleSystem;
    
    // Sistema de partículas para os nexos sombrios
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PurgatoryRealm")
    UNiagaraSystem* ShadowNexusParticleSystem;
    
    // Sistema de partículas para as torres de lamentação
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PurgatoryRealm")
    UNiagaraSystem* TowerOfLamentationParticleSystem;

    // Funções internas para geração de características específicas
    void GenerateCrystallinePlateaus();
    void GenerateLivingCanyons();
    void GenerateBreathingForests();
    void GenerateTectonicBridges();
    void GenerateOrbitalArchipelagos();
    void GenerateAuroraBridges();
    void GenerateCloudFortresses();
    void GenerateStellarGardens();
    void GenerateVoidRifts();
    void GenerateSpectralPlains();
    void GenerateRiversOfSouls();
    void GenerateFragmentedStructures();
    void GenerateTemporalDistortionZones();
    void GenerateShadowNexuses();
    void GenerateTowersOfLamentation();
    void GenerateSpectralGuardian();
    
    // Funções para mecânicas invertidas do Reino Purgatório
    void ApplyInvertedPhysicsMechanics(float CurrentTime);
    void ApplyTimeReversalMechanics(float CurrentTime, const FVector& Center, float Radius);
    
    // Função para criar portal de teletransporte para fenda do vazio
    void CreateVoidRiftPortal(const FVector& Position, float Radius, const FLinearColor& Color);

    // ========================================
    // VARIÁVEIS DE ESTADO - UE 5.6 APIS MODERNAS
    // ========================================

public:
    /** Estado de ativação do ambiente */
    UPROPERTY(BlueprintReadOnly, Replicated, Category = "AURACRON|PCG|State")
    bool bIsActive;

    // Se o ambiente possui estruturas sombrias
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|PurgatoryRealm")
    bool bHasShadowStructures;

private:

    // ========================================
    // FUNÇÕES AUXILIARES PARA UE 5.6 - APIS MODERNAS
    // ========================================

    /** Aplicar configurações de qualidade baseadas na performance */
    void ApplyQualitySettings();

    /** Obter configuração do ambiente */
    FAURACRONEnvironmentConfig GetEnvironmentConfiguration() const;

    /** Definir parâmetro PCG usando APIs modernas do UE 5.6 */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Parameters")
    void SetPCGParameterModern(const FString& ParameterName, const FVector& Value, const FString& Category = TEXT("Default"));

    // Funções auxiliares
    void ClearEnvironment();
    void ApplyActivityScale();
    void UpdateDynamicElements(float DeltaTime);

    // Funções de atualização específicas para elementos dinâmicos
    void UpdateBreathingForests(float Time);
    void UpdateTectonicBridges(float Time);
    void UpdateOrbitalArchipelagos(float Time);
    void UpdateAuroraBridges(float Time);
    void UpdateCloudFortresses(float Time);
    void UpdateStellarGardens(float Time);
    void UpdateRiversOfSouls(float Time);
    void UpdateFragmentedStructures(float Time);
    void UpdateTemporalDistortionZones(float Time);
    void UpdateShadowNexuses(float Time);
    void UpdateTowersOfLamentation(float Time);
};