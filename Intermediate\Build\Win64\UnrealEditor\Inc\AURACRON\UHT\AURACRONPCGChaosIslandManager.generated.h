// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGChaosIslandManager.h"

#ifdef AURACRON_AURACRONPCGChaosIslandManager_generated_h
#error "AURACRONPCGChaosIslandManager.generated.h already included, missing '#pragma once' in AURACRONPCGChaosIslandManager.h"
#endif
#define AURACRON_AURACRONPCGChaosIslandManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AAURACRONPCGChaosPortal;
class AAURACRONPCGPrismalFlow;
class AChaosIsland;
enum class EAURACRONMapPhase : uint8;

// ********** Begin Class AAURACRONPCGChaosIslandManager *******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execUpdateEffectsIntensity); \
	DECLARE_FUNCTION(execIsPointTooCloseToExistingIslands); \
	DECLARE_FUNCTION(execSpawnChaosPortal); \
	DECLARE_FUNCTION(execSpawnChaosIsland); \
	DECLARE_FUNCTION(execSetAllChaosPortalsActive); \
	DECLARE_FUNCTION(execSetAllChaosIslandsActive); \
	DECLARE_FUNCTION(execFindAllFlowIntersections); \
	DECLARE_FUNCTION(execIsPointAtFlowIntersection); \
	DECLARE_FUNCTION(execGetAllChaosPortals); \
	DECLARE_FUNCTION(execGetAllChaosIslands); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGenerateChaosIslands); \
	DECLARE_FUNCTION(execInitialize);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosIslandManager_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_24_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGChaosIslandManager(); \
	friend struct Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosIslandManager_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGChaosIslandManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGChaosIslandManager_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGChaosIslandManager)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_24_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGChaosIslandManager(AAURACRONPCGChaosIslandManager&&) = delete; \
	AAURACRONPCGChaosIslandManager(const AAURACRONPCGChaosIslandManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGChaosIslandManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGChaosIslandManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGChaosIslandManager) \
	NO_API virtual ~AAURACRONPCGChaosIslandManager();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_21_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_24_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_24_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_24_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGChaosIslandManager;

// ********** End Class AAURACRONPCGChaosIslandManager *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
