// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGEnvironment.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGEnvironment() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironment();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAuroraBridgeData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FBreathingForestData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FCloudFortressData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FFloatingIslandData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FFragmentedStructureData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FPurgatoryAnchorData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FRiverOfSoulsData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FShadowNexusData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSpectralGuardianData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FStellarGardenData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FTemporalDistortionData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FTowerOfLamentationData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_AStaticMeshActor_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGSettings_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FBreathingForestData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FBreathingForestData;
class UScriptStruct* FBreathingForestData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FBreathingForestData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FBreathingForestData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FBreathingForestData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("BreathingForestData"));
	}
	return Z_Registration_Info_UScriptStruct_FBreathingForestData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FBreathingForestData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar informa\xc3\xa7\xc3\xb5""es de uma floresta respirante\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar informa\xc3\xa7\xc3\xb5""es de uma floresta respirante" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TreePositions_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TreePositions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TreePositions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FBreathingForestData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBreathingForestData, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBreathingForestData, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_TreePositions_Inner = { "TreePositions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_TreePositions = { "TreePositions", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBreathingForestData, TreePositions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TreePositions_MetaData), NewProp_TreePositions_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FBreathingForestData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_TreePositions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_TreePositions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBreathingForestData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FBreathingForestData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"BreathingForestData",
	Z_Construct_UScriptStruct_FBreathingForestData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBreathingForestData_Statics::PropPointers),
	sizeof(FBreathingForestData),
	alignof(FBreathingForestData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBreathingForestData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FBreathingForestData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FBreathingForestData()
{
	if (!Z_Registration_Info_UScriptStruct_FBreathingForestData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FBreathingForestData.InnerSingleton, Z_Construct_UScriptStruct_FBreathingForestData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FBreathingForestData.InnerSingleton;
}
// ********** End ScriptStruct FBreathingForestData ************************************************

// ********** Begin ScriptStruct FFloatingIslandData ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FFloatingIslandData;
class UScriptStruct* FFloatingIslandData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FFloatingIslandData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FFloatingIslandData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FFloatingIslandData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("FloatingIslandData"));
	}
	return Z_Registration_Info_UScriptStruct_FFloatingIslandData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FFloatingIslandData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados de ilhas flutuantes\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados de ilhas flutuantes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandActor_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalHeight_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FloatHeight_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FloatSpeed_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeOffset_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OriginalHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FloatHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FloatSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeOffset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FFloatingIslandData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewProp_IslandActor = { "IslandActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFloatingIslandData, IslandActor), Z_Construct_UClass_AStaticMeshActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandActor_MetaData), NewProp_IslandActor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewProp_OriginalHeight = { "OriginalHeight", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFloatingIslandData, OriginalHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalHeight_MetaData), NewProp_OriginalHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewProp_FloatHeight = { "FloatHeight", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFloatingIslandData, FloatHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FloatHeight_MetaData), NewProp_FloatHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewProp_FloatSpeed = { "FloatSpeed", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFloatingIslandData, FloatSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FloatSpeed_MetaData), NewProp_FloatSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewProp_TimeOffset = { "TimeOffset", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFloatingIslandData, TimeOffset), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeOffset_MetaData), NewProp_TimeOffset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FFloatingIslandData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewProp_IslandActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewProp_OriginalHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewProp_FloatHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewProp_FloatSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewProp_TimeOffset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFloatingIslandData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FFloatingIslandData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"FloatingIslandData",
	Z_Construct_UScriptStruct_FFloatingIslandData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFloatingIslandData_Statics::PropPointers),
	sizeof(FFloatingIslandData),
	alignof(FFloatingIslandData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFloatingIslandData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FFloatingIslandData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FFloatingIslandData()
{
	if (!Z_Registration_Info_UScriptStruct_FFloatingIslandData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FFloatingIslandData.InnerSingleton, Z_Construct_UScriptStruct_FFloatingIslandData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FFloatingIslandData.InnerSingleton;
}
// ********** End ScriptStruct FFloatingIslandData *************************************************

// ********** Begin ScriptStruct FCloudFortressData ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FCloudFortressData;
class UScriptStruct* FCloudFortressData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FCloudFortressData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FCloudFortressData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FCloudFortressData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("CloudFortressData"));
	}
	return Z_Registration_Info_UScriptStruct_FCloudFortressData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FCloudFortressData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados das fortalezas de nuvem\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados das fortalezas de nuvem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FortressActor_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalPosition_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DriftDirection_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DriftSpeed_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HorizontalSpeed_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerticalSpeed_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FortressActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OriginalPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DriftDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DriftSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HorizontalSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VerticalSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FCloudFortressData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_FortressActor = { "FortressActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCloudFortressData, FortressActor), Z_Construct_UClass_AStaticMeshActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FortressActor_MetaData), NewProp_FortressActor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_OriginalPosition = { "OriginalPosition", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCloudFortressData, OriginalPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalPosition_MetaData), NewProp_OriginalPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_DriftDirection = { "DriftDirection", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCloudFortressData, DriftDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DriftDirection_MetaData), NewProp_DriftDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_DriftSpeed = { "DriftSpeed", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCloudFortressData, DriftSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DriftSpeed_MetaData), NewProp_DriftSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_HorizontalSpeed = { "HorizontalSpeed", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCloudFortressData, HorizontalSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HorizontalSpeed_MetaData), NewProp_HorizontalSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_VerticalSpeed = { "VerticalSpeed", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCloudFortressData, VerticalSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerticalSpeed_MetaData), NewProp_VerticalSpeed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FCloudFortressData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_FortressActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_OriginalPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_DriftDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_DriftSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_HorizontalSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewProp_VerticalSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCloudFortressData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FCloudFortressData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"CloudFortressData",
	Z_Construct_UScriptStruct_FCloudFortressData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCloudFortressData_Statics::PropPointers),
	sizeof(FCloudFortressData),
	alignof(FCloudFortressData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCloudFortressData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FCloudFortressData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FCloudFortressData()
{
	if (!Z_Registration_Info_UScriptStruct_FCloudFortressData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FCloudFortressData.InnerSingleton, Z_Construct_UScriptStruct_FCloudFortressData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FCloudFortressData.InnerSingleton;
}
// ********** End ScriptStruct FCloudFortressData **************************************************

// ********** Begin ScriptStruct FStellarGardenData ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FStellarGardenData;
class UScriptStruct* FStellarGardenData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FStellarGardenData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FStellarGardenData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FStellarGardenData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("StellarGardenData"));
	}
	return Z_Registration_Info_UScriptStruct_FStellarGardenData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FStellarGardenData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados dos jardins estelares\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados dos jardins estelares" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GardenActor_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GravityStrength_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceDensity_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeOffset_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GardenActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GravityStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResourceDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeOffset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FStellarGardenData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FStellarGardenData_Statics::NewProp_GardenActor = { "GardenActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FStellarGardenData, GardenActor), Z_Construct_UClass_AStaticMeshActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GardenActor_MetaData), NewProp_GardenActor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FStellarGardenData_Statics::NewProp_GravityStrength = { "GravityStrength", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FStellarGardenData, GravityStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GravityStrength_MetaData), NewProp_GravityStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FStellarGardenData_Statics::NewProp_ResourceDensity = { "ResourceDensity", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FStellarGardenData, ResourceDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceDensity_MetaData), NewProp_ResourceDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FStellarGardenData_Statics::NewProp_TimeOffset = { "TimeOffset", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FStellarGardenData, TimeOffset), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeOffset_MetaData), NewProp_TimeOffset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FStellarGardenData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStellarGardenData_Statics::NewProp_GardenActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStellarGardenData_Statics::NewProp_GravityStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStellarGardenData_Statics::NewProp_ResourceDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStellarGardenData_Statics::NewProp_TimeOffset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FStellarGardenData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FStellarGardenData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"StellarGardenData",
	Z_Construct_UScriptStruct_FStellarGardenData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FStellarGardenData_Statics::PropPointers),
	sizeof(FStellarGardenData),
	alignof(FStellarGardenData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FStellarGardenData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FStellarGardenData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FStellarGardenData()
{
	if (!Z_Registration_Info_UScriptStruct_FStellarGardenData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FStellarGardenData.InnerSingleton, Z_Construct_UScriptStruct_FStellarGardenData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FStellarGardenData.InnerSingleton;
}
// ********** End ScriptStruct FStellarGardenData **************************************************

// ********** Begin ScriptStruct FAuroraBridgeData *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuroraBridgeData;
class UScriptStruct* FAuroraBridgeData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuroraBridgeData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuroraBridgeData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuroraBridgeData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AuroraBridgeData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuroraBridgeData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuroraBridgeData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados das pontes aurora\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados das pontes aurora" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BridgeActor_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisibilityTimer_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisibilityDuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvisibilityDuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParticleComponent_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BridgeActor;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisibilityTimer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisibilityDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InvisibilityDuration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ParticleComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuroraBridgeData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_BridgeActor = { "BridgeActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuroraBridgeData, BridgeActor), Z_Construct_UClass_AStaticMeshActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BridgeActor_MetaData), NewProp_BridgeActor_MetaData) };
void Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((FAuroraBridgeData*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuroraBridgeData), &Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_VisibilityTimer = { "VisibilityTimer", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuroraBridgeData, VisibilityTimer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisibilityTimer_MetaData), NewProp_VisibilityTimer_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_VisibilityDuration = { "VisibilityDuration", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuroraBridgeData, VisibilityDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisibilityDuration_MetaData), NewProp_VisibilityDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_InvisibilityDuration = { "InvisibilityDuration", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuroraBridgeData, InvisibilityDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvisibilityDuration_MetaData), NewProp_InvisibilityDuration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_ParticleComponent = { "ParticleComponent", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuroraBridgeData, ParticleComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParticleComponent_MetaData), NewProp_ParticleComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_BridgeActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_VisibilityTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_VisibilityDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_InvisibilityDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewProp_ParticleComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AuroraBridgeData",
	Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::PropPointers),
	sizeof(FAuroraBridgeData),
	alignof(FAuroraBridgeData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000005),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuroraBridgeData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuroraBridgeData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuroraBridgeData.InnerSingleton, Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuroraBridgeData.InnerSingleton;
}
// ********** End ScriptStruct FAuroraBridgeData ***************************************************

// ********** Begin ScriptStruct FRiverOfSoulsData *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRiverOfSoulsData;
class UScriptStruct* FRiverOfSoulsData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRiverOfSoulsData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRiverOfSoulsData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRiverOfSoulsData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("RiverOfSoulsData"));
	}
	return Z_Registration_Info_UScriptStruct_FRiverOfSoulsData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados dos rios de almas\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados dos rios de almas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowPoints_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowDirection_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeed_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParticleComponent_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FlowPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ParticleComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRiverOfSoulsData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_FlowPoints_Inner = { "FlowPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_FlowPoints = { "FlowPoints", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverOfSoulsData, FlowPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowPoints_MetaData), NewProp_FlowPoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_FlowDirection = { "FlowDirection", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverOfSoulsData, FlowDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowDirection_MetaData), NewProp_FlowDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverOfSoulsData, FlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeed_MetaData), NewProp_FlowSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverOfSoulsData, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_ParticleComponent = { "ParticleComponent", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverOfSoulsData, ParticleComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParticleComponent_MetaData), NewProp_ParticleComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_FlowPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_FlowPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_FlowDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_FlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewProp_ParticleComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"RiverOfSoulsData",
	Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::PropPointers),
	sizeof(FRiverOfSoulsData),
	alignof(FRiverOfSoulsData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000005),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRiverOfSoulsData()
{
	if (!Z_Registration_Info_UScriptStruct_FRiverOfSoulsData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRiverOfSoulsData.InnerSingleton, Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRiverOfSoulsData.InnerSingleton;
}
// ********** End ScriptStruct FRiverOfSoulsData ***************************************************

// ********** Begin ScriptStruct FFragmentedStructureData ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FFragmentedStructureData;
class UScriptStruct* FFragmentedStructureData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FFragmentedStructureData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FFragmentedStructureData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FFragmentedStructureData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("FragmentedStructureData"));
	}
	return Z_Registration_Info_UScriptStruct_FFragmentedStructureData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FFragmentedStructureData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados das estruturas fragmentadas\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados das estruturas fragmentadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StructureActor_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalPosition_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FragmentationLevel_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeOffset_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StructureActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OriginalPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FragmentationLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeOffset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FFragmentedStructureData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::NewProp_StructureActor = { "StructureActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFragmentedStructureData, StructureActor), Z_Construct_UClass_AStaticMeshActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StructureActor_MetaData), NewProp_StructureActor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::NewProp_OriginalPosition = { "OriginalPosition", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFragmentedStructureData, OriginalPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalPosition_MetaData), NewProp_OriginalPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::NewProp_FragmentationLevel = { "FragmentationLevel", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFragmentedStructureData, FragmentationLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FragmentationLevel_MetaData), NewProp_FragmentationLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::NewProp_TimeOffset = { "TimeOffset", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFragmentedStructureData, TimeOffset), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeOffset_MetaData), NewProp_TimeOffset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::NewProp_StructureActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::NewProp_OriginalPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::NewProp_FragmentationLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::NewProp_TimeOffset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"FragmentedStructureData",
	Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::PropPointers),
	sizeof(FFragmentedStructureData),
	alignof(FFragmentedStructureData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FFragmentedStructureData()
{
	if (!Z_Registration_Info_UScriptStruct_FFragmentedStructureData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FFragmentedStructureData.InnerSingleton, Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FFragmentedStructureData.InnerSingleton;
}
// ********** End ScriptStruct FFragmentedStructureData ********************************************

// ********** Begin ScriptStruct FTemporalDistortionData *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTemporalDistortionData;
class UScriptStruct* FTemporalDistortionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTemporalDistortionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTemporalDistortionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTemporalDistortionData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("TemporalDistortionData"));
	}
	return Z_Registration_Info_UScriptStruct_FTemporalDistortionData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTemporalDistortionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados das zonas de distor\xc3\xa7\xc3\xa3o temporal\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados das zonas de distor\xc3\xa7\xc3\xa3o temporal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeScale_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParticleComponent_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeScale;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ParticleComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTemporalDistortionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTemporalDistortionData, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTemporalDistortionData, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::NewProp_TimeScale = { "TimeScale", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTemporalDistortionData, TimeScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeScale_MetaData), NewProp_TimeScale_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::NewProp_ParticleComponent = { "ParticleComponent", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTemporalDistortionData, ParticleComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParticleComponent_MetaData), NewProp_ParticleComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::NewProp_TimeScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::NewProp_ParticleComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"TemporalDistortionData",
	Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::PropPointers),
	sizeof(FTemporalDistortionData),
	alignof(FTemporalDistortionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000005),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTemporalDistortionData()
{
	if (!Z_Registration_Info_UScriptStruct_FTemporalDistortionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTemporalDistortionData.InnerSingleton, Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTemporalDistortionData.InnerSingleton;
}
// ********** End ScriptStruct FTemporalDistortionData *********************************************

// ********** Begin ScriptStruct FShadowNexusData **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FShadowNexusData;
class UScriptStruct* FShadowNexusData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FShadowNexusData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FShadowNexusData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FShadowNexusData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ShadowNexusData"));
	}
	return Z_Registration_Info_UScriptStruct_FShadowNexusData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FShadowNexusData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados dos nexos sombrios\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados dos nexos sombrios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NexusActor_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControlRadius_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyLevel_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectedStructures_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NexusActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ControlRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnergyLevel;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ConnectedStructures_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ConnectedStructures;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FShadowNexusData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_NexusActor = { "NexusActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShadowNexusData, NexusActor), Z_Construct_UClass_AStaticMeshActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NexusActor_MetaData), NewProp_NexusActor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShadowNexusData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_ControlRadius = { "ControlRadius", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShadowNexusData, ControlRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControlRadius_MetaData), NewProp_ControlRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_EnergyLevel = { "EnergyLevel", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShadowNexusData, EnergyLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyLevel_MetaData), NewProp_EnergyLevel_MetaData) };
void Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FShadowNexusData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FShadowNexusData), &Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_ConnectedStructures_Inner = { "ConnectedStructures", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_ConnectedStructures = { "ConnectedStructures", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShadowNexusData, ConnectedStructures), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectedStructures_MetaData), NewProp_ConnectedStructures_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FShadowNexusData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_NexusActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_ControlRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_EnergyLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_ConnectedStructures_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewProp_ConnectedStructures,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FShadowNexusData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FShadowNexusData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"ShadowNexusData",
	Z_Construct_UScriptStruct_FShadowNexusData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FShadowNexusData_Statics::PropPointers),
	sizeof(FShadowNexusData),
	alignof(FShadowNexusData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FShadowNexusData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FShadowNexusData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FShadowNexusData()
{
	if (!Z_Registration_Info_UScriptStruct_FShadowNexusData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FShadowNexusData.InnerSingleton, Z_Construct_UScriptStruct_FShadowNexusData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FShadowNexusData.InnerSingleton;
}
// ********** End ScriptStruct FShadowNexusData ****************************************************

// ********** Begin ScriptStruct FTowerOfLamentationData *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTowerOfLamentationData;
class UScriptStruct* FTowerOfLamentationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTowerOfLamentationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTowerOfLamentationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTowerOfLamentationData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("TowerOfLamentationData"));
	}
	return Z_Registration_Info_UScriptStruct_FTowerOfLamentationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados das torres de lamenta\xc3\xa7\xc3\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados das torres de lamenta\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrainRadius_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrainStrength_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyCapacity_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEnergy_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerActor_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DrainRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DrainStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnergyCapacity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentEnergy;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TowerActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTowerOfLamentationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewProp_DrainRadius = { "DrainRadius", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerOfLamentationData, DrainRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrainRadius_MetaData), NewProp_DrainRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewProp_DrainStrength = { "DrainStrength", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerOfLamentationData, DrainStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrainStrength_MetaData), NewProp_DrainStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewProp_EnergyCapacity = { "EnergyCapacity", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerOfLamentationData, EnergyCapacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyCapacity_MetaData), NewProp_EnergyCapacity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewProp_CurrentEnergy = { "CurrentEnergy", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerOfLamentationData, CurrentEnergy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEnergy_MetaData), NewProp_CurrentEnergy_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewProp_TowerActor = { "TowerActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerOfLamentationData, TowerActor), Z_Construct_UClass_AStaticMeshActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerActor_MetaData), NewProp_TowerActor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewProp_DrainRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewProp_DrainStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewProp_EnergyCapacity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewProp_CurrentEnergy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewProp_TowerActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"TowerOfLamentationData",
	Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::PropPointers),
	sizeof(FTowerOfLamentationData),
	alignof(FTowerOfLamentationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTowerOfLamentationData()
{
	if (!Z_Registration_Info_UScriptStruct_FTowerOfLamentationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTowerOfLamentationData.InnerSingleton, Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTowerOfLamentationData.InnerSingleton;
}
// ********** End ScriptStruct FTowerOfLamentationData *********************************************

// ********** Begin ScriptStruct FPurgatoryAnchorData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPurgatoryAnchorData;
class UScriptStruct* FPurgatoryAnchorData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPurgatoryAnchorData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPurgatoryAnchorData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPurgatoryAnchorData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("PurgatoryAnchorData"));
	}
	return Z_Registration_Info_UScriptStruct_FPurgatoryAnchorData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados da \xc3\x82ncora do Purgat\xc3\xb3rio\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados da \xc3\x82ncora do Purgat\xc3\xb3rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnchorStrength_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InfluenceRadius_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsStabilized_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StabilityLevel_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectedPoints_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyOutput_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResonanceFrequency_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasSpectralGuardian_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnchorActor_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyEffect_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResonanceEffect_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionEffects_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnchorStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InfluenceRadius;
	static void NewProp_bIsStabilized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsStabilized;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StabilityLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectedPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ConnectedPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnergyOutput;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResonanceFrequency;
	static void NewProp_bHasSpectralGuardian_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasSpectralGuardian;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AnchorActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnergyEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ResonanceEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ConnectionEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ConnectionEffects;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPurgatoryAnchorData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_AnchorStrength = { "AnchorStrength", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, AnchorStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnchorStrength_MetaData), NewProp_AnchorStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_InfluenceRadius = { "InfluenceRadius", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, InfluenceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InfluenceRadius_MetaData), NewProp_InfluenceRadius_MetaData) };
void Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_bIsStabilized_SetBit(void* Obj)
{
	((FPurgatoryAnchorData*)Obj)->bIsStabilized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_bIsStabilized = { "bIsStabilized", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPurgatoryAnchorData), &Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_bIsStabilized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsStabilized_MetaData), NewProp_bIsStabilized_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_StabilityLevel = { "StabilityLevel", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, StabilityLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StabilityLevel_MetaData), NewProp_StabilityLevel_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ConnectedPoints_Inner = { "ConnectedPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ConnectedPoints = { "ConnectedPoints", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, ConnectedPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectedPoints_MetaData), NewProp_ConnectedPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_EnergyOutput = { "EnergyOutput", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, EnergyOutput), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyOutput_MetaData), NewProp_EnergyOutput_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ResonanceFrequency = { "ResonanceFrequency", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, ResonanceFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResonanceFrequency_MetaData), NewProp_ResonanceFrequency_MetaData) };
void Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_bHasSpectralGuardian_SetBit(void* Obj)
{
	((FPurgatoryAnchorData*)Obj)->bHasSpectralGuardian = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_bHasSpectralGuardian = { "bHasSpectralGuardian", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPurgatoryAnchorData), &Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_bHasSpectralGuardian_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasSpectralGuardian_MetaData), NewProp_bHasSpectralGuardian_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_AnchorActor = { "AnchorActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, AnchorActor), Z_Construct_UClass_AStaticMeshActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnchorActor_MetaData), NewProp_AnchorActor_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_EnergyEffect = { "EnergyEffect", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, EnergyEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyEffect_MetaData), NewProp_EnergyEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ResonanceEffect = { "ResonanceEffect", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, ResonanceEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResonanceEffect_MetaData), NewProp_ResonanceEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ConnectionEffects_Inner = { "ConnectionEffects", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ConnectionEffects = { "ConnectionEffects", nullptr, (EPropertyFlags)0x0010008000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPurgatoryAnchorData, ConnectionEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionEffects_MetaData), NewProp_ConnectionEffects_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_AnchorStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_InfluenceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_bIsStabilized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_StabilityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ConnectedPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ConnectedPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_EnergyOutput,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ResonanceFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_bHasSpectralGuardian,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_AnchorActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_EnergyEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ResonanceEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ConnectionEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewProp_ConnectionEffects,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"PurgatoryAnchorData",
	Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::PropPointers),
	sizeof(FPurgatoryAnchorData),
	alignof(FPurgatoryAnchorData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000005),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPurgatoryAnchorData()
{
	if (!Z_Registration_Info_UScriptStruct_FPurgatoryAnchorData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPurgatoryAnchorData.InnerSingleton, Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPurgatoryAnchorData.InnerSingleton;
}
// ********** End ScriptStruct FPurgatoryAnchorData ************************************************

// ********** Begin ScriptStruct FSpectralGuardianData *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSpectralGuardianData;
class UScriptStruct* FSpectralGuardianData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSpectralGuardianData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSpectralGuardianData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSpectralGuardianData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SpectralGuardianData"));
	}
	return Z_Registration_Info_UScriptStruct_FSpectralGuardianData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSpectralGuardianData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados do Guardi\xc3\xa3o Espectral\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados do Guardi\xc3\xa3o Espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnTimer_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolPoints_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPatrolIndex_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeed_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackRange_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackDamage_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackCooldown_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAttackTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetActor_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GuardianActor_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralEffect_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnTimer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatrolPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PatrolPoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentPatrolIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackCooldown;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastAttackTime;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GuardianActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpectralEffect;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSpectralGuardianData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
void Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FSpectralGuardianData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSpectralGuardianData), &Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_SpawnTimer = { "SpawnTimer", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, SpawnTimer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnTimer_MetaData), NewProp_SpawnTimer_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_PatrolPoints_Inner = { "PatrolPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_PatrolPoints = { "PatrolPoints", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, PatrolPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolPoints_MetaData), NewProp_PatrolPoints_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_CurrentPatrolIndex = { "CurrentPatrolIndex", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, CurrentPatrolIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPatrolIndex_MetaData), NewProp_CurrentPatrolIndex_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_MovementSpeed = { "MovementSpeed", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, MovementSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeed_MetaData), NewProp_MovementSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_AttackRange = { "AttackRange", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, AttackRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackRange_MetaData), NewProp_AttackRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_AttackDamage = { "AttackDamage", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, AttackDamage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackDamage_MetaData), NewProp_AttackDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_AttackCooldown = { "AttackCooldown", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, AttackCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackCooldown_MetaData), NewProp_AttackCooldown_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_LastAttackTime = { "LastAttackTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, LastAttackTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAttackTime_MetaData), NewProp_LastAttackTime_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetActor_MetaData), NewProp_TargetActor_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_GuardianActor = { "GuardianActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, GuardianActor), Z_Construct_UClass_AStaticMeshActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GuardianActor_MetaData), NewProp_GuardianActor_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_SpectralEffect = { "SpectralEffect", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralGuardianData, SpectralEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralEffect_MetaData), NewProp_SpectralEffect_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_SpawnTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_PatrolPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_PatrolPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_CurrentPatrolIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_MovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_AttackRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_AttackDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_AttackCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_LastAttackTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_GuardianActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewProp_SpectralEffect,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SpectralGuardianData",
	Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::PropPointers),
	sizeof(FSpectralGuardianData),
	alignof(FSpectralGuardianData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000005),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSpectralGuardianData()
{
	if (!Z_Registration_Info_UScriptStruct_FSpectralGuardianData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSpectralGuardianData.InnerSingleton, Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSpectralGuardianData.InnerSingleton;
}
// ********** End ScriptStruct FSpectralGuardianData ***********************************************

// ********** Begin Class AAURACRONPCGEnvironment Function ActivateEnvironment *********************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_ActivateEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar ambiente (chamado pelo EnvironmentManager) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar ambiente (chamado pelo EnvironmentManager)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_ActivateEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "ActivateEnvironment", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_ActivateEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_ActivateEnvironment_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_ActivateEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_ActivateEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execActivateEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateEnvironment();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function ActivateEnvironment ***********************

// ********** Begin Class AAURACRONPCGEnvironment Function ApplyTransitionEffect *******************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics
{
	struct AURACRONPCGEnvironment_eventApplyTransitionEffect_Parms
	{
		float TransitionProgress;
		bool bFadingIn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar transi\xc3\xa7\xc3\xa3o suave (chamado pelo EnvironmentManager) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar transi\xc3\xa7\xc3\xa3o suave (chamado pelo EnvironmentManager)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionProgress;
	static void NewProp_bFadingIn_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFadingIn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::NewProp_TransitionProgress = { "TransitionProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventApplyTransitionEffect_Parms, TransitionProgress), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::NewProp_bFadingIn_SetBit(void* Obj)
{
	((AURACRONPCGEnvironment_eventApplyTransitionEffect_Parms*)Obj)->bFadingIn = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::NewProp_bFadingIn = { "bFadingIn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironment_eventApplyTransitionEffect_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::NewProp_bFadingIn_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::NewProp_TransitionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::NewProp_bFadingIn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "ApplyTransitionEffect", Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::AURACRONPCGEnvironment_eventApplyTransitionEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::AURACRONPCGEnvironment_eventApplyTransitionEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execApplyTransitionEffect)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_TransitionProgress);
	P_GET_UBOOL(Z_Param_bFadingIn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTransitionEffect(Z_Param_TransitionProgress,Z_Param_bFadingIn);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function ApplyTransitionEffect *********************

// ********** Begin Class AAURACRONPCGEnvironment Function DeactivateEnvironment *******************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_DeactivateEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Desativar ambiente (chamado pelo EnvironmentManager) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desativar ambiente (chamado pelo EnvironmentManager)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_DeactivateEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "DeactivateEnvironment", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_DeactivateEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_DeactivateEnvironment_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_DeactivateEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_DeactivateEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execDeactivateEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivateEnvironment();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function DeactivateEnvironment *********************

// ********** Begin Class AAURACRONPCGEnvironment Function GenerateEnvironment *********************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gerar o ambiente procedural\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar o ambiente procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "GenerateEnvironment", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execGenerateEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateEnvironment();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function GenerateEnvironment ***********************

// ********** Begin Class AAURACRONPCGEnvironment Function GeneratePurgatoryAnchor *****************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_GeneratePurgatoryAnchor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gerar a \xc3\x82ncora do Purgat\xc3\xb3rio\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar a \xc3\x82ncora do Purgat\xc3\xb3rio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GeneratePurgatoryAnchor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "GeneratePurgatoryAnchor", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GeneratePurgatoryAnchor_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_GeneratePurgatoryAnchor_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_GeneratePurgatoryAnchor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_GeneratePurgatoryAnchor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execGeneratePurgatoryAnchor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GeneratePurgatoryAnchor();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function GeneratePurgatoryAnchor *******************

// ********** Begin Class AAURACRONPCGEnvironment Function GetActivityScale ************************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics
{
	struct AURACRONPCGEnvironment_eventGetActivityScale_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obter escala de atividade atual\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter escala de atividade atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventGetActivityScale_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "GetActivityScale", Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::AURACRONPCGEnvironment_eventGetActivityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::AURACRONPCGEnvironment_eventGetActivityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execGetActivityScale)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetActivityScale();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function GetActivityScale **************************

// ********** Begin Class AAURACRONPCGEnvironment Function GetEnvironmentType **********************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics
{
	struct AURACRONPCGEnvironment_eventGetEnvironmentType_Parms
	{
		EAURACRONEnvironmentType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obter o tipo de ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter o tipo de ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventGetEnvironmentType_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2161956974
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "GetEnvironmentType", Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::AURACRONPCGEnvironment_eventGetEnvironmentType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::AURACRONPCGEnvironment_eventGetEnvironmentType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execGetEnvironmentType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONEnvironmentType*)Z_Param__Result=P_THIS->GetEnvironmentType();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function GetEnvironmentType ************************

// ********** Begin Class AAURACRONPCGEnvironment Function GetPCGComponent *************************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics
{
	struct AURACRONPCGEnvironment_eventGetPCGComponent_Parms
	{
		UPCGComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter componente PCG para acesso externo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter componente PCG para acesso externo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventGetPCGComponent_Parms, ReturnValue), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "GetPCGComponent", Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::AURACRONPCGEnvironment_eventGetPCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::AURACRONPCGEnvironment_eventGetPCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execGetPCGComponent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGComponent**)Z_Param__Result=P_THIS->GetPCGComponent();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function GetPCGComponent ***************************

// ********** Begin Class AAURACRONPCGEnvironment Function IsEnvironmentActive *********************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics
{
	struct AURACRONPCGEnvironment_eventIsEnvironmentActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se ambiente est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se ambiente est\xc3\xa1 ativo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGEnvironment_eventIsEnvironmentActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironment_eventIsEnvironmentActive_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "IsEnvironmentActive", Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::AURACRONPCGEnvironment_eventIsEnvironmentActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::AURACRONPCGEnvironment_eventIsEnvironmentActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execIsEnvironmentActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsEnvironmentActive();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function IsEnvironmentActive ***********************

// ********** Begin Class AAURACRONPCGEnvironment Function RegisterWithEnvironmentManager **********
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_RegisterWithEnvironmentManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Registrar este ambiente com o EnvironmentManager */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar este ambiente com o EnvironmentManager" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_RegisterWithEnvironmentManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "RegisterWithEnvironmentManager", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_RegisterWithEnvironmentManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_RegisterWithEnvironmentManager_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_RegisterWithEnvironmentManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_RegisterWithEnvironmentManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execRegisterWithEnvironmentManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterWithEnvironmentManager();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function RegisterWithEnvironmentManager ************

// ********** Begin Class AAURACRONPCGEnvironment Function SetActivityScale ************************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics
{
	struct AURACRONPCGEnvironment_eventSetActivityScale_Parms
	{
		float Scale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a escala de atividade do ambiente (0.0 = preview, 1.0 = totalmente ativo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a escala de atividade do ambiente (0.0 = preview, 1.0 = totalmente ativo)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventSetActivityScale_Parms, Scale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::NewProp_Scale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "SetActivityScale", Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::AURACRONPCGEnvironment_eventSetActivityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::AURACRONPCGEnvironment_eventSetActivityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execSetActivityScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Scale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActivityScale(Z_Param_Scale);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function SetActivityScale **************************

// ********** Begin Class AAURACRONPCGEnvironment Function SetEnvironmentType **********************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics
{
	struct AURACRONPCGEnvironment_eventSetEnvironmentType_Parms
	{
		EAURACRONEnvironmentType NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar o tipo de ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar o tipo de ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventSetEnvironmentType_Parms, NewType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2161956974
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "SetEnvironmentType", Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::AURACRONPCGEnvironment_eventSetEnvironmentType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::AURACRONPCGEnvironment_eventSetEnvironmentType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execSetEnvironmentType)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEnvironmentType(EAURACRONEnvironmentType(Z_Param_NewType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function SetEnvironmentType ************************

// ********** Begin Class AAURACRONPCGEnvironment Function SetEnvironmentVisibility ****************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics
{
	struct AURACRONPCGEnvironment_eventSetEnvironmentVisibility_Parms
	{
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a visibilidade do ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a visibilidade do ambiente" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AURACRONPCGEnvironment_eventSetEnvironmentVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironment_eventSetEnvironmentVisibility_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "SetEnvironmentVisibility", Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::AURACRONPCGEnvironment_eventSetEnvironmentVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::AURACRONPCGEnvironment_eventSetEnvironmentVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execSetEnvironmentVisibility)
{
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEnvironmentVisibility(Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function SetEnvironmentVisibility ******************

// ********** Begin Class AAURACRONPCGEnvironment Function SetPCGParameterModern *******************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics
{
	struct AURACRONPCGEnvironment_eventSetPCGParameterModern_Parms
	{
		FString ParameterName;
		FVector Value;
		FString Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Parameters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir par\xc3\xa2metro PCG usando APIs modernas do UE 5.6 */" },
#endif
		{ "CPP_Default_Category", "Default" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir par\xc3\xa2metro PCG usando APIs modernas do UE 5.6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventSetPCGParameterModern_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventSetPCGParameterModern_Parms, Value), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventSetPCGParameterModern_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "SetPCGParameterModern", Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::AURACRONPCGEnvironment_eventSetPCGParameterModern_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C40401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::AURACRONPCGEnvironment_eventSetPCGParameterModern_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execSetPCGParameterModern)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Value);
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPCGParameterModern(Z_Param_ParameterName,Z_Param_Out_Value,Z_Param_Category);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function SetPCGParameterModern *********************

// ********** Begin Class AAURACRONPCGEnvironment Function UpdateForMapPhase ***********************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics
{
	struct AURACRONPCGEnvironment_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualizar o ambiente com base na fase do mapa\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar o ambiente com base na fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::AURACRONPCGEnvironment_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::AURACRONPCGEnvironment_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function UpdateForMapPhase *************************

// ********** Begin Class AAURACRONPCGEnvironment Function UpdatePurgatoryAnchor *******************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics
{
	struct AURACRONPCGEnvironment_eventUpdatePurgatoryAnchor_Parms
	{
		float CurrentTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualizar a \xc3\x82ncora do Purgat\xc3\xb3rio\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar a \xc3\x82ncora do Purgat\xc3\xb3rio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::NewProp_CurrentTime = { "CurrentTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventUpdatePurgatoryAnchor_Parms, CurrentTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::NewProp_CurrentTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "UpdatePurgatoryAnchor", Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::AURACRONPCGEnvironment_eventUpdatePurgatoryAnchor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::AURACRONPCGEnvironment_eventUpdatePurgatoryAnchor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execUpdatePurgatoryAnchor)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_CurrentTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePurgatoryAnchor(Z_Param_CurrentTime);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function UpdatePurgatoryAnchor *********************

// ********** Begin Class AAURACRONPCGEnvironment **************************************************
void AAURACRONPCGEnvironment::StaticRegisterNativesAAURACRONPCGEnvironment()
{
	UClass* Class = AAURACRONPCGEnvironment::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateEnvironment", &AAURACRONPCGEnvironment::execActivateEnvironment },
		{ "ApplyTransitionEffect", &AAURACRONPCGEnvironment::execApplyTransitionEffect },
		{ "DeactivateEnvironment", &AAURACRONPCGEnvironment::execDeactivateEnvironment },
		{ "GenerateEnvironment", &AAURACRONPCGEnvironment::execGenerateEnvironment },
		{ "GeneratePurgatoryAnchor", &AAURACRONPCGEnvironment::execGeneratePurgatoryAnchor },
		{ "GetActivityScale", &AAURACRONPCGEnvironment::execGetActivityScale },
		{ "GetEnvironmentType", &AAURACRONPCGEnvironment::execGetEnvironmentType },
		{ "GetPCGComponent", &AAURACRONPCGEnvironment::execGetPCGComponent },
		{ "IsEnvironmentActive", &AAURACRONPCGEnvironment::execIsEnvironmentActive },
		{ "RegisterWithEnvironmentManager", &AAURACRONPCGEnvironment::execRegisterWithEnvironmentManager },
		{ "SetActivityScale", &AAURACRONPCGEnvironment::execSetActivityScale },
		{ "SetEnvironmentType", &AAURACRONPCGEnvironment::execSetEnvironmentType },
		{ "SetEnvironmentVisibility", &AAURACRONPCGEnvironment::execSetEnvironmentVisibility },
		{ "SetPCGParameterModern", &AAURACRONPCGEnvironment::execSetPCGParameterModern },
		{ "UpdateForMapPhase", &AAURACRONPCGEnvironment::execUpdateForMapPhase },
		{ "UpdatePurgatoryAnchor", &AAURACRONPCGEnvironment::execUpdatePurgatoryAnchor },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGEnvironment;
UClass* AAURACRONPCGEnvironment::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGEnvironment;
	if (!Z_Registration_Info_UClass_AAURACRONPCGEnvironment.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGEnvironment"),
			Z_Registration_Info_UClass_AAURACRONPCGEnvironment.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGEnvironment,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGEnvironment.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister()
{
	return AAURACRONPCGEnvironment::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ator individual para gerenciar um ambiente procedural espec\xc3\xad""fico no AURACRON\n *\n * RESPONSABILIDADES CLARIFICADAS:\n * ================================\n *\n * ESTA CLASSE (AURACRONPCGEnvironment) - ATOR INDIVIDUAL:\n * - Representa UMA INST\xc3\x82NCIA espec\xc3\xad""fica de ambiente (ex: uma \xc3\xa1rea de Radiant Plains)\n * - Gerencia a gera\xc3\xa7\xc3\xa3o PCG local de elementos espec\xc3\xad""ficos (\xc3\xa1rvores, rochas, estruturas)\n * - Controla caracter\xc3\xadsticas visuais e f\xc3\xadsicas do ambiente local\n * - Responde a comandos do EnvironmentManager para ativa\xc3\xa7\xc3\xa3o/desativa\xc3\xa7\xc3\xa3o\n * - Implementa l\xc3\xb3gica espec\xc3\xad""fica de cada tipo de ambiente (florestas respirantes, etc.)\n * - Gerencia atores gerados localmente (GeneratedActors array)\n *\n * AURACRONPCGEnvironmentManager - GERENCIADOR GLOBAL:\n * - Controla a rota\xc3\xa7\xc3\xa3o autom\xc3\xa1tica entre os 3 ambientes principais\n * - Gerencia transi\xc3\xa7\xc3\xb5""es suaves entre ambientes\n * - Coordena m\xc3\xbaltiplas inst\xc3\xa2ncias de AURACRONPCGEnvironment\n * - Aplica efeitos globais (ilumina\xc3\xa7\xc3\xa3o, p\xc3\xb3s-processamento, fog)\n * - Integra com outros sistemas (Phase, Objective, Lane, Jungle)\n * - Controla timing e sequenciamento de mudan\xc3\xa7""as de ambiente\n *\n * RELA\xc3\x87\xc3\x83O: Manager (1) -> Environment Instances (N)\n * O Manager orquestra, os Environments executam localmente.\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGEnvironment.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator individual para gerenciar um ambiente procedural espec\xc3\xad""fico no AURACRON\n\nRESPONSABILIDADES CLARIFICADAS:\n\n\nESTA CLASSE (AURACRONPCGEnvironment) - ATOR INDIVIDUAL:\n- Representa UMA INST\xc3\x82NCIA espec\xc3\xad""fica de ambiente (ex: uma \xc3\xa1rea de Radiant Plains)\n- Gerencia a gera\xc3\xa7\xc3\xa3o PCG local de elementos espec\xc3\xad""ficos (\xc3\xa1rvores, rochas, estruturas)\n- Controla caracter\xc3\xadsticas visuais e f\xc3\xadsicas do ambiente local\n- Responde a comandos do EnvironmentManager para ativa\xc3\xa7\xc3\xa3o/desativa\xc3\xa7\xc3\xa3o\n- Implementa l\xc3\xb3gica espec\xc3\xad""fica de cada tipo de ambiente (florestas respirantes, etc.)\n- Gerencia atores gerados localmente (GeneratedActors array)\n\nAURACRONPCGEnvironmentManager - GERENCIADOR GLOBAL:\n- Controla a rota\xc3\xa7\xc3\xa3o autom\xc3\xa1tica entre os 3 ambientes principais\n- Gerencia transi\xc3\xa7\xc3\xb5""es suaves entre ambientes\n- Coordena m\xc3\xbaltiplas inst\xc3\xa2ncias de AURACRONPCGEnvironment\n- Aplica efeitos globais (ilumina\xc3\xa7\xc3\xa3o, p\xc3\xb3s-processamento, fog)\n- Integra com outros sistemas (Phase, Objective, Lane, Jungle)\n- Controla timing e sequenciamento de mudan\xc3\xa7""as de ambiente\n\nRELA\xc3\x87\xc3\x83O: Manager (1) -> Environment Instances (N)\nO Manager orquestra, os Environments executam localmente." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente PCG principal para gera\xc3\xa7\xc3\xa3o do ambiente\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente PCG principal para gera\xc3\xa7\xc3\xa3o do ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentSettings_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es PCG para este ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es PCG para este ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityScale_MetaData[] = {
		{ "Category", "AURACRON|PCG|Activity" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasCrystallinePlateaus_MetaData[] = {
		{ "Category", "AURACRON|PCG|RadiantPlains" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Caracter\xc3\xadsticas espec\xc3\xad""ficas do ambiente\n// Radiant Plains\n" },
#endif
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Caracter\xc3\xadsticas espec\xc3\xad""ficas do ambiente\nRadiant Plains" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasLivingCanyons_MetaData[] = {
		{ "Category", "AURACRON|PCG|RadiantPlains" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasBreathingForests_MetaData[] = {
		{ "Category", "AURACRON|PCG|RadiantPlains" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasTectonicBridges_MetaData[] = {
		{ "Category", "AURACRON|PCG|RadiantPlains" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasOrbitalArchipelagos_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Zephyr Firmament\n" },
#endif
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Zephyr Firmament" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasAuroraBridges_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasCloudFortresses_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasStellarGardens_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasVoidRifts_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasSpectralPlains_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Purgatory Realm\n" },
#endif
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Purgatory Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasRiversOfSouls_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasFragmentedStructures_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasTemporalDistortionZones_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentType_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tipo de ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedActors_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Arrays para armazenar atores gerados\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Arrays para armazenar atores gerados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BreathingForests_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados espec\xc3\xad""ficos para elementos din\xc3\xa2micos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados espec\xc3\xad""ficos para elementos din\xc3\xa2micos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FloatingIslandData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para arquip\xc3\xa9lagos orbitais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para arquip\xc3\xa9lagos orbitais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CloudFortressData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para fortalezas de nuvem\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para fortalezas de nuvem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StellarGardenData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para jardins estelares\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para jardins estelares" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AuroraBridgeData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para pontes aurora\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para pontes aurora" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverOfSoulsData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para rios de almas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para rios de almas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FragmentedStructureData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para estruturas fragmentadas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para estruturas fragmentadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemporalDistortionData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para zonas de distor\xc3\xa7\xc3\xa3o temporal\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para zonas de distor\xc3\xa7\xc3\xa3o temporal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShadowNexusData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para nexos sombrios\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para nexos sombrios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerOfLamentationData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para torres de lamenta\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para torres de lamenta\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralGuardianData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para o Guardi\xc3\xa3o Espectral\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para o Guardi\xc3\xa3o Espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PurgatoryAnchorData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados para a \xc3\x82ncora do Purgat\xc3\xb3rio\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados para a \xc3\x82ncora do Purgat\xc3\xb3rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoidRiftParticleSystem_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de part\xc3\xad""culas para as fendas do vazio\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas para as fendas do vazio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AuroraBridgeParticleSystem_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de part\xc3\xad""culas para as pontes aurora\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas para as pontes aurora" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralGuardianParticleSystem_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de part\xc3\xad""culas para o Guardi\xc3\xa3o Espectral\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas para o Guardi\xc3\xa3o Espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PurgatoryAnchorParticleSystem_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de part\xc3\xad""culas para a \xc3\x82ncora do Purgat\xc3\xb3rio\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas para a \xc3\x82ncora do Purgat\xc3\xb3rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShadowNexusParticleSystem_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de part\xc3\xad""culas para os nexos sombrios\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas para os nexos sombrios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerOfLamentationParticleSystem_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de part\xc3\xad""culas para as torres de lamenta\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas para as torres de lamenta\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "AURACRON|PCG|State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado de ativa\xc3\xa7\xc3\xa3o do ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado de ativa\xc3\xa7\xc3\xa3o do ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasShadowStructures_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Se o ambiente possui estruturas sombrias\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o ambiente possui estruturas sombrias" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentSettings;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityScale;
	static void NewProp_bHasCrystallinePlateaus_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasCrystallinePlateaus;
	static void NewProp_bHasLivingCanyons_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasLivingCanyons;
	static void NewProp_bHasBreathingForests_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasBreathingForests;
	static void NewProp_bHasTectonicBridges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasTectonicBridges;
	static void NewProp_bHasOrbitalArchipelagos_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasOrbitalArchipelagos;
	static void NewProp_bHasAuroraBridges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasAuroraBridges;
	static void NewProp_bHasCloudFortresses_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasCloudFortresses;
	static void NewProp_bHasStellarGardens_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasStellarGardens;
	static void NewProp_bHasVoidRifts_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasVoidRifts;
	static void NewProp_bHasSpectralPlains_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasSpectralPlains;
	static void NewProp_bHasRiversOfSouls_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasRiversOfSouls;
	static void NewProp_bHasFragmentedStructures_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasFragmentedStructures;
	static void NewProp_bHasTemporalDistortionZones_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasTemporalDistortionZones;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GeneratedActors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BreathingForests_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BreathingForests;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FloatingIslandData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FloatingIslandData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CloudFortressData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CloudFortressData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StellarGardenData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_StellarGardenData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AuroraBridgeData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AuroraBridgeData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RiverOfSoulsData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RiverOfSoulsData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FragmentedStructureData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FragmentedStructureData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TemporalDistortionData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TemporalDistortionData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ShadowNexusData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ShadowNexusData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TowerOfLamentationData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TowerOfLamentationData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpectralGuardianData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PurgatoryAnchorData;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VoidRiftParticleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AuroraBridgeParticleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpectralGuardianParticleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PurgatoryAnchorParticleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ShadowNexusParticleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TowerOfLamentationParticleSystem;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bHasShadowStructures_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasShadowStructures;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_ActivateEnvironment, "ActivateEnvironment" }, // 1846176254
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_ApplyTransitionEffect, "ApplyTransitionEffect" }, // 1508077803
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_DeactivateEnvironment, "DeactivateEnvironment" }, // 4286713082
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment, "GenerateEnvironment" }, // 2057581934
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_GeneratePurgatoryAnchor, "GeneratePurgatoryAnchor" }, // 2300260966
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_GetActivityScale, "GetActivityScale" }, // 445990705
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType, "GetEnvironmentType" }, // 41884074
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_GetPCGComponent, "GetPCGComponent" }, // 2875135874
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_IsEnvironmentActive, "IsEnvironmentActive" }, // 2677889548
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_RegisterWithEnvironmentManager, "RegisterWithEnvironmentManager" }, // 2993913799
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale, "SetActivityScale" }, // 2604318470
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType, "SetEnvironmentType" }, // 1132300547
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility, "SetEnvironmentVisibility" }, // 3432250449
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_SetPCGParameterModern, "SetPCGParameterModern" }, // 2365027983
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase, "UpdateForMapPhase" }, // 3101457038
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdatePurgatoryAnchor, "UpdatePurgatoryAnchor" }, // 3402773834
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGEnvironment>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentSettings = { "EnvironmentSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, EnvironmentSettings), Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentSettings_MetaData), NewProp_EnvironmentSettings_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_ActivityScale = { "ActivityScale", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, ActivityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityScale_MetaData), NewProp_ActivityScale_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCrystallinePlateaus_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasCrystallinePlateaus = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCrystallinePlateaus = { "bHasCrystallinePlateaus", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCrystallinePlateaus_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasCrystallinePlateaus_MetaData), NewProp_bHasCrystallinePlateaus_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasLivingCanyons_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasLivingCanyons = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasLivingCanyons = { "bHasLivingCanyons", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasLivingCanyons_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasLivingCanyons_MetaData), NewProp_bHasLivingCanyons_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasBreathingForests_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasBreathingForests = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasBreathingForests = { "bHasBreathingForests", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasBreathingForests_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasBreathingForests_MetaData), NewProp_bHasBreathingForests_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTectonicBridges_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasTectonicBridges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTectonicBridges = { "bHasTectonicBridges", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTectonicBridges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasTectonicBridges_MetaData), NewProp_bHasTectonicBridges_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasOrbitalArchipelagos_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasOrbitalArchipelagos = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasOrbitalArchipelagos = { "bHasOrbitalArchipelagos", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasOrbitalArchipelagos_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasOrbitalArchipelagos_MetaData), NewProp_bHasOrbitalArchipelagos_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasAuroraBridges_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasAuroraBridges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasAuroraBridges = { "bHasAuroraBridges", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasAuroraBridges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasAuroraBridges_MetaData), NewProp_bHasAuroraBridges_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCloudFortresses_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasCloudFortresses = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCloudFortresses = { "bHasCloudFortresses", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCloudFortresses_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasCloudFortresses_MetaData), NewProp_bHasCloudFortresses_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasStellarGardens_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasStellarGardens = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasStellarGardens = { "bHasStellarGardens", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasStellarGardens_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasStellarGardens_MetaData), NewProp_bHasStellarGardens_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasVoidRifts_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasVoidRifts = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasVoidRifts = { "bHasVoidRifts", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasVoidRifts_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasVoidRifts_MetaData), NewProp_bHasVoidRifts_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasSpectralPlains_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasSpectralPlains = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasSpectralPlains = { "bHasSpectralPlains", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasSpectralPlains_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasSpectralPlains_MetaData), NewProp_bHasSpectralPlains_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasRiversOfSouls_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasRiversOfSouls = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasRiversOfSouls = { "bHasRiversOfSouls", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasRiversOfSouls_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasRiversOfSouls_MetaData), NewProp_bHasRiversOfSouls_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasFragmentedStructures_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasFragmentedStructures = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasFragmentedStructures = { "bHasFragmentedStructures", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasFragmentedStructures_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasFragmentedStructures_MetaData), NewProp_bHasFragmentedStructures_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTemporalDistortionZones_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasTemporalDistortionZones = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTemporalDistortionZones = { "bHasTemporalDistortionZones", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTemporalDistortionZones_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasTemporalDistortionZones_MetaData), NewProp_bHasTemporalDistortionZones_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0040000000000021, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentType_MetaData), NewProp_EnvironmentType_MetaData) }; // 2161956974
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_GeneratedActors_Inner = { "GeneratedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_GeneratedActors = { "GeneratedActors", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, GeneratedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedActors_MetaData), NewProp_GeneratedActors_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_BreathingForests_Inner = { "BreathingForests", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FBreathingForestData, METADATA_PARAMS(0, nullptr) }; // 59522974
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_BreathingForests = { "BreathingForests", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, BreathingForests), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BreathingForests_MetaData), NewProp_BreathingForests_MetaData) }; // 59522974
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_FloatingIslandData_Inner = { "FloatingIslandData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FFloatingIslandData, METADATA_PARAMS(0, nullptr) }; // 486215822
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_FloatingIslandData = { "FloatingIslandData", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, FloatingIslandData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FloatingIslandData_MetaData), NewProp_FloatingIslandData_MetaData) }; // 486215822
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_CloudFortressData_Inner = { "CloudFortressData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FCloudFortressData, METADATA_PARAMS(0, nullptr) }; // 3992028230
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_CloudFortressData = { "CloudFortressData", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, CloudFortressData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CloudFortressData_MetaData), NewProp_CloudFortressData_MetaData) }; // 3992028230
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_StellarGardenData_Inner = { "StellarGardenData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FStellarGardenData, METADATA_PARAMS(0, nullptr) }; // 1923843116
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_StellarGardenData = { "StellarGardenData", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, StellarGardenData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StellarGardenData_MetaData), NewProp_StellarGardenData_MetaData) }; // 1923843116
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_AuroraBridgeData_Inner = { "AuroraBridgeData", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuroraBridgeData, METADATA_PARAMS(0, nullptr) }; // 2700520684
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_AuroraBridgeData = { "AuroraBridgeData", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, AuroraBridgeData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AuroraBridgeData_MetaData), NewProp_AuroraBridgeData_MetaData) }; // 2700520684
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_RiverOfSoulsData_Inner = { "RiverOfSoulsData", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRiverOfSoulsData, METADATA_PARAMS(0, nullptr) }; // 4077280521
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_RiverOfSoulsData = { "RiverOfSoulsData", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, RiverOfSoulsData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverOfSoulsData_MetaData), NewProp_RiverOfSoulsData_MetaData) }; // 4077280521
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_FragmentedStructureData_Inner = { "FragmentedStructureData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FFragmentedStructureData, METADATA_PARAMS(0, nullptr) }; // 1714163754
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_FragmentedStructureData = { "FragmentedStructureData", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, FragmentedStructureData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FragmentedStructureData_MetaData), NewProp_FragmentedStructureData_MetaData) }; // 1714163754
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_TemporalDistortionData_Inner = { "TemporalDistortionData", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTemporalDistortionData, METADATA_PARAMS(0, nullptr) }; // 3001293921
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_TemporalDistortionData = { "TemporalDistortionData", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, TemporalDistortionData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemporalDistortionData_MetaData), NewProp_TemporalDistortionData_MetaData) }; // 3001293921
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_ShadowNexusData_Inner = { "ShadowNexusData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FShadowNexusData, METADATA_PARAMS(0, nullptr) }; // 825765918
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_ShadowNexusData = { "ShadowNexusData", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, ShadowNexusData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShadowNexusData_MetaData), NewProp_ShadowNexusData_MetaData) }; // 825765918
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_TowerOfLamentationData_Inner = { "TowerOfLamentationData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTowerOfLamentationData, METADATA_PARAMS(0, nullptr) }; // 3813930515
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_TowerOfLamentationData = { "TowerOfLamentationData", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, TowerOfLamentationData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerOfLamentationData_MetaData), NewProp_TowerOfLamentationData_MetaData) }; // 3813930515
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_SpectralGuardianData = { "SpectralGuardianData", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, SpectralGuardianData), Z_Construct_UScriptStruct_FSpectralGuardianData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralGuardianData_MetaData), NewProp_SpectralGuardianData_MetaData) }; // 3385605490
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_PurgatoryAnchorData = { "PurgatoryAnchorData", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, PurgatoryAnchorData), Z_Construct_UScriptStruct_FPurgatoryAnchorData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PurgatoryAnchorData_MetaData), NewProp_PurgatoryAnchorData_MetaData) }; // 33934020
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_VoidRiftParticleSystem = { "VoidRiftParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, VoidRiftParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoidRiftParticleSystem_MetaData), NewProp_VoidRiftParticleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_AuroraBridgeParticleSystem = { "AuroraBridgeParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, AuroraBridgeParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AuroraBridgeParticleSystem_MetaData), NewProp_AuroraBridgeParticleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_SpectralGuardianParticleSystem = { "SpectralGuardianParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, SpectralGuardianParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralGuardianParticleSystem_MetaData), NewProp_SpectralGuardianParticleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_PurgatoryAnchorParticleSystem = { "PurgatoryAnchorParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, PurgatoryAnchorParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PurgatoryAnchorParticleSystem_MetaData), NewProp_PurgatoryAnchorParticleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_ShadowNexusParticleSystem = { "ShadowNexusParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, ShadowNexusParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShadowNexusParticleSystem_MetaData), NewProp_ShadowNexusParticleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_TowerOfLamentationParticleSystem = { "TowerOfLamentationParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, TowerOfLamentationParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerOfLamentationParticleSystem_MetaData), NewProp_TowerOfLamentationParticleSystem_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasShadowStructures_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasShadowStructures = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasShadowStructures = { "bHasShadowStructures", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasShadowStructures_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasShadowStructures_MetaData), NewProp_bHasShadowStructures_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_ActivityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCrystallinePlateaus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasLivingCanyons,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasBreathingForests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTectonicBridges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasOrbitalArchipelagos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasAuroraBridges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCloudFortresses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasStellarGardens,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasVoidRifts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasSpectralPlains,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasRiversOfSouls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasFragmentedStructures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTemporalDistortionZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_GeneratedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_GeneratedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_BreathingForests_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_BreathingForests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_FloatingIslandData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_FloatingIslandData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_CloudFortressData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_CloudFortressData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_StellarGardenData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_StellarGardenData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_AuroraBridgeData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_AuroraBridgeData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_RiverOfSoulsData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_RiverOfSoulsData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_FragmentedStructureData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_FragmentedStructureData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_TemporalDistortionData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_TemporalDistortionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_ShadowNexusData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_ShadowNexusData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_TowerOfLamentationData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_TowerOfLamentationData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_SpectralGuardianData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_PurgatoryAnchorData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_VoidRiftParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_AuroraBridgeParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_SpectralGuardianParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_PurgatoryAnchorParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_ShadowNexusParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_TowerOfLamentationParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasShadowStructures,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::ClassParams = {
	&AAURACRONPCGEnvironment::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGEnvironment()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGEnvironment.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGEnvironment.OuterSingleton, Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGEnvironment.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AAURACRONPCGEnvironment::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_ActivityScale(TEXT("ActivityScale"));
	static FName Name_bHasCrystallinePlateaus(TEXT("bHasCrystallinePlateaus"));
	static FName Name_bHasLivingCanyons(TEXT("bHasLivingCanyons"));
	static FName Name_bHasBreathingForests(TEXT("bHasBreathingForests"));
	static FName Name_bHasTectonicBridges(TEXT("bHasTectonicBridges"));
	static FName Name_bHasOrbitalArchipelagos(TEXT("bHasOrbitalArchipelagos"));
	static FName Name_bHasAuroraBridges(TEXT("bHasAuroraBridges"));
	static FName Name_bHasCloudFortresses(TEXT("bHasCloudFortresses"));
	static FName Name_bHasStellarGardens(TEXT("bHasStellarGardens"));
	static FName Name_bHasVoidRifts(TEXT("bHasVoidRifts"));
	static FName Name_bHasSpectralPlains(TEXT("bHasSpectralPlains"));
	static FName Name_bHasRiversOfSouls(TEXT("bHasRiversOfSouls"));
	static FName Name_bHasFragmentedStructures(TEXT("bHasFragmentedStructures"));
	static FName Name_bHasTemporalDistortionZones(TEXT("bHasTemporalDistortionZones"));
	static FName Name_EnvironmentType(TEXT("EnvironmentType"));
	static FName Name_bIsActive(TEXT("bIsActive"));
	const bool bIsValid = true
		&& Name_ActivityScale == ClassReps[(int32)ENetFields_Private::ActivityScale].Property->GetFName()
		&& Name_bHasCrystallinePlateaus == ClassReps[(int32)ENetFields_Private::bHasCrystallinePlateaus].Property->GetFName()
		&& Name_bHasLivingCanyons == ClassReps[(int32)ENetFields_Private::bHasLivingCanyons].Property->GetFName()
		&& Name_bHasBreathingForests == ClassReps[(int32)ENetFields_Private::bHasBreathingForests].Property->GetFName()
		&& Name_bHasTectonicBridges == ClassReps[(int32)ENetFields_Private::bHasTectonicBridges].Property->GetFName()
		&& Name_bHasOrbitalArchipelagos == ClassReps[(int32)ENetFields_Private::bHasOrbitalArchipelagos].Property->GetFName()
		&& Name_bHasAuroraBridges == ClassReps[(int32)ENetFields_Private::bHasAuroraBridges].Property->GetFName()
		&& Name_bHasCloudFortresses == ClassReps[(int32)ENetFields_Private::bHasCloudFortresses].Property->GetFName()
		&& Name_bHasStellarGardens == ClassReps[(int32)ENetFields_Private::bHasStellarGardens].Property->GetFName()
		&& Name_bHasVoidRifts == ClassReps[(int32)ENetFields_Private::bHasVoidRifts].Property->GetFName()
		&& Name_bHasSpectralPlains == ClassReps[(int32)ENetFields_Private::bHasSpectralPlains].Property->GetFName()
		&& Name_bHasRiversOfSouls == ClassReps[(int32)ENetFields_Private::bHasRiversOfSouls].Property->GetFName()
		&& Name_bHasFragmentedStructures == ClassReps[(int32)ENetFields_Private::bHasFragmentedStructures].Property->GetFName()
		&& Name_bHasTemporalDistortionZones == ClassReps[(int32)ENetFields_Private::bHasTemporalDistortionZones].Property->GetFName()
		&& Name_EnvironmentType == ClassReps[(int32)ENetFields_Private::EnvironmentType].Property->GetFName()
		&& Name_bIsActive == ClassReps[(int32)ENetFields_Private::bIsActive].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AAURACRONPCGEnvironment"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGEnvironment);
AAURACRONPCGEnvironment::~AAURACRONPCGEnvironment() {}
// ********** End Class AAURACRONPCGEnvironment ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FBreathingForestData::StaticStruct, Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewStructOps, TEXT("BreathingForestData"), &Z_Registration_Info_UScriptStruct_FBreathingForestData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FBreathingForestData), 59522974U) },
		{ FFloatingIslandData::StaticStruct, Z_Construct_UScriptStruct_FFloatingIslandData_Statics::NewStructOps, TEXT("FloatingIslandData"), &Z_Registration_Info_UScriptStruct_FFloatingIslandData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FFloatingIslandData), 486215822U) },
		{ FCloudFortressData::StaticStruct, Z_Construct_UScriptStruct_FCloudFortressData_Statics::NewStructOps, TEXT("CloudFortressData"), &Z_Registration_Info_UScriptStruct_FCloudFortressData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FCloudFortressData), 3992028230U) },
		{ FStellarGardenData::StaticStruct, Z_Construct_UScriptStruct_FStellarGardenData_Statics::NewStructOps, TEXT("StellarGardenData"), &Z_Registration_Info_UScriptStruct_FStellarGardenData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FStellarGardenData), 1923843116U) },
		{ FAuroraBridgeData::StaticStruct, Z_Construct_UScriptStruct_FAuroraBridgeData_Statics::NewStructOps, TEXT("AuroraBridgeData"), &Z_Registration_Info_UScriptStruct_FAuroraBridgeData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuroraBridgeData), 2700520684U) },
		{ FRiverOfSoulsData::StaticStruct, Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics::NewStructOps, TEXT("RiverOfSoulsData"), &Z_Registration_Info_UScriptStruct_FRiverOfSoulsData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRiverOfSoulsData), 4077280521U) },
		{ FFragmentedStructureData::StaticStruct, Z_Construct_UScriptStruct_FFragmentedStructureData_Statics::NewStructOps, TEXT("FragmentedStructureData"), &Z_Registration_Info_UScriptStruct_FFragmentedStructureData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FFragmentedStructureData), 1714163754U) },
		{ FTemporalDistortionData::StaticStruct, Z_Construct_UScriptStruct_FTemporalDistortionData_Statics::NewStructOps, TEXT("TemporalDistortionData"), &Z_Registration_Info_UScriptStruct_FTemporalDistortionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTemporalDistortionData), 3001293921U) },
		{ FShadowNexusData::StaticStruct, Z_Construct_UScriptStruct_FShadowNexusData_Statics::NewStructOps, TEXT("ShadowNexusData"), &Z_Registration_Info_UScriptStruct_FShadowNexusData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FShadowNexusData), 825765918U) },
		{ FTowerOfLamentationData::StaticStruct, Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics::NewStructOps, TEXT("TowerOfLamentationData"), &Z_Registration_Info_UScriptStruct_FTowerOfLamentationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTowerOfLamentationData), 3813930515U) },
		{ FPurgatoryAnchorData::StaticStruct, Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics::NewStructOps, TEXT("PurgatoryAnchorData"), &Z_Registration_Info_UScriptStruct_FPurgatoryAnchorData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPurgatoryAnchorData), 33934020U) },
		{ FSpectralGuardianData::StaticStruct, Z_Construct_UScriptStruct_FSpectralGuardianData_Statics::NewStructOps, TEXT("SpectralGuardianData"), &Z_Registration_Info_UScriptStruct_FSpectralGuardianData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSpectralGuardianData), 3385605490U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGEnvironment, AAURACRONPCGEnvironment::StaticClass, TEXT("AAURACRONPCGEnvironment"), &Z_Registration_Info_UClass_AAURACRONPCGEnvironment, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGEnvironment), 431702714U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_3335893903(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
