// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGArsenalIsland.h"

#ifdef AURACRON_AURACRONPCGArsenalIsland_generated_h
#error "AURACRONPCGArsenalIsland.generated.h already included, missing '#pragma once' in AURACRONPCGArsenalIsland.h"
#endif
#define AURACRON_AURACRONPCGArsenalIsland_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;

// ********** Begin Class AArsenalIsland ***********************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h_23_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execRemoveArsenalEffects); \
	DECLARE_FUNCTION(execSetNearEnvironmentTransition); \
	DECLARE_FUNCTION(execIsNearEnvironmentTransition); \
	DECLARE_FUNCTION(execGrantAbilityBoost); \
	DECLARE_FUNCTION(execGrantSpecialAmmo); \
	DECLARE_FUNCTION(execGrantWeaponBonus);


AURACRON_API UClass* Z_Construct_UClass_AArsenalIsland_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h_23_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAArsenalIsland(); \
	friend struct Z_Construct_UClass_AArsenalIsland_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AArsenalIsland_NoRegister(); \
public: \
	DECLARE_CLASS2(AArsenalIsland, APrismalFlowIsland, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AArsenalIsland_NoRegister) \
	DECLARE_SERIALIZER(AArsenalIsland) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		WeaponBonusIntensity=NETFIELD_REP_START, \
		WeaponBonusDuration, \
		SpecialAmmoCount, \
		AbilityBoostIntensity, \
		AbilityBoostDuration, \
		WeaponBonusEffect, \
		SpecialAmmoEffect, \
		AbilityBoostEffect, \
		NETFIELD_REP_END=AbilityBoostEffect	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h_23_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AArsenalIsland(AArsenalIsland&&) = delete; \
	AArsenalIsland(const AArsenalIsland&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AArsenalIsland); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AArsenalIsland); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AArsenalIsland) \
	NO_API virtual ~AArsenalIsland();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h_20_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h_23_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h_23_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h_23_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h_23_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AArsenalIsland;

// ********** End Class AArsenalIsland *************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
