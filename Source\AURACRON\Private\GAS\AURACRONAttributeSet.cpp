// AURACRONAttributeSet.cpp
// Sistema de Sígilos AURACRON - Implementação do Conjunto de Atributos UE 5.6

#include "GAS/AURACRONAttributeSet.h"
#include "Net/UnrealNetwork.h"
#include "GameplayEffect.h"
#include "GameplayEffectExtension.h"
#include "AbilitySystemBlueprintLibrary.h"
#include "GameFramework/Character.h"

UAURACRONAttributeSet::UAURACRONAttributeSet()
{
    // Inicializar valores padrão dos atributos
    InitHealth(100.0f);
    InitMaxHealth(100.0f);
    InitMana(100.0f);
    InitMaxMana(100.0f);
    InitAttackDamage(50.0f);
    InitAbilityPower(50.0f);
    InitArmor(20.0f);
    InitMagicResistance(20.0f);
    InitAttackSpeed(1.0f);
    InitCriticalChance(0.05f);
    InitMovementSpeed(500.0f);
    InitHealthRegeneration(2.0f);
    InitManaRegeneration(5.0f);
    InitCooldownReduction(0.0f);
    InitSigilEfficiency(1.0f);
    InitMaxSigilSlots(3.0f);
}

void UAURACRONAttributeSet::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, Health, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, MaxHealth, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, Mana, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, MaxMana, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, AttackDamage, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, AbilityPower, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, Armor, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, MagicResistance, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, AttackSpeed, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, CriticalChance, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, MovementSpeed, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, HealthRegeneration, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, ManaRegeneration, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, CooldownReduction, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, SigilEfficiency, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, MaxSigilSlots, COND_None, REPNOTIFY_Always);
}

void UAURACRONAttributeSet::PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue)
{
    Super::PreAttributeChange(Attribute, NewValue);

    // Clampar valores para evitar valores inválidos
    if (Attribute == GetHealthAttribute())
    {
        NewValue = FMath::Clamp(NewValue, 0.0f, GetMaxHealth());
    }
    else if (Attribute == GetManaAttribute())
    {
        NewValue = FMath::Clamp(NewValue, 0.0f, GetMaxMana());
    }
    else if (Attribute == GetMaxHealthAttribute())
    {
        NewValue = FMath::Max(NewValue, 1.0f);
    }
    else if (Attribute == GetMaxManaAttribute())
    {
        NewValue = FMath::Max(NewValue, 0.0f);
    }
    else if (Attribute == GetCriticalChanceAttribute())
    {
        NewValue = FMath::Clamp(NewValue, 0.0f, 1.0f);
    }
    else if (Attribute == GetCooldownReductionAttribute())
    {
        NewValue = FMath::Clamp(NewValue, 0.0f, 0.8f); // Máximo 80% de redução
    }
    else if (Attribute == GetSigilEfficiencyAttribute())
    {
        NewValue = FMath::Max(NewValue, 0.1f); // Mínimo 10% de eficiência
    }
    else if (Attribute == GetMaxSigilSlotsAttribute())
    {
        NewValue = FMath::Clamp(NewValue, 1.0f, 6.0f); // Entre 1 e 6 slots
    }
}

void UAURACRONAttributeSet::PostGameplayEffectExecute(const FGameplayEffectModCallbackData& Data)
{
    Super::PostGameplayEffectExecute(Data);

    FGameplayEffectContextHandle Context = Data.EffectSpec.GetContext();
    UAbilitySystemComponent* Source = Context.GetOriginalInstigatorAbilitySystemComponent();
    const FGameplayTagContainer& SourceTags = *Data.EffectSpec.CapturedSourceTags.GetAggregatedTags();
    const FGameplayTagContainer& TargetTags = *Data.EffectSpec.CapturedTargetTags.GetAggregatedTags();

    // Obter o ator alvo
    AActor* TargetActor = nullptr;
    ACharacter* TargetCharacter = nullptr;
    if (Data.Target.AbilityActorInfo.IsValid() && Data.Target.AbilityActorInfo->AvatarActor.IsValid())
    {
        TargetActor = Data.Target.AbilityActorInfo->AvatarActor.Get();
        TargetCharacter = Cast<ACharacter>(TargetActor);
    }

    // Ajustar atributos após mudanças
    if (Data.EvaluatedData.Attribute == GetHealthAttribute())
    {
        SetHealth(FMath::Clamp(GetHealth(), 0.0f, GetMaxHealth()));
    }
    else if (Data.EvaluatedData.Attribute == GetManaAttribute())
    {
        SetMana(FMath::Clamp(GetMana(), 0.0f, GetMaxMana()));
    }
    else if (Data.EvaluatedData.Attribute == GetMaxHealthAttribute())
    {
        // Ajustar vida atual quando vida máxima muda
        AdjustAttributeForMaxChange(Health, MaxHealth, GetMaxHealth(), GetHealthAttribute());
    }
    else if (Data.EvaluatedData.Attribute == GetMaxManaAttribute())
    {
        // Ajustar mana atual quando mana máxima muda
        AdjustAttributeForMaxChange(Mana, MaxMana, GetMaxMana(), GetManaAttribute());
    }
}

void UAURACRONAttributeSet::OnRep_Health(const FGameplayAttributeData& OldHealth)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, Health, OldHealth);
}

void UAURACRONAttributeSet::OnRep_MaxHealth(const FGameplayAttributeData& OldMaxHealth)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, MaxHealth, OldMaxHealth);
}

void UAURACRONAttributeSet::OnRep_Mana(const FGameplayAttributeData& OldMana)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, Mana, OldMana);
}

void UAURACRONAttributeSet::OnRep_MaxMana(const FGameplayAttributeData& OldMaxMana)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, MaxMana, OldMaxMana);
}

void UAURACRONAttributeSet::OnRep_AttackDamage(const FGameplayAttributeData& OldAttackDamage)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, AttackDamage, OldAttackDamage);
}

void UAURACRONAttributeSet::OnRep_AbilityPower(const FGameplayAttributeData& OldAbilityPower)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, AbilityPower, OldAbilityPower);
}

void UAURACRONAttributeSet::OnRep_Armor(const FGameplayAttributeData& OldArmor)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, Armor, OldArmor);
}

void UAURACRONAttributeSet::OnRep_MagicResistance(const FGameplayAttributeData& OldMagicResistance)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, MagicResistance, OldMagicResistance);
}

void UAURACRONAttributeSet::OnRep_AttackSpeed(const FGameplayAttributeData& OldAttackSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, AttackSpeed, OldAttackSpeed);
}

void UAURACRONAttributeSet::OnRep_CriticalChance(const FGameplayAttributeData& OldCriticalChance)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, CriticalChance, OldCriticalChance);
}

void UAURACRONAttributeSet::OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, MovementSpeed, OldMovementSpeed);
}

void UAURACRONAttributeSet::OnRep_HealthRegeneration(const FGameplayAttributeData& OldHealthRegeneration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, HealthRegeneration, OldHealthRegeneration);
}

void UAURACRONAttributeSet::OnRep_ManaRegeneration(const FGameplayAttributeData& OldManaRegeneration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, ManaRegeneration, OldManaRegeneration);
}

void UAURACRONAttributeSet::OnRep_CooldownReduction(const FGameplayAttributeData& OldCooldownReduction)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, CooldownReduction, OldCooldownReduction);
}

void UAURACRONAttributeSet::OnRep_SigilEfficiency(const FGameplayAttributeData& OldSigilEfficiency)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, SigilEfficiency, OldSigilEfficiency);
}

void UAURACRONAttributeSet::OnRep_MaxSigilSlots(const FGameplayAttributeData& OldMaxSigilSlots)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, MaxSigilSlots, OldMaxSigilSlots);
}

void UAURACRONAttributeSet::AdjustAttributeForMaxChange(const FGameplayAttributeData& AffectedAttribute, const FGameplayAttributeData& MaxAttribute, float NewMaxValue, const FGameplayAttribute& AffectedAttributeProperty) const
{
    UAbilitySystemComponent* AbilityComp = GetOwningAbilitySystemComponent();
    const float CurrentMaxValue = MaxAttribute.GetCurrentValue();
    if (!FMath::IsNearlyEqual(CurrentMaxValue, NewMaxValue) && AbilityComp)
    {
        // Calcular a nova proporção
        const float CurrentValue = AffectedAttribute.GetCurrentValue();
        float NewDelta = (CurrentMaxValue > 0.f) ? (CurrentValue * NewMaxValue / CurrentMaxValue) - CurrentValue : NewMaxValue;

        AbilityComp->ApplyModToAttributeUnsafe(AffectedAttributeProperty, EGameplayModOp::Additive, NewDelta);
    }
}
