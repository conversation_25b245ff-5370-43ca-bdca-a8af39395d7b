// AURACRONPCGNexusIsland.cpp
// Implementação da classe ANexusIsland para o sistema Prismal Flow

#include "PCG/AURACRONPCGNexusIsland.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"

ANexusIsland::ANexusIsland()
{
    // Configuração padrão
    PrimaryActorTick.bCanEverTick = true;
    
    // Inicializar propriedades
    PowerIntensity = 2.0f;
    PowerDuration = 30.0f;
    
    // Inicializar o mapa de efeitos ativos
    ActiveFlowManipulationEffects.Empty();
    
    // Configurar componentes específicos da Nexus Island
    
    // Torre de controle central
    CentralTower = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("CentralTower"));
    CentralTower->SetupAttachment(RootComponent);
    CentralTower->SetRelativeLocation(FVector(0.0f, 0.0f, 200.0f));
    CentralTower->SetRelativeScale3D(FVector(1.0f, 1.0f, 3.0f));
    CentralTower->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Efeito de energia da torre
    TowerEnergyEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("TowerEnergyEffect"));
    TowerEnergyEffect->SetupAttachment(CentralTower);
    TowerEnergyEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 300.0f));
    
    // Plataformas defensivas em múltiplos níveis
    for (int32 i = 0; i < 3; ++i)
    {
        FString PlatformName = FString::Printf(TEXT("DefensivePlatform_%d"), i);
        UStaticMeshComponent* Platform = CreateDefaultSubobject<UStaticMeshComponent>(*PlatformName);
        Platform->SetupAttachment(RootComponent);
        
        // Posicionar em diferentes alturas e distâncias do centro
        float Angle = 2.0f * PI * i / 3.0f;
        float Radius = 300.0f + i * 100.0f;
        float Height = 50.0f + i * 75.0f;
        
        Platform->SetRelativeLocation(FVector(Radius * FMath::Cos(Angle), Radius * FMath::Sin(Angle), Height));
        Platform->SetRelativeScale3D(FVector(1.0f + i * 0.2f));
        Platform->SetCollisionProfileName(TEXT("BlockAll"));
        
        DefensivePlatforms.Add(Platform);
    }
    
    // Geradores de recursos
    ResourceGenerator = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ResourceGenerator"));
    ResourceGenerator->SetupAttachment(RootComponent);
    ResourceGenerator->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
    ResourceGenerator->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Efeito do gerador de recursos
    ResourceEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("ResourceEffect"));
    ResourceEffect->SetupAttachment(ResourceGenerator);
}

void ANexusIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Atualizar efeitos visuais
    if (bIsActive)
    {
        // Acumular tempo para efeitos pulsantes
        AccumulatedTime += DeltaTime;
        
        // Atualizar efeito da torre central
        if (TowerEnergyEffect && IsValid(TowerEnergyEffect))
        {
            float PulseIntensity = 1.0f + 0.5f * FMath::Sin(AccumulatedTime * 2.0f);
            TowerEnergyEffect->SetFloatParameter(FName("Intensity"), PulseIntensity * PowerIntensity);
            
            // Rotação da torre
            if (CentralTower && IsValid(CentralTower))
            {
                FRotator NewRotation = CentralTower->GetRelativeRotation();
                NewRotation.Yaw += DeltaTime * 10.0f;
                CentralTower->SetRelativeRotation(NewRotation);
            }
        }
        
        // Atualizar efeito do gerador de recursos
        if (ResourceEffect && IsValid(ResourceEffect))
        {
            float ResourcePulse = 1.0f + 0.3f * FMath::Cos(AccumulatedTime * 1.5f);
            ResourceEffect->SetFloatParameter(FName("GenerationRate"), ResourcePulse);
        }
        
        // Atualizar plataformas defensivas
        for (int32 i = 0; i < DefensivePlatforms.Num(); ++i)
        {
            if (DefensivePlatforms[i] && IsValid(DefensivePlatforms[i]))
            {
                // Movimento sutil de flutuação
                FVector CurrentLocation = DefensivePlatforms[i]->GetRelativeLocation();
                float HeightOffset = 5.0f * FMath::Sin(AccumulatedTime * 0.5f + i * 0.7f);
                CurrentLocation.Z += HeightOffset;
                DefensivePlatforms[i]->SetRelativeLocation(CurrentLocation);
            }
        }
    }
}

void ANexusIsland::ApplyIslandEffect(AActor* OverlappingActor)
{
    Super::ApplyIslandEffect(OverlappingActor);
    
    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (Character && Character->IsPlayerControlled())
    {
        // Conceder habilidade de manipulação do flow
        GrantFlowManipulationAbility(Character);
        
        // Aplicar efeito visual ao personagem
        if (PowerEffect)
        {
            UNiagaraComponent* EffectComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                PowerEffect,
                Character->GetRootComponent(),
                NAME_None,
                FVector::ZeroVector,
                FRotator::ZeroRotator,
                EAttachLocation::SnapToTarget,
                true
            );
            
            if (EffectComponent)
            {
                EffectComponent->SetFloatParameter(FName("Duration"), PowerDuration);
                EffectComponent->SetFloatParameter(FName("Intensity"), PowerIntensity);
            }
        }
    }
}

void ANexusIsland::GrantFlowManipulationAbility(AActor* TargetActor)
{
    // Implementação da concessão de habilidade de manipulação do flow
    // Integração com o sistema de habilidades do jogo
    
    if (!TargetActor)
    {
        UE_LOG(LogTemp, Warning, TEXT("ANexusIsland::GrantFlowManipulationAbility - TargetActor inválido"));
        return;
    }
    
    // Obter o AbilitySystemComponent do ator alvo
    UAbilitySystemComponent* TargetASC = TargetActor->FindComponentByClass<UAbilitySystemComponent>();
    if (!TargetASC)
    {
        UE_LOG(LogTemp, Warning, TEXT("ANexusIsland::GrantFlowManipulationAbility - Ator %s não possui AbilitySystemComponent"), *TargetActor->GetName());
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("ANexusIsland::GrantFlowManipulationAbility - Concedendo habilidade de manipulação do flow para %s por %.1f segundos"), *TargetActor->GetName(), PowerDuration);
    
    // Criar contexto do efeito
    FGameplayEffectContextHandle EffectContext = TargetASC->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Aplicar efeito temporário de manipulação do fluxo
    // Nota: FlowManipulationEffect deve ser definido como uma propriedade da classe
    if (FlowManipulationEffect)
    {
        FGameplayEffectSpecHandle SpecHandle = TargetASC->MakeOutgoingSpec(FlowManipulationEffect, 1.0f, EffectContext);
        if (SpecHandle.IsValid())
        {
            // Definir duração do efeito
            SpecHandle.Data->SetDuration(PowerDuration, false);
            
            // Aplicar efeito
            FActiveGameplayEffectHandle ActiveEffectHandle = TargetASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
            
            // Armazenar o handle do efeito para referência futura
            ActiveFlowManipulationEffects.Add(TargetActor, ActiveEffectHandle);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("ANexusIsland::GrantFlowManipulationAbility - FlowManipulationEffect não configurado"));
    }
    
    // Configurar timer para remover a habilidade após a duração
    FTimerHandle TimerHandle;
    FTimerDelegate TimerDelegate;
    TimerDelegate.BindUFunction(this, FName("RemoveFlowManipulationAbility"), TargetActor);
    GetWorldTimerManager().SetTimer(TimerHandle, TimerDelegate, PowerDuration, false);
}

void ANexusIsland::RemoveFlowManipulationAbility(AActor* TargetActor)
{
    // Remover a habilidade de manipulação do flow
    if (!TargetActor)
    {
        UE_LOG(LogTemp, Warning, TEXT("ANexusIsland::RemoveFlowManipulationAbility - TargetActor inválido"));
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("ANexusIsland::RemoveFlowManipulationAbility - Removendo habilidade de manipulação do flow de %s"), *TargetActor->GetName());
    
    // Obter o AbilitySystemComponent do ator alvo
    UAbilitySystemComponent* TargetASC = TargetActor->FindComponentByClass<UAbilitySystemComponent>();
    if (!TargetASC)
    {
        UE_LOG(LogTemp, Warning, TEXT("ANexusIsland::RemoveFlowManipulationAbility - Ator %s não possui AbilitySystemComponent"), *TargetActor->GetName());
        return;
    }
    
    // Remover o efeito ativo se existir
    FActiveGameplayEffectHandle* EffectHandle = ActiveFlowManipulationEffects.Find(TargetActor);
    if (EffectHandle && EffectHandle->IsValid())
    {
        TargetASC->RemoveActiveGameplayEffect(*EffectHandle);
        ActiveFlowManipulationEffects.Remove(TargetActor);
    }
}

void ANexusIsland::UpdateIslandVisuals()
{
    Super::UpdateIslandVisuals();
    
    // Atualizar visuais específicos da Nexus Island
    if (IslandMaterial)
    {
        // Configurar parâmetros específicos da Nexus Island
        IslandMaterial->SetVectorParameterValue(FName("NexusColor"), FLinearColor(0.2f, 0.8f, 1.0f, 1.0f));
        IslandMaterial->SetScalarParameterValue(FName("NexusPower"), PowerIntensity);
    }
    
    // Atualizar efeito da ilha
    if (IslandEffect)
    {
        IslandEffect->SetFloatParameter(FName("NexusIntensity"), PowerIntensity);
    }
    
    // Atualizar torre central
    if (CentralTower && IsValid(CentralTower))
    {
        // Criar material dinâmico para a torre se necessário
        UMaterialInterface* BaseMaterial = CentralTower->GetMaterial(0);
        if (BaseMaterial)
        {
            UMaterialInstanceDynamic* TowerMaterial = CentralTower->CreateAndSetMaterialInstanceDynamic(0);
            if (TowerMaterial)
            {
                TowerMaterial->SetVectorParameterValue(FName("TowerColor"), FLinearColor(0.1f, 0.6f, 1.0f, 1.0f));
                TowerMaterial->SetScalarParameterValue(FName("TowerPower"), PowerIntensity * 1.5f);
            }
        }
    }
}

void ANexusIsland::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    // Replicar propriedades relevantes
    DOREPLIFETIME(ANexusIsland, PowerIntensity);
    DOREPLIFETIME(ANexusIsland, PowerDuration);
    DOREPLIFETIME(ANexusIsland, FlowManipulationEffect);
}