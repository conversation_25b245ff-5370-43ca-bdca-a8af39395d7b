<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <None Include="..\..\AURACRON.uproject" />
    <Filter Include="Source">
      <UniqueIdentifier>{F31BBDD1-B3E8-3BCC-9652-680E16935819}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\AURACRON.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\Source\AURACRONEditor.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\.vsconfig" />
    <None Include="..\..\AURACRON.uplugin" />
    <None Include="..\..\AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md" />
    <None Include="..\..\AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED_EN.md" />
    <None Include="..\..\DOCUMENTACAO_TECNICA_ADAPTIVE_AI_JUNGLE.md" />
    <None Include="..\..\IMPLEMENTACAO_FFASTARRAYSERIALIZER_UE56_COMPLETA.md" />
    <Filter Include="Config">
      <UniqueIdentifier>{FA535FFB-25E1-3D20-B416-52F9BE21E06E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Config\DefaultEditor.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultEngine.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultGame.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultGameplayTags.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultInput.ini">
      <Filter>Config</Filter>
    </None>
    <Filter Include="Source\AURACRON">
      <UniqueIdentifier>{38706F87-0F5F-30F3-ACA7-4436234CDD96}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\AURACRON\AURACRON.Build.cs">
      <Filter>Source\AURACRON</Filter>
    </None>
    <Filter Include="Source\AURACRON\Private">
      <UniqueIdentifier>{E7F264DC-6C17-3666-B3EC-EA0F417E010B}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\AURACRON.cpp">
      <Filter>Source\AURACRON\Private</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\Debug">
      <UniqueIdentifier>{5CA211E5-903D-3F0E-B6FA-F605AF169081}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\Debug\SigilDebugCommands.cpp">
      <Filter>Source\AURACRON\Private\Debug</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\Fusion">
      <UniqueIdentifier>{B9FD29CB-A148-31BF-9CA5-1E7ED1610E91}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\Fusion\SigilFusionSystem.cpp">
      <Filter>Source\AURACRON\Private\Fusion</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\Multiplayer">
      <UniqueIdentifier>{87751589-A20E-3B72-B70A-6F50BDDFBEE6}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\Multiplayer\SigilNetworkConfig.cpp">
      <Filter>Source\AURACRON\Private\Multiplayer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Multiplayer\SigilReplicationManager.cpp">
      <Filter>Source\AURACRON\Private\Multiplayer</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\Sigils">
      <UniqueIdentifier>{80360CA7-8B2F-39AA-B92B-398EE52BBAF6}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilAbilities.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilAbilityEffects.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilAttributeSet.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilGameplayEffects.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilItem.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilManagerComponent.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\UI">
      <UniqueIdentifier>{BB7EA195-13C7-3081-BB04-EEA3AF0198F8}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\UI\SigilWidgets.cpp">
      <Filter>Source\AURACRON\Private\UI</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\VFX">
      <UniqueIdentifier>{8790BEC4-8721-3863-A2D4-D0B14F220986}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\VFX\SigilVFXManager.cpp">
      <Filter>Source\AURACRON\Private\VFX</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Public">
      <UniqueIdentifier>{30D56F11-4DA0-389E-A273-B3AD9FE16C61}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source\AURACRON\Public\Debug">
      <UniqueIdentifier>{933836EB-647E-3963-9D86-9BDA2A7952CB}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Debug\SigilDebugCommands.h">
      <Filter>Source\AURACRON\Public\Debug</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\Fusion">
      <UniqueIdentifier>{1F4ABCA1-0E40-3D52-90E7-908958832904}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Fusion\SigilFusionSystem.h">
      <Filter>Source\AURACRON\Public\Fusion</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\Multiplayer">
      <UniqueIdentifier>{CFE59D4B-D59F-3613-8909-2394124C68D8}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Multiplayer\SigilNetworkConfig.h">
      <Filter>Source\AURACRON\Public\Multiplayer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Multiplayer\SigilReplicationManager.h">
      <Filter>Source\AURACRON\Public\Multiplayer</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\Sigils">
      <UniqueIdentifier>{AEFD65CA-2884-3F0F-ABD0-9C39B6E6C900}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilAbilities.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilAbilityEffects.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilAttributeSet.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilGameplayEffects.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilItem.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilManagerComponent.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\UI">
      <UniqueIdentifier>{2A483FF3-3116-36FC-A396-CAE6D21B7877}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\UI\SigilWidgets.h">
      <Filter>Source\AURACRON\Public\UI</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\VFX">
      <UniqueIdentifier>{1F5507C3-37EA-343D-B06B-FAC882ABAD30}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\VFX\SigilVFXManager.h">
      <Filter>Source\AURACRON\Public\VFX</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
