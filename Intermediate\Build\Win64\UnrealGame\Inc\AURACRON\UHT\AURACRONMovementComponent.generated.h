// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Components/AURACRONMovementComponent.h"

#ifdef AURACRON_AURACRONMovementComponent_generated_h
#error "AURACRONMovementComponent.generated.h already included, missing '#pragma once' in AURACRONMovementComponent.h"
#endif
#define AURACRON_AURACRONMovementComponent_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONMovementState : uint8;

// ********** Begin ScriptStruct FSpeedModifier ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_21_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSpeedModifier_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSpeedModifier;
// ********** End ScriptStruct FSpeedModifier ******************************************************

// ********** Begin ScriptStruct FAURACRONEnvironmentMovementConfig ********************************
#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_62_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONEnvironmentMovementConfig;
// ********** End ScriptStruct FAURACRONEnvironmentMovementConfig **********************************

// ********** Begin Class UAURACRONMovementComponent ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_97_RPC_WRAPPERS_NO_PURE_DECLS \
	virtual void RemoveSpeedModifier_Implementation(FName ModifierName); \
	virtual void ApplySpeedModifier_Implementation(float Multiplier, float Duration, FName ModifierName); \
	virtual void PerformSigilDash_Implementation(FVector Direction, float Distance, float Duration); \
	virtual void ExitPrismalFlow_Implementation(); \
	virtual void EnterPrismalFlow_Implementation(FVector FlowDirection, float FlowSpeed); \
	virtual void SetCurrentEnvironment_Implementation(EAURACRONEnvironmentType Environment); \
	virtual void SetMovementState_Implementation(EAURACRONMovementState NewState); \
	DECLARE_FUNCTION(execOnRep_CurrentEnvironment); \
	DECLARE_FUNCTION(execOnRep_MovementState); \
	DECLARE_FUNCTION(execGetModifiedSpeed); \
	DECLARE_FUNCTION(execRemoveSpeedModifier); \
	DECLARE_FUNCTION(execApplySpeedModifier); \
	DECLARE_FUNCTION(execCanPerformDash); \
	DECLARE_FUNCTION(execPerformSigilDash); \
	DECLARE_FUNCTION(execIsInPrismalFlow); \
	DECLARE_FUNCTION(execExitPrismalFlow); \
	DECLARE_FUNCTION(execEnterPrismalFlow); \
	DECLARE_FUNCTION(execGetCurrentEnvironment); \
	DECLARE_FUNCTION(execSetCurrentEnvironment); \
	DECLARE_FUNCTION(execGetMovementState); \
	DECLARE_FUNCTION(execSetMovementState);


#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_97_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_UAURACRONMovementComponent_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_97_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAURACRONMovementComponent(); \
	friend struct Z_Construct_UClass_UAURACRONMovementComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UAURACRONMovementComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(UAURACRONMovementComponent, UCharacterMovementComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UAURACRONMovementComponent_NoRegister) \
	DECLARE_SERIALIZER(UAURACRONMovementComponent) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		CurrentMovementState=NETFIELD_REP_START, \
		CurrentEnvironment, \
		bIsInPrismalFlow, \
		PrismalFlowDirection, \
		PrismalFlowSpeed, \
		bIsDashing, \
		DashTimeRemaining, \
		DashDirection, \
		DashSpeed, \
		DashCooldownRemaining, \
		NETFIELD_REP_END=DashCooldownRemaining	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_97_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAURACRONMovementComponent(UAURACRONMovementComponent&&) = delete; \
	UAURACRONMovementComponent(const UAURACRONMovementComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAURACRONMovementComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAURACRONMovementComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAURACRONMovementComponent) \
	NO_API virtual ~UAURACRONMovementComponent();


#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_94_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_97_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_97_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_97_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_97_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h_97_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAURACRONMovementComponent;

// ********** End Class UAURACRONMovementComponent *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h

// ********** Begin Enum EAURACRONMovementState ****************************************************
#define FOREACH_ENUM_EAURACRONMOVEMENTSTATE(op) \
	op(EAURACRONMovementState::Normal) \
	op(EAURACRONMovementState::PrismalFlow) \
	op(EAURACRONMovementState::SigilDash) \
	op(EAURACRONMovementState::EnvironmentBoost) \
	op(EAURACRONMovementState::Stunned) \
	op(EAURACRONMovementState::Rooted) 

enum class EAURACRONMovementState : uint8;
template<> struct TIsUEnumClass<EAURACRONMovementState> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONMovementState>();
// ********** End Enum EAURACRONMovementState ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
