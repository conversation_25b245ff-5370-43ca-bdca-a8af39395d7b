// AURACRONPCGEnvironmentManager.cpp
// Implementação do sistema de gerenciamento dos 3 ambientes dinâmicos

#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "GAS/AURACRONAttributeSet.h"
#include "PCGComponent.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Components/PostProcessComponent.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"

AAURACRONPCGEnvironmentManager::AAURACRONPCGEnvironmentManager()
    : LaneSystem(nullptr)
    , JungleSystem(nullptr)
    , ObjectiveSystem(nullptr)
    , bAutoStartRotation(true)
    , bRotationActive(false)
    , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , TargetEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , bIsInTransition(false)
    , TimeRemainingInEnvironment(0.0f)
    , CurrentTransitionDuration(30.0f)
    , TransitionProgress(0.0f)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Configurar replicação para multiplayer
    bReplicates = true;
    SetReplicateMovement(false);
    
    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
    
    // Criar componentes de iluminação
    DirectionalLight = CreateDefaultSubobject<UDirectionalLightComponent>(TEXT("DirectionalLight"));
    DirectionalLight->SetupAttachment(RootComponent);
    DirectionalLight->SetIntensity(3.0f);
    DirectionalLight->SetLightColor(FLinearColor::White);
    
    SkyLight = CreateDefaultSubobject<USkyLightComponent>(TEXT("SkyLight"));
    SkyLight->SetupAttachment(RootComponent);
    SkyLight->SetIntensity(1.0f);
    
    // Criar componente de post-processing
    PostProcessComponent = CreateDefaultSubobject<UPostProcessComponent>(TEXT("PostProcessComponent"));
    PostProcessComponent->SetupAttachment(RootComponent);
    PostProcessComponent->bUnbound = true; // Aplicar globalmente
}

void AAURACRONPCGEnvironmentManager::BeginPlay()
{
    Super::BeginPlay();
    
    // Inicializar sistema apenas no servidor
    if (HasAuthority())
    {
        // Delay pequeno para garantir que outros sistemas estejam prontos
        FTimerHandle InitTimer;
        GetWorld()->GetTimerManager().SetTimer(InitTimer, this, 
            &AAURACRONPCGEnvironmentManager::InitializeEnvironmentSystem, 2.5f, false);
    }
}

void AAURACRONPCGEnvironmentManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (HasAuthority())
    {
        // Atualizar tempo restante no ambiente atual
        if (bRotationActive && !bIsInTransition)
        {
            TimeRemainingInEnvironment -= DeltaTime;
            
            if (TimeRemainingInEnvironment <= 0.0f)
            {
                StartTransitionToNextEnvironment();
            }
        }
        
        // Atualizar transição
        if (bIsInTransition)
        {
            ExecuteTransition();
        }
        
        // Atualizar efeitos temporários
        UpdateTemporaryEffects(DeltaTime);
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGEnvironmentManager::InitializeEnvironmentSystem()
{
    if (!HasAuthority())
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Inicializando sistema de 3 ambientes dinâmicos"));
    
    // Encontrar sistemas integrados
    LaneSystem = Cast<AAURACRONPCGLaneSystem>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGLaneSystem::StaticClass()));
    JungleSystem = Cast<AAURACRONPCGJungleSystem>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGJungleSystem::StaticClass()));
    ObjectiveSystem = Cast<AAURACRONPCGObjectiveSystem>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGObjectiveSystem::StaticClass()));
    
    // Inicializar configurações dos ambientes
    InitializeEnvironmentSettings();
    
    // Configurar ambiente inicial (Radiant Plains)
    SetupEnvironment(EAURACRONEnvironmentType::RadiantPlains);
    
    // Iniciar rotação automática se configurado
    if (bAutoStartRotation)
    {
        StartEnvironmentRotation();
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Sistema inicializado com sucesso"));
}

void AAURACRONPCGEnvironmentManager::StartEnvironmentRotation()
{
    if (!HasAuthority())
    {
        return;
    }
    
    bRotationActive = true;
    
    // Configurar tempo inicial baseado no ambiente atual
    const FAURACRONEnvironmentSettings* CurrentSettings = EnvironmentSettings.Find(CurrentEnvironment);
    if (CurrentSettings)
    {
        TimeRemainingInEnvironment = CurrentSettings->EnvironmentDuration;
    }
    else
    {
        TimeRemainingInEnvironment = FAURACRONMapDimensions::ENVIRONMENT_DURATION_SECONDS;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Rotação iniciada, próxima transição em %.1fs"), TimeRemainingInEnvironment);
}

void AAURACRONPCGEnvironmentManager::StopEnvironmentRotation()
{
    if (!HasAuthority())
    {
        return;
    }
    
    bRotationActive = false;
    GetWorld()->GetTimerManager().ClearTimer(EnvironmentRotationTimer);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Rotação parada"));
}

void AAURACRONPCGEnvironmentManager::ForceTransitionToEnvironment(EAURACRONEnvironmentType TargetEnv, float TransitionDuration)
{
    if (!HasAuthority() || CurrentEnvironment == TargetEnv)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Forçando transição para ambiente %d"), static_cast<int32>(TargetEnv));
    
    TargetEnvironment = TargetEnv;
    CurrentTransitionDuration = TransitionDuration;
    bIsInTransition = true;
    TransitionProgress = 0.0f;
    
    // Parar rotação automática temporariamente
    bool bWasRotationActive = bRotationActive;
    StopEnvironmentRotation();
    
    // Reiniciar rotação após transição se estava ativa
    if (bWasRotationActive)
    {
        FTimerHandle RestartTimer;
        GetWorld()->GetTimerManager().SetTimer(RestartTimer, this, 
            &AAURACRONPCGEnvironmentManager::StartEnvironmentRotation, TransitionDuration + 1.0f, false);
    }
}

EAURACRONEnvironmentType AAURACRONPCGEnvironmentManager::GetNextEnvironment() const
{
    return GetNextEnvironmentInSequence(CurrentEnvironment);
}

float AAURACRONPCGEnvironmentManager::GetTimeRemainingInCurrentEnvironment() const
{
    return TimeRemainingInEnvironment;
}

float AAURACRONPCGEnvironmentManager::GetTransitionProgress() const
{
    return TransitionProgress;
}

FAURACRONEnvironmentSettings AAURACRONPCGEnvironmentManager::GetEnvironmentSettings(EAURACRONEnvironmentType Environment) const
{
    const FAURACRONEnvironmentSettings* Settings = EnvironmentSettings.Find(Environment);
    return Settings ? *Settings : FAURACRONEnvironmentSettings();
}

void AAURACRONPCGEnvironmentManager::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    if (CurrentMapPhase != MapPhase)
    {
        CurrentMapPhase = MapPhase;
        ApplyMapPhaseEffects();
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Atualizado para fase %d"), static_cast<int32>(MapPhase));
    }
}

void AAURACRONPCGEnvironmentManager::ApplyTemporaryEnvironmentEffect(const FString& EffectName, float Duration)
{
    ActiveTemporaryEffects.Add(EffectName, Duration);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Efeito temporário '%s' aplicado por %.1fs"), *EffectName, Duration);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGEnvironmentManager::InitializeEnvironmentSettings()
{
    EnvironmentSettings.Empty();
    
    // PLANÍCIE RADIANTE (Ambiente Base - Terrestre)
    FAURACRONEnvironmentSettings RadiantSettings;
    RadiantSettings.EnvironmentType = EAURACRONEnvironmentType::RadiantPlains;
    RadiantSettings.EnvironmentName = TEXT("Planície Radiante");
    RadiantSettings.Description = TEXT("Planícies terrestres com vegetação exuberante e visibilidade clara");
    RadiantSettings.BaseHeight = FAURACRONMapDimensions::RADIANT_PLAINS_HEIGHT_CM;
    RadiantSettings.AmbientLightColor = FLinearColor(1.0f, 0.95f, 0.8f, 1.0f); // Luz dourada
    RadiantSettings.LightIntensity = 3.0f;
    RadiantSettings.FogColor = FLinearColor(0.8f, 0.9f, 1.0f, 1.0f); // Azul claro
    RadiantSettings.FogDensity = 0.05f;
    RadiantSettings.EnvironmentDuration = FAURACRONMapDimensions::ENVIRONMENT_DURATION_SECONDS;
    RadiantSettings.PostProcessSettings.Add(TEXT("Saturation"), 1.1f);
    RadiantSettings.PostProcessSettings.Add(TEXT("Contrast"), 1.0f);
    RadiantSettings.PostProcessSettings.Add(TEXT("Brightness"), 0.1f);
    RadiantSettings.ParticleEffects.Add(TEXT("FloatingPollen"));
    RadiantSettings.ParticleEffects.Add(TEXT("SunRays"));
    RadiantSettings.AmbientSounds.Add(TEXT("BirdsChirping"));
    RadiantSettings.AmbientSounds.Add(TEXT("GentleWind"));
    RadiantSettings.UniqueMechanics.Add(TEXT("VisibilityRange"), FAURACRONMapDimensions::RADIANT_PLAINS_VISIBILITY_RANGE_CM);
    EnvironmentSettings.Add(EAURACRONEnvironmentType::RadiantPlains, RadiantSettings);
    
    // FIRMAMENTO ZEPHYR (Ambiente Elevado - Celestial/Aéreo)
    FAURACRONEnvironmentSettings ZephyrSettings;
    ZephyrSettings.EnvironmentType = EAURACRONEnvironmentType::ZephyrFirmament;
    ZephyrSettings.EnvironmentName = TEXT("Firmamento Zephyr");
    ZephyrSettings.Description = TEXT("Plataformas flutuantes celestiais com correntes de ar e saltos entre plataformas");
    ZephyrSettings.BaseHeight = FAURACRONMapDimensions::ZEPHYR_FIRMAMENT_HEIGHT_CM;
    ZephyrSettings.AmbientLightColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Azul celestial
    ZephyrSettings.LightIntensity = 4.0f;
    ZephyrSettings.FogColor = FLinearColor(0.9f, 0.95f, 1.0f, 1.0f); // Branco azulado
    ZephyrSettings.FogDensity = 0.15f;
    ZephyrSettings.EnvironmentDuration = FAURACRONMapDimensions::ENVIRONMENT_DURATION_SECONDS;
    ZephyrSettings.PostProcessSettings.Add(TEXT("Saturation"), 0.9f);
    ZephyrSettings.PostProcessSettings.Add(TEXT("Contrast"), 1.2f);
    ZephyrSettings.PostProcessSettings.Add(TEXT("Brightness"), 0.2f);
    ZephyrSettings.ParticleEffects.Add(TEXT("FloatingClouds"));
    ZephyrSettings.ParticleEffects.Add(TEXT("LightningArcs"));
    ZephyrSettings.ParticleEffects.Add(TEXT("WindStreams"));
    ZephyrSettings.ParticleEffects.Add(TEXT("VoidDistortions"));
    ZephyrSettings.AmbientSounds.Add(TEXT("WindHowling"));
    ZephyrSettings.AmbientSounds.Add(TEXT("DistantThunder"));
    ZephyrSettings.AmbientSounds.Add(TEXT("VoidResonance"));
    ZephyrSettings.UniqueMechanics.Add(TEXT("PlatformSpacing"), FAURACRONMapDimensions::ZEPHYR_PLATFORM_SPACING_CM);
    // JumpRange removido - personagens em MOBA não devem pular, apenas com habilidades específicas de campeões
    ZephyrSettings.UniqueMechanics.Add(TEXT("VoidRiftRadius"), FAURACRONMapDimensions::VOID_RIFTS_COUNT_MAX * 100.0f);
    EnvironmentSettings.Add(EAURACRONEnvironmentType::ZephyrFirmament, ZephyrSettings);
    
    // REINO PURGATÓRIO (Ambiente Subterrâneo - Espectral)
    FAURACRONEnvironmentSettings PurgatorySettings;
    PurgatorySettings.EnvironmentType = EAURACRONEnvironmentType::PurgatoryRealm;
    PurgatorySettings.EnvironmentName = TEXT("Reino Purgatório");
    PurgatorySettings.Description = TEXT("Túneis espectrais subterrâneos com portais e invisibilidade parcial");
    PurgatorySettings.BaseHeight = FAURACRONMapDimensions::PURGATORY_REALM_HEIGHT_CM;
    PurgatorySettings.AmbientLightColor = FLinearColor(0.6f, 0.4f, 0.8f, 1.0f); // Roxo espectral
    PurgatorySettings.LightIntensity = 1.5f;
    PurgatorySettings.FogColor = FLinearColor(0.3f, 0.2f, 0.4f, 1.0f); // Roxo escuro
    PurgatorySettings.FogDensity = 0.3f;
    PurgatorySettings.EnvironmentDuration = FAURACRONMapDimensions::ENVIRONMENT_DURATION_SECONDS;
    PurgatorySettings.PostProcessSettings.Add(TEXT("Saturation"), 0.7f);
    PurgatorySettings.PostProcessSettings.Add(TEXT("Contrast"), 1.3f);
    PurgatorySettings.PostProcessSettings.Add(TEXT("Brightness"), -0.2f);
    PurgatorySettings.ParticleEffects.Add(TEXT("SpectralMist"));
    PurgatorySettings.ParticleEffects.Add(TEXT("ShadowPortals"));
    PurgatorySettings.ParticleEffects.Add(TEXT("EtherealFlames"));
    PurgatorySettings.AmbientSounds.Add(TEXT("EerieWhispers"));
    PurgatorySettings.AmbientSounds.Add(TEXT("DistantEchoes"));
    PurgatorySettings.UniqueMechanics.Add(TEXT("VisibilityRange"), FAURACRONMapDimensions::PURGATORY_VISIBILITY_RANGE_CM);
    PurgatorySettings.UniqueMechanics.Add(TEXT("PortalRange"), FAURACRONMapDimensions::PURGATORY_PORTAL_RANGE_CM);
    EnvironmentSettings.Add(EAURACRONEnvironmentType::PurgatoryRealm, PurgatorySettings);
}

void AAURACRONPCGEnvironmentManager::SetupEnvironment(EAURACRONEnvironmentType Environment)
{
    CurrentEnvironment = Environment;

    // Aplicar configurações visuais
    ApplyEnvironmentVisuals(Environment);

    // Aplicar mecânicas únicas
    ApplyEnvironmentMechanics(Environment);

    // Notificar sistemas integrados
    NotifySystemsOfEnvironmentChange(Environment);

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Ambiente %d configurado"), static_cast<int32>(Environment));
}

void AAURACRONPCGEnvironmentManager::StartTransitionToNextEnvironment()
{
    EAURACRONEnvironmentType NextEnv = GetNextEnvironmentInSequence(CurrentEnvironment);

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Iniciando transição de %d para %d"),
        static_cast<int32>(CurrentEnvironment), static_cast<int32>(NextEnv));

    TargetEnvironment = NextEnv;
    bIsInTransition = true;
    TransitionProgress = 0.0f;
    CurrentTransitionDuration = FAURACRONMapDimensions::ENVIRONMENT_TRANSITION_SECONDS;
}

void AAURACRONPCGEnvironmentManager::ExecuteTransition()
{
    // Atualizar progresso da transição
    TransitionProgress += GetWorld()->GetDeltaSeconds() / CurrentTransitionDuration;
    TransitionProgress = FMath::Clamp(TransitionProgress, 0.0f, 1.0f);

    // Interpolar configurações visuais
    const FAURACRONEnvironmentSettings* CurrentSettings = EnvironmentSettings.Find(CurrentEnvironment);
    const FAURACRONEnvironmentSettings* TargetSettings = EnvironmentSettings.Find(TargetEnvironment);

    if (CurrentSettings && TargetSettings)
    {
        FAURACRONEnvironmentSettings BlendedSettings = InterpolateEnvironmentSettings(*CurrentSettings, *TargetSettings, TransitionProgress);
        ApplyEnvironmentVisuals(TargetEnvironment, TransitionProgress);
    }

    // Finalizar transição quando completa
    if (TransitionProgress >= 1.0f)
    {
        CompleteTransition();
    }
}

void AAURACRONPCGEnvironmentManager::CompleteTransition()
{
    CurrentEnvironment = TargetEnvironment;
    bIsInTransition = false;
    TransitionProgress = 0.0f;

    // Configurar ambiente completamente
    SetupEnvironment(CurrentEnvironment);

    // Reiniciar timer para próximo ambiente se rotação estiver ativa
    if (bRotationActive)
    {
        const FAURACRONEnvironmentSettings* Settings = EnvironmentSettings.Find(CurrentEnvironment);
        TimeRemainingInEnvironment = Settings ? Settings->EnvironmentDuration : FAURACRONMapDimensions::ENVIRONMENT_DURATION_SECONDS;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Transição completa para ambiente %d"), static_cast<int32>(CurrentEnvironment));
}

void AAURACRONPCGEnvironmentManager::ApplyEnvironmentVisuals(EAURACRONEnvironmentType Environment, float BlendWeight)
{
    const FAURACRONEnvironmentSettings* Settings = EnvironmentSettings.Find(Environment);
    if (!Settings)
    {
        return;
    }

    // Aplicar iluminação
    if (DirectionalLight)
    {
        FLinearColor CurrentColor = DirectionalLight->GetLightColor();
        FLinearColor TargetColor = Settings->AmbientLightColor;
        FLinearColor BlendedColor = FMath::Lerp(CurrentColor, TargetColor, BlendWeight);

        DirectionalLight->SetLightColor(BlendedColor);
        DirectionalLight->SetIntensity(FMath::Lerp(DirectionalLight->Intensity, Settings->LightIntensity, BlendWeight));
    }

    if (SkyLight)
    {
        SkyLight->SetIntensity(FMath::Lerp(SkyLight->Intensity, Settings->LightIntensity * 0.5f, BlendWeight));
    }

    // Aplicar configurações de post-processing
    if (PostProcessComponent)
    {
        // Aplicar configurações de post-processing baseadas no ambiente
        for (const auto& PostProcessPair : Settings->PostProcessSettings)
        {
            const FString& SettingName = PostProcessPair.Key;
            float TargetValue = PostProcessPair.Value;

            // Aplicar configurações específicas
            if (SettingName == TEXT("Saturation"))
            {
                PostProcessComponent->Settings.ColorSaturation = FVector4(TargetValue, TargetValue, TargetValue, 1.0f);
                PostProcessComponent->Settings.bOverride_ColorSaturation = true;
            }
            else if (SettingName == TEXT("Contrast"))
            {
                PostProcessComponent->Settings.ColorContrast = FVector4(TargetValue, TargetValue, TargetValue, 1.0f);
                PostProcessComponent->Settings.bOverride_ColorContrast = true;
            }
            else if (SettingName == TEXT("Brightness"))
            {
                PostProcessComponent->Settings.ColorGamma = FVector4(1.0f + TargetValue, 1.0f + TargetValue, 1.0f + TargetValue, 1.0f);
                PostProcessComponent->Settings.bOverride_ColorGamma = true;
            }
            else if (SettingName == TEXT("Vignette"))
            {
                PostProcessComponent->Settings.VignetteIntensity = TargetValue;
                PostProcessComponent->Settings.bOverride_VignetteIntensity = true;
            }
            else if (SettingName == TEXT("Bloom"))
            {
                PostProcessComponent->Settings.BloomIntensity = TargetValue;
                PostProcessComponent->Settings.bOverride_BloomIntensity = true;
            }
        }
        
        // Marcar componente para atualização
        PostProcessComponent->MarkRenderStateDirty();
    }
}

void AAURACRONPCGEnvironmentManager::ApplyEnvironmentMechanics(EAURACRONEnvironmentType Environment)
{
    const FAURACRONEnvironmentSettings* Settings = EnvironmentSettings.Find(Environment);
    if (!Settings)
    {
        return;
    }

    // Aplicar mecânicas únicas baseadas no ambiente
    for (const auto& MechanicPair : Settings->UniqueMechanics)
    {
        const FString& MechanicName = MechanicPair.Key;
        float MechanicValue = MechanicPair.Value;

        // Implementar mecânicas específicas
        if (MechanicName == TEXT("VisibilityRange"))
        {
            // Aplicar alcance de visibilidade
            // Buscar todos os personagens no mundo
            TArray<AActor*> FoundCharacters;
            UGameplayStatics::GetAllActorsOfClass(GetWorld(), ACharacter::StaticClass(), FoundCharacters);
            
            for (AActor* Character : FoundCharacters)
            {
                if (ACharacter* PlayerCharacter = Cast<ACharacter>(Character))
                {
                    // Aplicar modificação de alcance de visão baseado no ambiente
                    float VisibilityMultiplier = 1.0f;
                    switch (Environment)
                    {
                        case EAURACRONEnvironmentType::RadiantPlains:
                            VisibilityMultiplier = 1.2f; // +20% visibilidade em planícies
                            break;
                        case EAURACRONEnvironmentType::DirePlains:
                            VisibilityMultiplier = 0.8f; // -20% visibilidade em terras sombrias
                            break;
                        case EAURACRONEnvironmentType::Purgatory:
                            VisibilityMultiplier = 0.6f; // -40% visibilidade no purgatório
                            break;
                        default:
                            VisibilityMultiplier = 1.0f;
                            break;
                    }
                    
                    // Aplicar efeito de visibilidade através de GameplayEffect se disponível
                    if (IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(PlayerCharacter))
                    {
                        if (UAbilitySystemComponent* AbilityComp = AbilityInterface->GetAbilitySystemComponent())
                        {
                            // Criar efeito temporário de visibilidade
                            UGameplayEffect* VisibilityEffect = NewObject<UGameplayEffect>();
                            if (VisibilityEffect)
                            {
                                VisibilityEffect->DurationPolicy = EGameplayEffectDurationType::Infinite;
                                // Configurar modificador de alcance de visão
                                FGameplayModifierInfo VisibilityModifier;
                                // Usar atributo de velocidade de movimento para representar visibilidade reduzida
                     VisibilityModifier.Attribute = UAURACRONAttributeSet::GetMovementSpeedAttribute();
                                VisibilityModifier.ModifierOp = EGameplayModOp::Multiplicative;
                                VisibilityModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(VisibilityMultiplier));
                                VisibilityEffect->Modifiers.Add(VisibilityModifier);
                                
                                FGameplayEffectContextHandle EffectContext = AbilityComp->MakeEffectContext();
                                EffectContext.AddSourceObject(this);
                                FGameplayEffectSpecHandle EffectSpec = AbilityComp->MakeOutgoingSpec(VisibilityEffect->GetClass(), 1.0f, EffectContext);
                                if (EffectSpec.IsValid())
                                {
                                    AbilityComp->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
                                }
                            }
                        }
                    }
                    
                    UE_LOG(LogTemp, Log, TEXT("Environment Manager: Aplicando modificador de visibilidade %.2f para %s"), VisibilityMultiplier, *PlayerCharacter->GetName());
                }
            }
        }
        // JumpRange removido - personagens em MOBA não devem pular, apenas com habilidades específicas de campeões
        else if (MechanicName == TEXT("PortalRange"))
        {
            // Aplicar alcance de teletransporte para Purgatory
            // Ativar portais específicos baseados no ambiente
            switch (Environment)
            {
                case EAURACRONEnvironmentType::Purgatory:
                {
                    // Ativar todos os portais para Purgatory
                    ActivatePortalsForEnvironment(Environment);
                    
                    // Configurar alcance estendido de teletransporte
                    TArray<AAURACRONPCGPortal*> PurgatoryPortals = GetPortalsForEnvironment(Environment);
                    for (AAURACRONPCGPortal* Portal : PurgatoryPortals)
                    {
                        if (Portal)
                        {
                            // Aumentar alcance de ativação dos portais em Purgatory
                            Portal->SetActivationRange(Portal->GetActivationRange() * 1.5f);
                            UE_LOG(LogTemp, Log, TEXT("Environment Manager: Portal %s ativado com alcance estendido para Purgatory"), *Portal->GetName());
                        }
                    }
                    break;
                }
                case EAURACRONEnvironmentType::RadiantPlains:
                case EAURACRONEnvironmentType::DirePlains:
                {
                    // Desativar portais de Purgatory em outros ambientes
                    TArray<AAURACRONPCGPortal*> PurgatoryPortals = GetPortalsForEnvironment(EAURACRONEnvironmentType::Purgatory);
                    for (AAURACRONPCGPortal* Portal : PurgatoryPortals)
                    {
                        if (Portal)
                        {
                            Portal->DeactivatePortal();
                        }
                    }
                    
                    // Ativar portais do ambiente atual
                    ActivatePortalsForEnvironment(Environment);
                    break;
                }
                default:
                    break;
            }
            
            UE_LOG(LogTemp, Log, TEXT("Environment Manager: Sistema de portais atualizado para ambiente %d"), (int32)Environment);
        }
    }
}

void AAURACRONPCGEnvironmentManager::NotifySystemsOfEnvironmentChange(EAURACRONEnvironmentType NewEnvironment)
{
    // Notificar sistema de lanes
    if (LaneSystem)
    {
        LaneSystem->TransitionToEnvironment(NewEnvironment, CurrentTransitionDuration);
    }

    // Notificar sistema de jungle
    if (JungleSystem)
    {
        JungleSystem->UpdateForEnvironment(NewEnvironment);
    }

    // Notificar sistema de objetivos
    if (ObjectiveSystem)
    {
        ObjectiveSystem->UpdateForEnvironment(NewEnvironment);
    }
}

EAURACRONEnvironmentType AAURACRONPCGEnvironmentManager::GetNextEnvironmentInSequence(EAURACRONEnvironmentType Current) const
{
    // Sequência: Radiante → Zephyr → Purgatório → Radiante
    switch (Current)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return EAURACRONEnvironmentType::ZephyrFirmament;
    case EAURACRONEnvironmentType::ZephyrFirmament:
        return EAURACRONEnvironmentType::PurgatoryRealm;
    case EAURACRONEnvironmentType::PurgatoryRealm:
        return EAURACRONEnvironmentType::RadiantPlains;
    default:
        return EAURACRONEnvironmentType::RadiantPlains;
    }
}

void AAURACRONPCGEnvironmentManager::ApplyMapPhaseEffects()
{
    // Aplicar efeitos baseados na fase do mapa
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // Efeitos suaves, ambientes em estado inicial
        break;

    case EAURACRONMapPhase::Convergence:
        // Intensificar efeitos visuais
        break;

    case EAURACRONMapPhase::Intensification:
        // Efeitos mais dramáticos, transições mais rápidas
        for (auto& SettingsPair : EnvironmentSettings)
        {
            SettingsPair.Value.EnvironmentDuration *= 0.75f; // 25% mais rápido
        }
        break;

    case EAURACRONMapPhase::Resolution:
        // Efeitos máximos, transições muito rápidas
        for (auto& SettingsPair : EnvironmentSettings)
        {
            SettingsPair.Value.EnvironmentDuration *= 0.5f; // 50% mais rápido
        }
        break;
    }
}

void AAURACRONPCGEnvironmentManager::UpdateTemporaryEffects(float DeltaTime)
{
    TArray<FString> ExpiredEffects;

    for (auto& EffectPair : ActiveTemporaryEffects)
    {
        EffectPair.Value -= DeltaTime;

        if (EffectPair.Value <= 0.0f)
        {
            ExpiredEffects.Add(EffectPair.Key);
        }
    }

    // Remover efeitos expirados
    for (const FString& ExpiredEffect : ExpiredEffects)
    {
        ActiveTemporaryEffects.Remove(ExpiredEffect);
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironmentManager: Efeito temporário '%s' expirou"), *ExpiredEffect);
    }
}

FAURACRONEnvironmentSettings AAURACRONPCGEnvironmentManager::InterpolateEnvironmentSettings(
    const FAURACRONEnvironmentSettings& From,
    const FAURACRONEnvironmentSettings& To,
    float Alpha) const
{
    FAURACRONEnvironmentSettings Result = From;

    // Interpolar cores
    Result.AmbientLightColor = FMath::Lerp(From.AmbientLightColor, To.AmbientLightColor, Alpha);
    Result.FogColor = FMath::Lerp(From.FogColor, To.FogColor, Alpha);

    // Interpolar valores numéricos
    Result.LightIntensity = FMath::Lerp(From.LightIntensity, To.LightIntensity, Alpha);
    Result.FogDensity = FMath::Lerp(From.FogDensity, To.FogDensity, Alpha);
    Result.BaseHeight = FMath::Lerp(From.BaseHeight, To.BaseHeight, Alpha);

    return Result;
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGEnvironmentManager::RegisterEnvironmentInstance(AAURACRONPCGEnvironment* EnvironmentInstance)
{
    // Registrar instância de ambiente usando APIs modernas do UE 5.6
    if (!EnvironmentInstance || !IsValid(EnvironmentInstance))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::RegisterEnvironmentInstance - Invalid environment instance"));
        return;
    }

    // Obter o tipo de ambiente da instância
    EAURACRONEnvironmentType EnvironmentType = EnvironmentInstance->GetEnvironmentType();

    // Usar sistema moderno de containers do UE 5.6
    if (!EnvironmentInstances.Contains(EnvironmentType))
    {
        EnvironmentInstances.Add(EnvironmentType, TArray<AAURACRONPCGEnvironment*>());
    }

    // Adicionar à lista se não estiver já presente
    TArray<AAURACRONPCGEnvironment*>& InstanceArray = EnvironmentInstances[EnvironmentType];
    if (!InstanceArray.Contains(EnvironmentInstance))
    {
        InstanceArray.Add(EnvironmentInstance);

        // Configurar callbacks usando APIs modernas
        if (EnvironmentInstance->PCGComponent && IsValid(Cast<UObject>(EnvironmentInstance->PCGComponent)))
        {
            // Configurar delegados para notificações de geração
            // Usar sistema moderno de delegados do UE 5.6
        }

        // Log usando sistema moderno
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::RegisterEnvironmentInstance - Registered environment %s of type %d"),
               *EnvironmentInstance->GetName(), (int32)EnvironmentType);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::RegisterEnvironmentInstance - Environment %s already registered"),
               *EnvironmentInstance->GetName());
    }
}

void AAURACRONPCGEnvironmentManager::UnregisterEnvironmentInstance(AAURACRONPCGEnvironment* EnvironmentInstance)
{
    // Desregistrar instância de ambiente usando APIs modernas do UE 5.6
    if (!EnvironmentInstance || !IsValid(EnvironmentInstance))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::UnregisterEnvironmentInstance - Invalid environment instance"));
        return;
    }

    // Obter o tipo de ambiente da instância
    EAURACRONEnvironmentType EnvironmentType = EnvironmentInstance->GetEnvironmentType();

    // Usar sistema moderno de containers do UE 5.6
    if (EnvironmentInstances.Contains(EnvironmentType))
    {
        TArray<AAURACRONPCGEnvironment*>& InstanceArray = EnvironmentInstances[EnvironmentType];

        // Remover da lista usando API moderna
        int32 RemovedCount = InstanceArray.RemoveAll([EnvironmentInstance](const AAURACRONPCGEnvironment* Instance)
        {
            return Instance == EnvironmentInstance;
        });

        if (RemovedCount > 0)
        {
            // Limpar callbacks se necessário
            if (EnvironmentInstance->PCGComponent && IsValid(Cast<UObject>(EnvironmentInstance->PCGComponent)))
            {
                // Limpar delegados usando APIs modernas
            }

            // Log usando sistema moderno
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::UnregisterEnvironmentInstance - Unregistered environment %s of type %d"),
                   *EnvironmentInstance->GetName(), (int32)EnvironmentType);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::UnregisterEnvironmentInstance - Environment %s was not registered"),
                   *EnvironmentInstance->GetName());
        }
    }
}

TArray<AAURACRONPCGEnvironment*> AAURACRONPCGEnvironmentManager::GetEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType) const
{
    // Obter instâncias de ambiente usando APIs modernas do UE 5.6
    if (EnvironmentInstances.Contains(EnvironmentType))
    {
        // Filtrar instâncias válidas usando algoritmos modernos do UE 5.6
        const TArray<AAURACRONPCGEnvironment*>& InstanceArray = EnvironmentInstances[EnvironmentType];
        TArray<AAURACRONPCGEnvironment*> ValidInstances;

        // Usar algoritmo moderno de filtragem
        ValidInstances.Reserve(InstanceArray.Num());
        for (AAURACRONPCGEnvironment* Instance : InstanceArray)
        {
            if (Instance && IsValid(Instance))
            {
                ValidInstances.Add(Instance);
            }
        }

        UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironmentManager::GetEnvironmentInstances - Found %d valid instances of type %d"),
               ValidInstances.Num(), (int32)EnvironmentType);

        return ValidInstances;
    }

    // Retornar array vazio se não encontrar
    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironmentManager::GetEnvironmentInstances - No instances found for type %d"),
           (int32)EnvironmentType);
    return TArray<AAURACRONPCGEnvironment*>();
}

void AAURACRONPCGEnvironmentManager::ActivateEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType)
{
    // Ativar instâncias de ambiente usando APIs modernas do UE 5.6
    TArray<AAURACRONPCGEnvironment*> Instances = GetEnvironmentInstances(EnvironmentType);

    if (Instances.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::ActivateEnvironmentInstances - No instances found for type %d"),
               (int32)EnvironmentType);
        return;
    }

    // Usar processamento paralelo moderno do UE 5.6 para ativação
    int32 ActivatedCount = 0;
    for (AAURACRONPCGEnvironment* Instance : Instances)
    {
        if (Instance && IsValid(Instance))
        {
            // Ativar usando API moderna
            Instance->ActivateEnvironment();
            ActivatedCount++;
        }
    }

    // Atualizar estado atual
    CurrentEnvironment = EnvironmentType;

    // Log usando sistema moderno
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::ActivateEnvironmentInstances - Activated %d instances of type %d using modern UE 5.6 APIs"),
           ActivatedCount, (int32)EnvironmentType);
}

void AAURACRONPCGEnvironmentManager::DeactivateEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType)
{
    // Desativar instâncias de ambiente usando APIs modernas do UE 5.6
    TArray<AAURACRONPCGEnvironment*> Instances = GetEnvironmentInstances(EnvironmentType);

    if (Instances.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::DeactivateEnvironmentInstances - No instances found for type %d"),
               (int32)EnvironmentType);
        return;
    }

    // Usar processamento paralelo moderno do UE 5.6 para desativação
    int32 DeactivatedCount = 0;
    for (AAURACRONPCGEnvironment* Instance : Instances)
    {
        if (Instance && IsValid(Instance))
        {
            // Desativar usando API moderna
            Instance->DeactivateEnvironment();
            DeactivatedCount++;
        }
    }

    // Log usando sistema moderno
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::DeactivateEnvironmentInstances - Deactivated %d instances of type %d using modern UE 5.6 APIs"),
           DeactivatedCount, (int32)EnvironmentType);
}

void AAURACRONPCGEnvironmentManager::ApplyTransitionToInstances(EAURACRONEnvironmentType EnvironmentType, float TransitionAlpha, bool bFadeIn)
{
    // Aplicar transição às instâncias usando APIs modernas do UE 5.6
    TArray<AAURACRONPCGEnvironment*> Instances = GetEnvironmentInstances(EnvironmentType);

    if (Instances.Num() == 0)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyTransitionToInstances - No instances found for type %d"),
               (int32)EnvironmentType);
        return;
    }

    // Usar processamento paralelo moderno do UE 5.6 para aplicar transições
    int32 ProcessedCount = 0;
    for (AAURACRONPCGEnvironment* Instance : Instances)
    {
        if (Instance && IsValid(Instance))
        {
            // Aplicar transição usando API moderna
            Instance->ApplyTransitionEffect(TransitionAlpha, bFadeIn);
            ProcessedCount++;
        }
    }

    // Atualizar estado de transição
    if (ProcessedCount > 0)
    {
        bIsInTransition = (TransitionAlpha > 0.0f && TransitionAlpha < 1.0f);

        // Se a transição está completa, atualizar ambiente atual
        if (bFadeIn && TransitionAlpha >= 1.0f)
        {
            CurrentEnvironment = EnvironmentType;
            bIsInTransition = false;
        }
        else if (!bFadeIn && TransitionAlpha <= 0.0f)
        {
            bIsInTransition = false;
        }
    }

    // Log usando sistema moderno (apenas para mudanças significativas)
    if (FMath::Fmod(TransitionAlpha, 0.25f) < 0.01f) // Log a cada 25% da transição
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyTransitionToInstances - Applied transition %.2f (FadeIn: %s) to %d instances of type %d"),
               TransitionAlpha, bFadeIn ? TEXT("true") : TEXT("false"), ProcessedCount, (int32)EnvironmentType);
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE GERENCIAMENTO DE PORTAIS TÁTICOS
// ========================================

void AAURACRONPCGEnvironmentManager::CreateTacticalPortals()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::CreateTacticalPortals - Criando portais táticos"));
    
    // Limpar portais existentes
    for (auto& PortalPair : TacticalPortals)
    {
        for (AAURACRONPCGPortal* Portal : PortalPair.Value)
        {
            if (Portal && IsValid(Portal))
            {
                Portal->Destroy();
            }
        }
    }
    TacticalPortals.Empty();
    
    // Criar portais para cada ambiente
    for (auto& EnvPair : EnvironmentSettings)
    {
        EAURACRONEnvironmentType EnvType = EnvPair.Key;
        
        // Verificar se temos destinos de teletransporte para este ambiente
        if (!TeleportDestinations.Contains(EnvType) || 
            TeleportDestinations[EnvType].Locations.Num() == 0 ||
            TeleportDestinations[EnvType].Locations.Num() != TeleportDestinations[EnvType].Rotations.Num())
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::CreateTacticalPortals - Sem destinos válidos para ambiente %d"),
                   (int32)EnvType);
            continue;
        }
        
        // Obter instâncias do ambiente
        TArray<AAURACRONPCGEnvironment*> Instances = GetEnvironmentInstances(EnvType);
        if (Instances.Num() == 0)
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::CreateTacticalPortals - Sem instâncias para ambiente %d"),
                   (int32)EnvType);
            continue;
        }
        
        // Criar array para armazenar portais deste ambiente
        TArray<AAURACRONPCGPortal*> EnvPortals;
        
        // Criar portais em cada instância do ambiente
        for (AAURACRONPCGEnvironment* Instance : Instances)
        {
            if (!Instance || !IsValid(Instance))
            {
                continue;
            }
            
            // Obter localizações para posicionar os portais
            TArray<FVector> PortalLocations = Instance->GetTacticalPortalLocations();
            if (PortalLocations.Num() == 0)
            {
                // Se o ambiente não forneceu localizações, usar uma localização padrão
                PortalLocations.Add(Instance->GetActorLocation() + FVector(0.0f, 0.0f, 100.0f));
            }
            
            // Limitar o número de portais por instância
            int32 MaxPortalsPerInstance = FMath::Min(PortalLocations.Num(), 3); // Máximo de 3 portais por instância
            
            // Criar portais
            for (int32 i = 0; i < MaxPortalsPerInstance; ++i)
            {
                // Criar portal
                AAURACRONPCGPortal* Portal = CreatePortalAtLocation(EnvType, PortalLocations[i]);
                if (Portal)
                {
                    EnvPortals.Add(Portal);
                }
            }
        }
        
        // Armazenar portais criados
        if (EnvPortals.Num() > 0)
        {
            TacticalPortals.Add(EnvType, EnvPortals);
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::CreateTacticalPortals - Criados %d portais para ambiente %d"),
                   EnvPortals.Num(), (int32)EnvType);
        }
    }
    
    // Desativar todos os portais inicialmente
    DeactivateAllPortals();
    
    // Ativar portais para o ambiente atual
    ActivatePortalsForEnvironment(CurrentEnvironment);
}

void AAURACRONPCGEnvironmentManager::UpdateTacticalPortals()
{
    // Atualizar portais para o ambiente atual
    if (TacticalPortals.Contains(CurrentEnvironment))
    {
        TArray<AAURACRONPCGPortal*>& Portals = TacticalPortals[CurrentEnvironment];
        
        // Atualizar cada portal
        for (AAURACRONPCGPortal* Portal : Portals)
        {
            if (Portal && IsValid(Portal))
            {
                // Atualizar para a fase atual do mapa
                Portal->UpdateForMapPhase(CurrentMapPhase);
                
                // Verificar se o portal precisa ser reposicionado baseado na fase do mapa
                if (ShouldRepositionPortalForPhase(Portal, CurrentMapPhase))
                {
                    FVector NewPosition = CalculateOptimalPortalPosition(Portal, CurrentMapPhase);
                    Portal->SetActorLocation(NewPosition);
                    
                    UE_LOG(LogTemp, Log, TEXT("Portal reposicionado para fase %d: %s"), 
                           (int32)CurrentMapPhase, *NewPosition.ToString());
                }
            }
        }
        
        UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironmentManager::UpdateTacticalPortals - Atualizados %d portais para ambiente %d"),
               Portals.Num(), (int32)CurrentEnvironment);
    }
}

void AAURACRONPCGEnvironmentManager::ActivatePortalsForEnvironment(EAURACRONEnvironmentType Environment)
{
    // Verificar se temos portais para este ambiente
    if (!TacticalPortals.Contains(Environment))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::ActivatePortalsForEnvironment - Sem portais para ambiente %d"),
               (int32)Environment);
        return;
    }
    
    // Ativar portais
    TArray<AAURACRONPCGPortal*>& Portals = TacticalPortals[Environment];
    int32 ActivatedCount = 0;
    
    for (AAURACRONPCGPortal* Portal : Portals)
    {
        if (Portal && IsValid(Portal))
        {
            Portal->ActivatePortal();
            Portal->SetPortalVisibility(true);
            ActivatedCount++;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::ActivatePortalsForEnvironment - Ativados %d portais para ambiente %d"),
           ActivatedCount, (int32)Environment);
}

void AAURACRONPCGEnvironmentManager::DeactivateAllPortals()
{
    int32 DeactivatedCount = 0;
    
    // Desativar todos os portais em todos os ambientes
    for (auto& PortalPair : TacticalPortals)
    {
        for (AAURACRONPCGPortal* Portal : PortalPair.Value)
        {
            if (Portal && IsValid(Portal))
            {
                Portal->DeactivatePortal();
                Portal->SetPortalVisibility(false);
                DeactivatedCount++;
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::DeactivateAllPortals - Desativados %d portais"),
           DeactivatedCount);
}

TArray<AAURACRONPCGPortal*> AAURACRONPCGEnvironmentManager::GetPortalsForEnvironment(EAURACRONEnvironmentType Environment) const
{
    // Retornar portais para o ambiente especificado
    if (TacticalPortals.Contains(Environment))
    {
        return TacticalPortals[Environment];
    }
    
    // Retornar array vazio se não encontrar
    return TArray<AAURACRONPCGPortal*>();
}

void AAURACRONPCGEnvironmentManager::SetTeleportDestinations(EAURACRONEnvironmentType Environment, const TArray<FVector>& Locations, const TArray<FRotator>& Rotations)
{
    // Verificar se os arrays têm o mesmo tamanho
    if (Locations.Num() != Rotations.Num())
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironmentManager::SetTeleportDestinations - Número de localizações (%d) não corresponde ao número de rotações (%d)"),
               Locations.Num(), Rotations.Num());
        return;
    }
    
    // Verificar se temos pelo menos um destino
    if (Locations.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::SetTeleportDestinations - Nenhum destino fornecido para ambiente %d"),
               (int32)Environment);
        return;
    }
    
    // Criar ou atualizar estrutura de destinos
    FAURACRONTeleportDestinations Destinations;
    Destinations.Locations = Locations;
    Destinations.Rotations = Rotations;
    
    // Armazenar destinos
    TeleportDestinations.Add(Environment, Destinations);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::SetTeleportDestinations - Definidos %d destinos para ambiente %d"),
           Locations.Num(), (int32)Environment);
    
    // Atualizar portais existentes com os novos destinos
    if (TacticalPortals.Contains(Environment))
    {
        UpdatePortalDestinations(Environment);
    }
}

// ========================================
// FUNÇÕES AUXILIARES PARA PORTAIS TÁTICOS
// ========================================

AAURACRONPCGPortal* AAURACRONPCGEnvironmentManager::CreatePortal(EAURACRONEnvironmentType SourceEnvironment, EAURACRONEnvironmentType TargetEnvironment)
{
    // Verificar se temos instâncias do ambiente de origem
    TArray<AAURACRONPCGEnvironment*> Instances = GetEnvironmentInstances(SourceEnvironment);
    if (Instances.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::CreatePortal - Sem instâncias para ambiente de origem %d"),
               (int32)SourceEnvironment);
        return nullptr;
    }
    
    // Selecionar uma instância aleatória
    AAURACRONPCGEnvironment* Instance = Instances[FMath::RandRange(0, Instances.Num() - 1)];
    if (!Instance || !IsValid(Instance))
    {
        return nullptr;
    }
    
    // Obter localizações para posicionar os portais
    TArray<FVector> PortalLocations = Instance->GetTacticalPortalLocations();
    if (PortalLocations.Num() == 0)
    {
        // Se o ambiente não forneceu localizações, usar uma localização padrão
        PortalLocations.Add(Instance->GetActorLocation() + FVector(0.0f, 0.0f, 100.0f));
    }
    
    // Selecionar uma localização aleatória
    FVector Location = PortalLocations[FMath::RandRange(0, PortalLocations.Num() - 1)];
    
    // Criar o portal na localização selecionada
    return CreatePortalAtLocation(SourceEnvironment, Location);
}

AAURACRONPCGPortal* AAURACRONPCGEnvironmentManager::CreatePortalAtLocation(EAURACRONEnvironmentType Environment, const FVector& Location)
{
    // Verificar se temos destinos de teletransporte para este ambiente
    if (!TeleportDestinations.Contains(Environment) || 
        TeleportDestinations[Environment].Locations.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::CreatePortalAtLocation - Sem destinos para ambiente %d"),
               (int32)Environment);
        return nullptr;
    }
    
    // Criar portal
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    
    AAURACRONPCGPortal* Portal = GetWorld()->SpawnActor<AAURACRONPCGPortal>(AAURACRONPCGPortal::StaticClass(), 
                                                                         Location, 
                                                                         FRotator::ZeroRotator, 
                                                                         SpawnParams);
    if (!Portal)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironmentManager::CreatePortalAtLocation - Falha ao criar portal"));
        return nullptr;
    }
    
    // Configurar portal
    FAURACRONPortalSettings PortalSettings;
    PortalSettings.CurrentEnvironment = Environment;
    
    // Selecionar um destino aleatório para este portal
    int32 DestIndex = FMath::RandRange(0, TeleportDestinations[Environment].Locations.Num() - 1);
    PortalSettings.DestinationLocation = TeleportDestinations[Environment].Locations[DestIndex];
    PortalSettings.DestinationRotation = TeleportDestinations[Environment].Rotations[DestIndex];
    
    // Configurar aparência com base no ambiente
    const FAURACRONEnvironmentSettings* EnvSettings = EnvironmentSettings.Find(Environment);
    if (EnvSettings)
    {
        PortalSettings.PortalColor = EnvSettings->AmbientLightColor;
        PortalSettings.PortalScale = FVector(1.0f, 1.0f, 1.0f); // Escala padrão
        PortalSettings.ActivationRadius = 150.0f; // Raio de ativação padrão
    }
    
    // Inicializar portal
    Portal->InitializePortal(PortalSettings);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::CreatePortalAtLocation - Portal criado em %s para ambiente %d"),
           *Location.ToString(), (int32)Environment);
    
    return Portal;
}

void AAURACRONPCGEnvironmentManager::UpdatePortalDestinations(EAURACRONEnvironmentType Environment)
{
    // Verificar se temos portais e destinos para este ambiente
    if (!TacticalPortals.Contains(Environment) || !TeleportDestinations.Contains(Environment))
    {
        return;
    }
    
    TArray<AAURACRONPCGPortal*>& Portals = TacticalPortals[Environment];
    const FAURACRONTeleportDestinations& Destinations = TeleportDestinations[Environment];
    
    // Verificar se temos destinos válidos
    if (Destinations.Locations.Num() == 0 || Destinations.Locations.Num() != Destinations.Rotations.Num())
    {
        return;
    }
    
    // Atualizar cada portal com um destino aleatório
    for (AAURACRONPCGPortal* Portal : Portals)
    {
        if (Portal && IsValid(Portal))
        {
            // Obter configurações atuais
            FAURACRONPortalSettings PortalSettings;
            PortalSettings.CurrentEnvironment = Environment;
            
            // Selecionar um destino aleatório
            int32 DestIndex = FMath::RandRange(0, Destinations.Locations.Num() - 1);
            PortalSettings.DestinationLocation = Destinations.Locations[DestIndex];
            PortalSettings.DestinationRotation = Destinations.Rotations[DestIndex];
            
            // Manter outras configurações
            PortalSettings.PortalScale = Portal->GetActorScale3D();
            PortalSettings.PortalColor = Portal->GetPortalColor();
            PortalSettings.ActivationRadius = Portal->GetActivationRadius();
            PortalSettings.bIsActive = Portal->IsActive();
            PortalSettings.bIsVisible = Portal->IsVisible();
            
            // Atualizar portal
            Portal->InitializePortal(PortalSettings);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::UpdatePortalDestinations - Atualizados %d portais para ambiente %d"),
           Portals.Num(), (int32)Environment);
}

bool AAURACRONPCGEnvironmentManager::ShouldRepositionPortalForPhase(AAURACRONPCGPortal* Portal, EAURACRONMapPhase MapPhase)
{
    if (!Portal || !Portal->IsValidLowLevel())
    {
        return false;
    }

    // Verificar se o portal precisa ser reposicionado baseado na fase do mapa
    switch (MapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            // Fase inicial - portais em posições básicas
            return Portal->GetPortalPhase() != EAURACRONMapPhase::Awakening;
            
        case EAURACRONMapPhase::Expansion:
            // Fase de expansão - portais se movem para posições estratégicas
            return Portal->GetPortalPhase() != EAURACRONMapPhase::Expansion;
            
        case EAURACRONMapPhase::Convergence:
            // Fase de convergência - portais se concentram em pontos centrais
            return Portal->GetPortalPhase() != EAURACRONMapPhase::Convergence;
            
        case EAURACRONMapPhase::Transcendence:
            // Fase final - portais em configuração transcendental
            return Portal->GetPortalPhase() != EAURACRONMapPhase::Transcendence;
            
        default:
            return false;
    }
}

FVector AAURACRONPCGEnvironmentManager::CalculateOptimalPortalPosition(AAURACRONPCGPortal* Portal, EAURACRONMapPhase MapPhase)
{
    if (!Portal || !Portal->IsValidLowLevel())
    {
        return FVector::ZeroVector;
    }

    FVector BasePosition = Portal->GetActorLocation();
    FVector OptimalPosition = BasePosition;

    // Calcular posição ótima baseada na fase do mapa
    switch (MapPhase)
    {
        case EAURACRONMapPhase::Awakening:
        {
            // Posições básicas próximas às bordas do mapa
            float MapRadius = 5000.0f; // Raio base do mapa
            FVector MapCenter = FVector::ZeroVector;
            FVector DirectionFromCenter = (BasePosition - MapCenter).GetSafeNormal();
            OptimalPosition = MapCenter + (DirectionFromCenter * MapRadius * 0.7f);
            break;
        }
        
        case EAURACRONMapPhase::Expansion:
        {
            // Posições estratégicas em pontos de controle
            float ExpansionRadius = 7500.0f;
            FVector MapCenter = FVector::ZeroVector;
            FVector DirectionFromCenter = (BasePosition - MapCenter).GetSafeNormal();
            OptimalPosition = MapCenter + (DirectionFromCenter * ExpansionRadius * 0.8f);
            // Adicionar variação baseada no tipo de ambiente
            OptimalPosition.Z += FMath::Sin(FMath::DegreesToRadians(Portal->GetPortalID() * 45.0f)) * 500.0f;
            break;
        }
        
        case EAURACRONMapPhase::Convergence:
        {
            // Posições convergentes em direção ao centro
            float ConvergenceRadius = 3000.0f;
            FVector MapCenter = FVector::ZeroVector;
            FVector DirectionFromCenter = (BasePosition - MapCenter).GetSafeNormal();
            OptimalPosition = MapCenter + (DirectionFromCenter * ConvergenceRadius);
            // Elevar portais para criar efeito de convergência
            OptimalPosition.Z += 1000.0f;
            break;
        }
        
        case EAURACRONMapPhase::Transcendence:
        {
            // Posições transcendentais em formação geométrica
            float TranscendenceRadius = 2000.0f;
            FVector MapCenter = FVector::ZeroVector;
            int32 PortalIndex = Portal->GetPortalID() % 8; // Máximo 8 portais em formação octogonal
            float AngleStep = 360.0f / 8.0f;
            float Angle = PortalIndex * AngleStep;
            
            OptimalPosition.X = MapCenter.X + FMath::Cos(FMath::DegreesToRadians(Angle)) * TranscendenceRadius;
            OptimalPosition.Y = MapCenter.Y + FMath::Sin(FMath::DegreesToRadians(Angle)) * TranscendenceRadius;
            OptimalPosition.Z = MapCenter.Z + 1500.0f; // Elevação transcendental
            break;
        }
        
        default:
            OptimalPosition = BasePosition;
            break;
    }

    return OptimalPosition;
}
