// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGSubsystem.h"

#ifdef AURACRON_AURACRONPCGSubsystem_generated_h
#error "AURACRONPCGSubsystem.generated.h already included, missing '#pragma once' in AURACRONPCGSubsystem.h"
#endif
#define AURACRON_AURACRONPCGSubsystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONIslandType : uint8;
enum class EAURACRONMapPhase : uint8;
enum class EAURACRONTrailType : uint8;

// ********** Begin Class UAURACRONPCGSubsystem ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h_50_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetCurrentQualityLevel); \
	DECLARE_FUNCTION(execGenerateIsland); \
	DECLARE_FUNCTION(execUpdatePrismalFlow); \
	DECLARE_FUNCTION(execGeneratePrismalFlow); \
	DECLARE_FUNCTION(execUpdateTrailPositions); \
	DECLARE_FUNCTION(execGenerateTrail); \
	DECLARE_FUNCTION(execGenerateEnvironment); \
	DECLARE_FUNCTION(execGetCurrentMapPhase); \
	DECLARE_FUNCTION(execSetMapPhase); \
	DECLARE_FUNCTION(execAdvanceToNextPhase); \
	DECLARE_FUNCTION(execGenerateMap);


AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGSubsystem_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h_50_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAURACRONPCGSubsystem(); \
	friend struct Z_Construct_UClass_UAURACRONPCGSubsystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGSubsystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UAURACRONPCGSubsystem, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UAURACRONPCGSubsystem_NoRegister) \
	DECLARE_SERIALIZER(UAURACRONPCGSubsystem)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h_50_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAURACRONPCGSubsystem(UAURACRONPCGSubsystem&&) = delete; \
	UAURACRONPCGSubsystem(const UAURACRONPCGSubsystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAURACRONPCGSubsystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAURACRONPCGSubsystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAURACRONPCGSubsystem) \
	NO_API virtual ~UAURACRONPCGSubsystem();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h_47_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h_50_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h_50_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h_50_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h_50_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAURACRONPCGSubsystem;

// ********** End Class UAURACRONPCGSubsystem ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h

// ********** Begin Enum EAURACRONTrailType ********************************************************
#define FOREACH_ENUM_EAURACRONTRAILTYPE(op) \
	op(EAURACRONTrailType::None) \
	op(EAURACRONTrailType::Solar) \
	op(EAURACRONTrailType::Axis) \
	op(EAURACRONTrailType::Lunar) \
	op(EAURACRONTrailType::PrismalFlow) \
	op(EAURACRONTrailType::EtherealPath) \
	op(EAURACRONTrailType::NexusConnection) 

enum class EAURACRONTrailType : uint8;
template<> struct TIsUEnumClass<EAURACRONTrailType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONTrailType>();
// ********** End Enum EAURACRONTrailType **********************************************************

// ********** Begin Enum EAURACRONIslandType *******************************************************
#define FOREACH_ENUM_EAURACRONISLANDTYPE(op) \
	op(EAURACRONIslandType::None) \
	op(EAURACRONIslandType::Nexus) \
	op(EAURACRONIslandType::Sanctuary) \
	op(EAURACRONIslandType::Arsenal) \
	op(EAURACRONIslandType::Chaos) \
	op(EAURACRONIslandType::Battlefield) 

enum class EAURACRONIslandType : uint8;
template<> struct TIsUEnumClass<EAURACRONIslandType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONIslandType>();
// ********** End Enum EAURACRONIslandType *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
