// AURACRONCharacter.cpp
// Sistema de Sígilos AURACRON - Implementação da Classe Base do Personagem UE 5.6

#include "Character/AURACRONCharacter.h"
#include "AbilitySystemComponent.h"
#include "GameplayAbilitySpec.h"
#include "GameplayEffect.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"

// Includes para componentes
#include "GAS/AURACRONAttributeSet.h"
#include "Components/AURACRONSigilComponent.h"
#include "Components/AURACRONMovementComponent.h"

AAURACRONCharacter::AAURACRONCharacter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
    , TeamID(-1)
    , bAbilitySystemInitialized(false)
{
    // Configurações básicas do personagem
    PrimaryActorTick.bCanEverTick = true;
    bReplicates = true;
    SetReplicateMovement(true);

    // Configuração da cápsula de colisão
    GetCapsuleComponent()->SetCapsuleHalfHeight(88.0f);
    GetCapsuleComponent()->SetCapsuleRadius(34.0f);
    GetCapsuleComponent()->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    GetCapsuleComponent()->SetCollisionResponseToAllChannels(ECR_Block);
    GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_Camera, ECR_Ignore);

    // Configuração do movimento
    GetCharacterMovement()->bOrientRotationToMovement = true;
    GetCharacterMovement()->RotationRate = FRotator(0.0f, 540.0f, 0.0f);
    GetCharacterMovement()->JumpZVelocity = 600.0f;
    GetCharacterMovement()->AirControl = 0.2f;
    GetCharacterMovement()->MaxWalkSpeed = 500.0f;
    GetCharacterMovement()->MinAnalogWalkSpeed = 20.0f;
    GetCharacterMovement()->BrakingDecelerationWalking = 2000.0f;

    // Criar componente do sistema de habilidades
    AbilitySystemComponent = CreateDefaultSubobject<UAbilitySystemComponent>(TEXT("AbilitySystemComponent"));
    AbilitySystemComponent->SetIsReplicated(true);
    AbilitySystemComponent->SetReplicationMode(EGameplayEffectReplicationMode::Mixed);

    // Criar AttributeSet
    AttributeSet = CreateDefaultSubobject<UAURACRONAttributeSet>(TEXT("AttributeSet"));

    // Criar componente de Sígilos
    SigilComponent = CreateDefaultSubobject<UAURACRONSigilComponent>(TEXT("SigilComponent"));

    // Criar componente de movimento customizado
    AURACRONMovementComponent = CreateDefaultSubobject<UAURACRONMovementComponent>(TEXT("AURACRONMovementComponent"));

    // Inicializar arrays
    EquippedSigils.Empty();
    DefaultAbilities.Empty();
    DefaultEffects.Empty();
}

void AAURACRONCharacter::BeginPlay()
{
    Super::BeginPlay();

    // Inicializar sistema de habilidades se for o servidor ou cliente local
    if (HasAuthority() || IsLocallyControlled())
    {
        InitializeAbilitySystem();
    }
}

void AAURACRONCharacter::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar delegates de atributos
    if (AbilitySystemComponent && bAbilitySystemInitialized)
    {
        if (HealthChangedDelegateHandle.IsValid())
        {
            // Remover callback quando AttributeSet for implementado
            HealthChangedDelegateHandle.Reset();
        }
        if (ManaChangedDelegateHandle.IsValid())
        {
            // Remover callback quando AttributeSet for implementado
            ManaChangedDelegateHandle.Reset();
        }
    }

    Super::EndPlay(EndPlayReason);
}

void AAURACRONCharacter::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Lógica de tick personalizada pode ser adicionada aqui
}

void AAURACRONCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
    Super::SetupPlayerInputComponent(PlayerInputComponent);

    // Input será configurado quando o sistema de input for implementado
}

void AAURACRONCharacter::PossessedBy(AController* NewController)
{
    Super::PossessedBy(NewController);

    // Inicializar sistema de habilidades no servidor
    if (HasAuthority())
    {
        InitializeAbilitySystem();
    }
}

void AAURACRONCharacter::OnRep_PlayerState()
{
    Super::OnRep_PlayerState();

    // Inicializar sistema de habilidades no cliente
    if (!HasAuthority() && IsLocallyControlled())
    {
        InitializeAbilitySystem();
    }
}

void AAURACRONCharacter::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(AAURACRONCharacter, TeamID);
    DOREPLIFETIME(AAURACRONCharacter, EquippedSigils);
}

UAbilitySystemComponent* AAURACRONCharacter::GetAbilitySystemComponent() const
{
    return AbilitySystemComponent;
}

void AAURACRONCharacter::InitializeAbilitySystem()
{
    if (bAbilitySystemInitialized || !AbilitySystemComponent)
    {
        return;
    }

    // Inicializar o ASC
    AbilitySystemComponent->InitAbilityActorInfo(this, this);

    // Inicializar o componente de Sígilos
    if (SigilComponent)
    {
        SigilComponent->InitializeWithAbilitySystem(AbilitySystemComponent);
    }

    // Conceder habilidades padrão
    GiveDefaultAbilities();

    // Aplicar efeitos padrão
    ApplyDefaultEffects();

    // Aplicar tags iniciais
    if (InitialGameplayTags.Num() > 0)
    {
        AbilitySystemComponent->AddLooseGameplayTags(InitialGameplayTags);
    }

    // Configurar callbacks de atributos
    if (AttributeSet)
    {
        HealthChangedDelegateHandle = AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetHealthAttribute()).AddUObject(this, &AAURACRONCharacter::OnHealthAttributeChanged);
        ManaChangedDelegateHandle = AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetManaAttribute()).AddUObject(this, &AAURACRONCharacter::OnManaAttributeChanged);
    }

    bAbilitySystemInitialized = true;
}

void AAURACRONCharacter::GiveDefaultAbilities()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Conceder habilidades padrão
    for (const TSubclassOf<UGameplayAbility>& AbilityClass : DefaultAbilities)
    {
        if (AbilityClass)
        {
            FGameplayAbilitySpec AbilitySpec(AbilityClass, 1, INDEX_NONE, this);
            AbilitySystemComponent->GiveAbility(AbilitySpec);
        }
    }
}

void AAURACRONCharacter::ApplyDefaultEffects()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Aplicar efeitos padrão
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    for (const TSubclassOf<UGameplayEffect>& EffectClass : DefaultEffects)
    {
        if (EffectClass)
        {
            FGameplayEffectSpecHandle EffectSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(EffectClass, 1.0f, EffectContext);
            if (EffectSpecHandle.IsValid())
            {
                AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpecHandle.Data.Get());
            }
        }
    }
}

void AAURACRONCharacter::EquipSigil_Implementation(EAURACRONSigilType SigilType)
{
    if (!EquippedSigils.Contains(SigilType))
    {
        EquippedSigils.AddUnique(SigilType);
        OnSigilEquipped(SigilType);
    }
}

void AAURACRONCharacter::UnequipSigil_Implementation(EAURACRONSigilType SigilType)
{
    if (EquippedSigils.Remove(SigilType) > 0)
    {
        OnSigilUnequipped(SigilType);
    }
}

bool AAURACRONCharacter::IsSigilEquipped(EAURACRONSigilType SigilType) const
{
    return EquippedSigils.Contains(SigilType);
}

TArray<EAURACRONSigilType> AAURACRONCharacter::GetEquippedSigils() const
{
    return EquippedSigils;
}

float AAURACRONCharacter::GetHealth() const
{
    return AttributeSet ? AttributeSet->GetHealth() : 0.0f;
}

float AAURACRONCharacter::GetMaxHealth() const
{
    return AttributeSet ? AttributeSet->GetMaxHealth() : 0.0f;
}

float AAURACRONCharacter::GetMana() const
{
    return AttributeSet ? AttributeSet->GetMana() : 0.0f;
}

float AAURACRONCharacter::GetMaxMana() const
{
    return AttributeSet ? AttributeSet->GetMaxMana() : 0.0f;
}

float AAURACRONCharacter::GetAttackDamage() const
{
    return AttributeSet ? AttributeSet->GetAttackDamage() : 0.0f;
}

float AAURACRONCharacter::GetAbilityPower() const
{
    return AttributeSet ? AttributeSet->GetAbilityPower() : 0.0f;
}

void AAURACRONCharacter::ApplyTemporalEffect(EAURACRONTemporalEffectType EffectType, float Duration)
{
    // Implementação robusta de efeitos temporais
    switch (EffectType)
    {
        case EAURACRONTemporalEffectType::Rewind:
        {
            // Implementar rewind - salvar posição e estado atual, depois restaurar posição anterior
            FVector CurrentLocation = GetActorLocation();
            FRotator CurrentRotation = GetActorRotation();

            // Aplicar efeito visual de rewind
            if (AURACRONMovementComponent)
            {
                AURACRONMovementComponent->ApplySpeedModifier(0.0f, Duration, FName("TemporalRewind"));
            }

            // TODO: Implementar sistema de salvamento de estados temporais
            break;
        }
        case EAURACRONTemporalEffectType::Slow:
        {
            // Reduzir velocidade de movimento e animações
            if (AURACRONMovementComponent)
            {
                AURACRONMovementComponent->ApplySpeedModifier(0.5f, Duration, FName("TemporalSlow"));
            }
            break;
        }
        case EAURACRONTemporalEffectType::Accelerate:
        {
            // Aumentar velocidade de movimento e animações
            if (AURACRONMovementComponent)
            {
                AURACRONMovementComponent->ApplySpeedModifier(1.5f, Duration, FName("TemporalAccelerate"));
            }
            break;
        }
        case EAURACRONTemporalEffectType::Freeze:
        {
            // Congelar completamente o personagem
            if (AURACRONMovementComponent)
            {
                AURACRONMovementComponent->ApplySpeedModifier(0.0f, Duration, FName("TemporalFreeze"));
            }
            break;
        }
        case EAURACRONTemporalEffectType::Loop:
        {
            // Criar loop temporal - repetir ações dos últimos segundos
            // TODO: Implementar sistema de gravação e reprodução de ações
            break;
        }
        default:
            break;
    }
}

void AAURACRONCharacter::SetTeamID_Implementation(int32 NewTeamID)
{
    TeamID = NewTeamID;
}

int32 AAURACRONCharacter::GetTeamID() const
{
    return TeamID;
}

bool AAURACRONCharacter::IsAlly(const AAURACRONCharacter* OtherCharacter) const
{
    if (!OtherCharacter)
    {
        return false;
    }

    return TeamID != -1 && TeamID == OtherCharacter->GetTeamID();
}

void AAURACRONCharacter::OnRep_TeamID()
{
    // Lógica adicional quando TeamID é replicado
}

void AAURACRONCharacter::OnRep_EquippedSigils()
{
    // Lógica adicional quando EquippedSigils são replicados
}

void AAURACRONCharacter::OnHealthAttributeChanged(const struct FOnAttributeChangeData& Data)
{
    OnHealthChanged(Data.NewValue, GetMaxHealth());
}

void AAURACRONCharacter::OnManaAttributeChanged(const struct FOnAttributeChangeData& Data)
{
    OnManaChanged(Data.NewValue, GetMaxMana());
}
