{"Version": "1.2", "Data": {"Source": "c:\\auracron\\source\\auracron\\private\\pcg\\auracronpcgjunglesystem.cpp", "ProvidedModule": "", "PCH": "c:\\auracron\\intermediate\\build\\win64\\x64\\auracron\\development\\engine\\sharedpch.engine.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\auracron\\intermediate\\build\\win64\\x64\\unrealgame\\development\\auracron\\definitions.auracron.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgjunglesystem.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronmapmeasurements.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronmapmeasurements.generated.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcglanesystem.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgsubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgcommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcrc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgcrc.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\data\\pcgdataptrwrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgdataptrwrapper.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgattributepropertyselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgpoint.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\helpers\\pcgpointhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgpoint.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgattributepropertyselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgmetadatacommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgmetadatacommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgdebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgdebug.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgpin.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgpin.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\elements\\pcgactorselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgactorselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\utils\\pcgpreconfiguration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgmetadataattributetraits.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\defaultvaluehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgmetadataattributetraits.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgpreconfiguration.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\tests\\determinism\\pcgdeterminismsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgdeterminismsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\algo\\allof.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgvolume.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgvolume.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronpcgsubsystem.generated.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgenvironment.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgmathlibrary.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronpcgmathlibrary.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronpcgenvironment.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\splinecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\curves\\spline.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\spline.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\splinecomponent.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronpcglanesystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spherecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\shapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\spherecomponent.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronpcgjunglesystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\assetregistry\\arfilter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\gameplaystatics.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}