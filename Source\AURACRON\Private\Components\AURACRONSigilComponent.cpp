// AURACRONSigilComponent.cpp
// Sistema de Sígilos AURACRON - Implementação do Componente de Gerenciamento de Sígilos UE 5.6

#include "Components/AURACRONSigilComponent.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "GameplayAbility.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "TimerManager.h"

UAURACRONSigilComponent::UAURACRONSigilComponent()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // Atualizar a cada 100ms
    SetIsReplicatedByDefault(true);

    // Configurações padrão
    MaxSigilSlots = 3;
    FusionCooldown = 120.0f; // 2 minutos
    FusionDuration = 10.0f;  // 10 segundos
    FusionCooldownRemaining = 0.0f;
    bIsFusionActive = false;
    FusionTimeRemaining = 0.0f;
    bIsInitialized = false;

    // Inicializar arrays
    EquippedSigils.Empty();
    ActiveEffectHandles.Empty();
    GrantedAbilityHandles.Empty();
}

void UAURACRONSigilComponent::BeginPlay()
{
    Super::BeginPlay();

    // Tentar inicializar com o sistema de habilidades do proprietário
    if (AActor* Owner = GetOwner())
    {
        if (UAbilitySystemComponent* ASC = Owner->FindComponentByClass<UAbilitySystemComponent>())
        {
            InitializeWithAbilitySystem(ASC);
        }
    }
}

void UAURACRONSigilComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar todos os efeitos e habilidades
    for (const FAURACRONEquippedSigilInfo& SigilInfo : EquippedSigils)
    {
        RemoveSigilEffects(SigilInfo);
        RemoveSigilAbilities(SigilInfo);
    }

    EquippedSigils.Empty();
    ActiveEffectHandles.Empty();
    GrantedAbilityHandles.Empty();

    Super::EndPlay(EndPlayReason);
}

void UAURACRONSigilComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    // Atualizar cooldowns apenas no servidor
    if (GetOwner()->HasAuthority())
    {
        UpdateCooldowns(DeltaTime);
    }
}

void UAURACRONSigilComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAURACRONSigilComponent, EquippedSigils);
    DOREPLIFETIME(UAURACRONSigilComponent, FusionCooldownRemaining);
    DOREPLIFETIME(UAURACRONSigilComponent, bIsFusionActive);
    DOREPLIFETIME(UAURACRONSigilComponent, FusionTimeRemaining);
}

void UAURACRONSigilComponent::InitializeWithAbilitySystem(UAbilitySystemComponent* InAbilitySystemComponent)
{
    if (bIsInitialized || !InAbilitySystemComponent)
    {
        return;
    }

    AbilitySystemComponent = InAbilitySystemComponent;
    bIsInitialized = true;

    // Aplicar efeitos de Sígilos já equipados (para casos de reconexão)
    for (FAURACRONEquippedSigilInfo& SigilInfo : EquippedSigils)
    {
        ApplySigilEffects(SigilInfo);
        GrantSigilAbilities(SigilInfo);
    }
}

void UAURACRONSigilComponent::EquipSigil_Implementation(EAURACRONSigilType SigilType, int32 Level, int32 Rarity)
{
    // Verificar se já está equipado
    if (IsSigilEquipped(SigilType))
    {
        return;
    }

    // Verificar se há slots disponíveis
    if (!CanEquipMoreSigils())
    {
        return;
    }

    // Criar informações do Sígilo
    FAURACRONEquippedSigilInfo NewSigilInfo;
    NewSigilInfo.SigilType = SigilType;
    NewSigilInfo.Level = FMath::Clamp(Level, 1, 5);
    NewSigilInfo.Rarity = FMath::Clamp(Rarity, 1, 5);
    NewSigilInfo.EquipTime = GetWorld()->GetTimeSeconds();
    NewSigilInfo.bIsActive = true;

    // Adicionar à lista
    EquippedSigils.Add(NewSigilInfo);

    // Aplicar efeitos se o sistema estiver inicializado
    if (bIsInitialized)
    {
        ApplySigilEffects(EquippedSigils.Last());
        GrantSigilAbilities(EquippedSigils.Last());
    }

    // Chamar evento
    OnSigilEquipped(SigilType, Level, Rarity);
}

void UAURACRONSigilComponent::UnequipSigil_Implementation(EAURACRONSigilType SigilType)
{
    // Encontrar o Sígilo
    int32 SigilIndex = EquippedSigils.IndexOfByPredicate([SigilType](const FAURACRONEquippedSigilInfo& Info)
    {
        return Info.SigilType == SigilType;
    });

    if (SigilIndex == INDEX_NONE)
    {
        return;
    }

    // Remover efeitos e habilidades
    const FAURACRONEquippedSigilInfo& SigilInfo = EquippedSigils[SigilIndex];
    RemoveSigilEffects(SigilInfo);
    RemoveSigilAbilities(SigilInfo);

    // Remover da lista
    EquippedSigils.RemoveAt(SigilIndex);

    // Chamar evento
    OnSigilUnequipped(SigilType);
}

bool UAURACRONSigilComponent::IsSigilEquipped(EAURACRONSigilType SigilType) const
{
    return EquippedSigils.ContainsByPredicate([SigilType](const FAURACRONEquippedSigilInfo& Info)
    {
        return Info.SigilType == SigilType;
    });
}

FAURACRONEquippedSigilInfo UAURACRONSigilComponent::GetSigilInfo(EAURACRONSigilType SigilType) const
{
    const FAURACRONEquippedSigilInfo* FoundInfo = EquippedSigils.FindByPredicate([SigilType](const FAURACRONEquippedSigilInfo& Info)
    {
        return Info.SigilType == SigilType;
    });

    return FoundInfo ? *FoundInfo : FAURACRONEquippedSigilInfo();
}

TArray<FAURACRONEquippedSigilInfo> UAURACRONSigilComponent::GetAllEquippedSigils() const
{
    return EquippedSigils;
}

int32 UAURACRONSigilComponent::GetEquippedSigilCount() const
{
    return EquippedSigils.Num();
}

int32 UAURACRONSigilComponent::GetMaxSigilSlots() const
{
    return MaxSigilSlots;
}

bool UAURACRONSigilComponent::CanEquipMoreSigils() const
{
    return GetEquippedSigilCount() < GetMaxSigilSlots();
}

void UAURACRONSigilComponent::ActivateSigilFusion_Implementation()
{
    if (!IsFusionAvailable())
    {
        return;
    }

    // Ativar fusão
    bIsFusionActive = true;
    FusionTimeRemaining = FusionDuration;
    FusionCooldownRemaining = FusionCooldown;

    // Aplicar efeito de fusão
    if (FusionEffect && AbilitySystemComponent)
    {
        FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
        EffectContext.AddSourceObject(GetOwner());

        FGameplayEffectSpecHandle EffectSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(FusionEffect, 1.0f, EffectContext);
        if (EffectSpecHandle.IsValid())
        {
            AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpecHandle.Data.Get());
        }
    }

    // Chamar evento
    OnFusionActivated();
}

bool UAURACRONSigilComponent::IsFusionAvailable() const
{
    return FusionCooldownRemaining <= 0.0f && !bIsFusionActive && GetEquippedSigilCount() >= 2;
}

float UAURACRONSigilComponent::GetFusionCooldownRemaining() const
{
    return FusionCooldownRemaining;
}

void UAURACRONSigilComponent::ApplySigilEffects(FAURACRONEquippedSigilInfo& SigilInfo)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Obter efeitos base para este tipo de Sígilo
    TArray<TSubclassOf<UGameplayEffect>>* BaseEffects = nullptr;
    switch (SigilInfo.SigilType)
    {
        case EAURACRONSigilType::Aegis:
            BaseEffects = &AegisBaseEffects;
            break;
        case EAURACRONSigilType::Ruin:
            BaseEffects = &RuinBaseEffects;
            break;
        case EAURACRONSigilType::Vesper:
            BaseEffects = &VesperBaseEffects;
            break;
    }

    if (!BaseEffects)
    {
        return;
    }

    // Aplicar cada efeito
    TArray<FActiveGameplayEffectHandle>& EffectHandles = ActiveEffectHandles.FindOrAdd(SigilInfo.SigilType);

    for (const TSubclassOf<UGameplayEffect>& EffectClass : *BaseEffects)
    {
        if (EffectClass)
        {
            FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
            EffectContext.AddSourceObject(GetOwner());

            FGameplayEffectSpecHandle EffectSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(EffectClass, SigilInfo.Level, EffectContext);
            if (EffectSpecHandle.IsValid())
            {
                FActiveGameplayEffectHandle ActiveHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpecHandle.Data.Get());
                EffectHandles.Add(ActiveHandle);
                SigilInfo.AppliedEffects.Add(EffectClass);
            }
        }
    }
}

void UAURACRONSigilComponent::RemoveSigilEffects(const FAURACRONEquippedSigilInfo& SigilInfo)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Remover efeitos ativos
    TArray<FActiveGameplayEffectHandle>* EffectHandles = ActiveEffectHandles.Find(SigilInfo.SigilType);
    if (EffectHandles)
    {
        for (const FActiveGameplayEffectHandle& Handle : *EffectHandles)
        {
            AbilitySystemComponent->RemoveActiveGameplayEffect(Handle);
        }
        EffectHandles->Empty();
    }
}

void UAURACRONSigilComponent::GrantSigilAbilities(FAURACRONEquippedSigilInfo& SigilInfo)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Obter habilidades base para este tipo de Sígilo
    TArray<TSubclassOf<UGameplayAbility>>* BaseAbilities = nullptr;
    switch (SigilInfo.SigilType)
    {
        case EAURACRONSigilType::Aegis:
            BaseAbilities = &AegisBaseAbilities;
            break;
        case EAURACRONSigilType::Ruin:
            BaseAbilities = &RuinBaseAbilities;
            break;
        case EAURACRONSigilType::Vesper:
            BaseAbilities = &VesperBaseAbilities;
            break;
    }

    if (!BaseAbilities)
    {
        return;
    }

    // Conceder cada habilidade
    TArray<FGameplayAbilitySpecHandle>& AbilityHandles = GrantedAbilityHandles.FindOrAdd(SigilInfo.SigilType);

    for (const TSubclassOf<UGameplayAbility>& AbilityClass : *BaseAbilities)
    {
        if (AbilityClass)
        {
            FGameplayAbilitySpec AbilitySpec(AbilityClass, SigilInfo.Level, INDEX_NONE, GetOwner());
            FGameplayAbilitySpecHandle Handle = AbilitySystemComponent->GiveAbility(AbilitySpec);
            AbilityHandles.Add(Handle);
            SigilInfo.GrantedAbilities.Add(AbilityClass);
        }
    }
}

void UAURACRONSigilComponent::RemoveSigilAbilities(const FAURACRONEquippedSigilInfo& SigilInfo)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Remover habilidades concedidas
    TArray<FGameplayAbilitySpecHandle>* AbilityHandles = GrantedAbilityHandles.Find(SigilInfo.SigilType);
    if (AbilityHandles)
    {
        for (const FGameplayAbilitySpecHandle& Handle : *AbilityHandles)
        {
            AbilitySystemComponent->ClearAbility(Handle);
        }
        AbilityHandles->Empty();
    }
}

void UAURACRONSigilComponent::UpdateCooldowns(float DeltaTime)
{
    // Atualizar cooldown da fusão
    if (FusionCooldownRemaining > 0.0f)
    {
        FusionCooldownRemaining = FMath::Max(0.0f, FusionCooldownRemaining - DeltaTime);
    }

    // Atualizar tempo da fusão ativa
    if (bIsFusionActive)
    {
        FusionTimeRemaining = FMath::Max(0.0f, FusionTimeRemaining - DeltaTime);
        if (FusionTimeRemaining <= 0.0f)
        {
            bIsFusionActive = false;
        }
    }
}

void UAURACRONSigilComponent::OnRep_EquippedSigils()
{
    // Replicação dos Sígilos equipados - pode ser usado para atualizar UI
}
