// AURACRONMapMeasurements.cpp
// Sistema de Medidas e Escalas para AURACRON - UE 5.6
// Implementação das funções de medidas e conversões

#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "Kismet/KismetMathLibrary.h"

// Definição do centro do mapa
// ANÁLISE DETALHADA DOS CONCORRENTES:
// - League of Legends (Summoner's Rift): 16,000 x 16,000 units = 160m x 160m
// - Dota 2: ~11,000 x 11,000 units = 110m x 110m
// - AURACRON: 15,000 x 15,000 cm = 150m x 150m (meio termo otimizado)
const FVector FAURACRONMapDimensions::MAP_CENTER = FVector(0.0f, 0.0f, 0.0f);

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE CONVERSÃO
// ========================================

float UAURACRONMapMeasurements::MetersToUnrealUnits(float Meters)
{
    return FAURACRONMapDimensions::MetersToUnrealUnits(Meters);
}

float UAURACRONMapMeasurements::UnrealUnitsToMeters(float UnrealUnits)
{
    return FAURACRONMapDimensions::UnrealUnitsToMeters(UnrealUnits);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE POSICIONAMENTO
// ========================================

FVector UAURACRONMapMeasurements::GetEnvironmentCenter(int32 EnvironmentType)
{
    return FAURACRONMapDimensions::GetEnvironmentCenter(EnvironmentType);
}

float UAURACRONMapMeasurements::GetEnvironmentRadius(int32 EnvironmentType)
{
    return FAURACRONMapDimensions::GetEnvironmentRadius(EnvironmentType);
}

FVector UAURACRONMapMeasurements::GetPrismalFlowPosition(float T, const FVector& MapCenter)
{
    return FAURACRONMapDimensions::GetPrismalFlowPosition(T, MapCenter);
}

float UAURACRONMapMeasurements::GetPrismalFlowWidth(float T)
{
    return FAURACRONMapDimensions::GetPrismalFlowWidth(T);
}

TArray<FVector> UAURACRONMapMeasurements::GetTrailPositions(int32 TrailType, float TimeOfDay, const FVector& MapCenter)
{
    return FAURACRONMapDimensions::GetTrailPositions(TrailType, TimeOfDay, MapCenter);
}

TArray<FVector> UAURACRONMapMeasurements::GetIslandPositions(int32 IslandType, const TArray<FVector>& FlowPoints)
{
    return FAURACRONMapDimensions::GetIslandPositions(IslandType, FlowPoints);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES ESTÁTICAS
// ========================================

FVector FAURACRONMapDimensions::GetEnvironmentCenter(int32 EnvironmentType)
{
    switch (static_cast<EAURACRONEnvironmentType>(EnvironmentType))
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return MAP_CENTER;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        return MAP_CENTER + FVector(0.0f, 0.0f, ZEPHYR_HEIGHT_OFFSET_CM);
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        return MAP_CENTER + FVector(0.0f, 0.0f, PURGATORY_DEPTH_OFFSET_CM);
        
    default:
        return MAP_CENTER;
    }
}

float FAURACRONMapDimensions::GetEnvironmentRadius(int32 EnvironmentType)
{
    switch (static_cast<EAURACRONEnvironmentType>(EnvironmentType))
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return RADIANT_PLAINS_RADIUS_CM;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        return ZEPHYR_FIRMAMENT_RADIUS_CM;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        return PURGATORY_REALM_RADIUS_CM;
        
    default:
        return RADIANT_PLAINS_RADIUS_CM;
    }
}

FVector FAURACRONMapDimensions::GetPrismalFlowPosition(float T, const FVector& MapCenter)
{
    // Criar uma curva serpentina que passa pelos três ambientes
    // T varia de 0.0 a 1.0 ao longo do comprimento total do flow
    
    // Normalizar T para garantir que está no intervalo [0, 1]
    T = FMath::Clamp(T, 0.0f, 1.0f);
    
    // Criar múltiplas ondas para o padrão serpentino
    float Angle = 4.0f * PI * T; // 4 voltas completas
    float RadiusVariation = 0.3f + 0.2f * FMath::Sin(3.0f * Angle); // Variação do raio
    float Radius = MAP_RADIUS_CM * RadiusVariation;
    
    // Posição X e Y baseada na curva serpentina
    float X = MapCenter.X + Radius * FMath::Cos(Angle);
    float Y = MapCenter.Y + Radius * FMath::Sin(Angle);
    
    // Altura Z varia para passar pelos três ambientes
    float Z = MapCenter.Z + 200.0f * FMath::Sin(T * PI); // Varia de -200 a +200
    
    return FVector(X, Y, Z);
}

float FAURACRONMapDimensions::GetPrismalFlowWidth(float T)
{
    // Largura varia ao longo do flow para criar pontos de estrangulamento
    T = FMath::Clamp(T, 0.0f, 1.0f);
    
    // Usar uma função senoidal para criar variações suaves
    float WidthFactor = 0.5f + 0.5f * FMath::Sin(T * 6.0f * PI); // 6 variações ao longo do flow
    
    return FMath::Lerp(PRISMAL_FLOW_MIN_WIDTH_CM, PRISMAL_FLOW_MAX_WIDTH_CM, WidthFactor);
}

TArray<FVector> FAURACRONMapDimensions::GetTrailPositions(int32 TrailType, float TimeOfDay, const FVector& MapCenter)
{
    TArray<FVector> Positions;
    
    switch (static_cast<EAURACRONTrailType>(TrailType))
    {
    case EAURACRONTrailType::Solar:
        {
            // Solar Trails seguem a posição do sol
            const int32 NumPoints = 10;
            float SunAngleOffset = TimeOfDay * 2.0f * PI; // TimeOfDay de 0 a 1 representa 24 horas
            
            for (int32 i = 0; i < NumPoints; ++i)
            {
                float Angle = 2.0f * PI * i / NumPoints + SunAngleOffset;
                float X = MapCenter.X + SOLAR_TRAIL_RADIUS_CM * FMath::Cos(Angle);
                float Y = MapCenter.Y + SOLAR_TRAIL_RADIUS_CM * FMath::Sin(Angle);
                float Z = MapCenter.Z + TRAIL_HEIGHT_OFFSET_CM;
                
                Positions.Add(FVector(X, Y, Z));
            }
        }
        break;
        
    case EAURACRONTrailType::Axis:
        {
            // Axis Trails conectam os três ambientes verticalmente
            Positions.Add(GetEnvironmentCenter(static_cast<int32>(EAURACRONEnvironmentType::RadiantPlains)));
            Positions.Add(GetEnvironmentCenter(static_cast<int32>(EAURACRONEnvironmentType::ZephyrFirmament)));
            Positions.Add(GetEnvironmentCenter(static_cast<int32>(EAURACRONEnvironmentType::PurgatoryRealm)));
        }
        break;
        
    case EAURACRONTrailType::Lunar:
        {
            // Lunar Trails seguem a posição da lua (oposta ao sol)
            const int32 NumPoints = 8;
            float MoonAngleOffset = (TimeOfDay + 0.5f) * 2.0f * PI; // Lua oposta ao sol
            
            for (int32 i = 0; i < NumPoints; ++i)
            {
                float Angle = 2.0f * PI * i / NumPoints + MoonAngleOffset;
                float X = MapCenter.X + LUNAR_TRAIL_RADIUS_CM * FMath::Cos(Angle);
                float Y = MapCenter.Y + LUNAR_TRAIL_RADIUS_CM * FMath::Sin(Angle);
                float Z = MapCenter.Z + TRAIL_HEIGHT_OFFSET_CM;
                
                Positions.Add(FVector(X, Y, Z));
            }
        }
        break;
        
    default:
        break;
    }
    
    return Positions;
}

TArray<FVector> FAURACRONMapDimensions::GetIslandPositions(int32 IslandType, const TArray<FVector>& FlowPoints)
{
    TArray<FVector> Positions;
    
    if (FlowPoints.Num() == 0)
    {
        return Positions;
    }
    
    switch (static_cast<EAURACRONIslandType>(IslandType))
    {
    case EAURACRONIslandType::Nexus:
        {
            // Nexus Islands posicionadas em curvas-chave do Prismal Flow
            for (int32 i = 0; i < NEXUS_ISLAND_COUNT; ++i)
            {
                int32 Index = (i * FlowPoints.Num() / NEXUS_ISLAND_COUNT) % FlowPoints.Num();
                Positions.Add(FlowPoints[Index]);
            }
        }
        break;
        
    case EAURACRONIslandType::Sanctuary:
        {
            // Sanctuary Islands espalhadas ao longo de seções mais calmas
            for (int32 i = 0; i < SANCTUARY_ISLAND_COUNT; ++i)
            {
                int32 Index = (i * FlowPoints.Num() / SANCTUARY_ISLAND_COUNT + FlowPoints.Num() / 16) % FlowPoints.Num();
                Positions.Add(FlowPoints[Index]);
            }
        }
        break;
        
    case EAURACRONIslandType::Arsenal:
        {
            // Arsenal Islands próximas aos pontos de transição de ambiente
            for (int32 i = 0; i < ARSENAL_ISLAND_COUNT; ++i)
            {
                int32 Index = (i * FlowPoints.Num() / ARSENAL_ISLAND_COUNT + FlowPoints.Num() / 12) % FlowPoints.Num();
                Positions.Add(FlowPoints[Index]);
            }
        }
        break;
        
    case EAURACRONIslandType::Chaos:
        {
            // Chaos Islands nos pontos de interseção do Flow
            const FVector& MapCenter = MAP_CENTER;
            for (int32 i = 0; i < CHAOS_ISLAND_COUNT; ++i)
            {
                float Angle = 2.0f * PI * i / CHAOS_ISLAND_COUNT;
                float X = MapCenter.X + MAP_RADIUS_CM * 0.5f * FMath::Cos(Angle);
                float Y = MapCenter.Y + MAP_RADIUS_CM * 0.5f * FMath::Sin(Angle);
                Positions.Add(FVector(X, Y, MapCenter.Z));
            }
        }
        break;
        
    default:
        break;
    }
    
    return Positions;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE VALIDAÇÃO
// ========================================

bool UAURACRONMapMeasurements::IsPositionWithinMapBounds(const FVector& Position, const FVector& MapCenter)
{
    float Distance = FVector::Dist2D(Position, MapCenter);
    return Distance <= FAURACRONMapDimensions::MAP_RADIUS_CM;
}

float UAURACRONMapMeasurements::GetDistanceInMeters(const FVector& PointA, const FVector& PointB)
{
    float DistanceInUnrealUnits = FVector::Dist(PointA, PointB);
    return UnrealUnitsToMeters(DistanceInUnrealUnits);
}

int32 UAURACRONMapMeasurements::GetMapPhaseFromTime(float ElapsedTimeSeconds)
{
    if (ElapsedTimeSeconds < FAURACRONMapDimensions::PHASE_AWAKENING_DURATION_SECONDS)
    {
        return static_cast<int32>(EAURACRONMapPhase::Awakening);
    }
    else if (ElapsedTimeSeconds < FAURACRONMapDimensions::PHASE_AWAKENING_DURATION_SECONDS + 
             FAURACRONMapDimensions::PHASE_CONVERGENCE_DURATION_SECONDS)
    {
        return static_cast<int32>(EAURACRONMapPhase::Convergence);
    }
    else if (ElapsedTimeSeconds < FAURACRONMapDimensions::PHASE_RESOLUTION_START_SECONDS)
    {
        return static_cast<int32>(EAURACRONMapPhase::Intensification);
    }
    else
    {
        return static_cast<int32>(EAURACRONMapPhase::Resolution);
    }
}

float UAURACRONMapMeasurements::GetMapScaleFactorForPhase(int32 MapPhase)
{
    switch (static_cast<EAURACRONMapPhase>(MapPhase))
    {
    case EAURACRONMapPhase::Awakening:
    case EAURACRONMapPhase::Convergence:
    case EAURACRONMapPhase::Intensification:
        return 1.0f; // Tamanho normal

    case EAURACRONMapPhase::Resolution:
        return FAURACRONMapDimensions::MAP_CONTRACTION_FACTOR; // Contração de 20%

    default:
        return 1.0f;
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES DE LANES E JUNGLE
// Baseadas em análise detalhada de League of Legends e Dota 2
// ========================================

TArray<FVector> UAURACRONMapMeasurements::GetTopLanePoints()
{
    TArray<FVector> LanePoints;

    // Top Lane: diagonal superior esquerda para inferior direita (layout exato do LoL)
    FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::TOP_LANE_START_X,
        FAURACRONMapDimensions::TOP_LANE_START_Y,
        0.0f
    );
    FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::TOP_LANE_END_X,
        FAURACRONMapDimensions::TOP_LANE_END_Y,
        0.0f
    );

    // Gerar pontos ao longo da lane (mais pontos para precisão)
    int32 NumPoints = 25;
    for (int32 i = 0; i <= NumPoints; ++i)
    {
        float T = static_cast<float>(i) / NumPoints;
        FVector LanePoint = FMath::Lerp(StartPoint, EndPoint, T);
        LanePoints.Add(LanePoint);
    }

    return LanePoints;
}

TArray<FVector> UAURACRONMapMeasurements::GetMidLanePoints()
{
    TArray<FVector> LanePoints;

    // Mid Lane: diagonal inferior esquerda para superior direita (passando pelo centro como LoL)
    FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::MID_LANE_START_X,
        FAURACRONMapDimensions::MID_LANE_START_Y,
        0.0f
    );
    FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::MID_LANE_END_X,
        FAURACRONMapDimensions::MID_LANE_END_Y,
        0.0f
    );

    // Mid lane tem mais pontos por ser a mais longa e importante
    int32 NumPoints = 30;
    for (int32 i = 0; i <= NumPoints; ++i)
    {
        float T = static_cast<float>(i) / NumPoints;
        FVector LanePoint = FMath::Lerp(StartPoint, EndPoint, T);
        LanePoints.Add(LanePoint);
    }

    return LanePoints;
}

TArray<FVector> UAURACRONMapMeasurements::GetBotLanePoints()
{
    TArray<FVector> LanePoints;

    // Bot Lane: diagonal inferior direita para superior esquerda (espelho da Top Lane)
    FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::BOT_LANE_START_X,
        FAURACRONMapDimensions::BOT_LANE_START_Y,
        0.0f
    );
    FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::BOT_LANE_END_X,
        FAURACRONMapDimensions::BOT_LANE_END_Y,
        0.0f
    );

    // Mesmo número de pontos que Top Lane (simetria)
    int32 NumPoints = 25;
    for (int32 i = 0; i <= NumPoints; ++i)
    {
        float T = static_cast<float>(i) / NumPoints;
        FVector LanePoint = FMath::Lerp(StartPoint, EndPoint, T);
        LanePoints.Add(LanePoint);
    }

    return LanePoints;
}

TArray<FVector> UAURACRONMapMeasurements::GetJungleCampPositions()
{
    TArray<FVector> CampPositions;

    // BUFF CAMPS (equivalentes ao Blue/Red Buff do LoL)

    // Radiant Essence (Blue Buff equivalent) - Team 1 side
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::RADIANT_ESSENCE_X,
        FAURACRONMapDimensions::RADIANT_ESSENCE_Y,
        0.0f
    ));

    // Chaos Essence (Red Buff equivalent) - Team 2 side
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::CHAOS_ESSENCE_X,
        FAURACRONMapDimensions::CHAOS_ESSENCE_Y,
        0.0f
    ));

    // JUNGLE CAMPS NORMAIS (distribuição simétrica baseada no LoL)

    // Team 1 Jungle (lado inferior esquerdo)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(-3500.0f, -1500.0f, 0.0f)); // Stone Guardians (Krugs)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(-2800.0f, 800.0f, 0.0f));   // Prismal Toad (Gromp)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(-1800.0f, -3200.0f, 0.0f)); // Spectral Pack (Wolves)

    // Team 2 Jungle (lado superior direito)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(3500.0f, 1500.0f, 0.0f));  // Stone Guardians (Krugs)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(2800.0f, -800.0f, 0.0f));  // Prismal Toad (Gromp)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(1800.0f, 3200.0f, 0.0f));  // Spectral Pack (Wolves)

    // Jungle Neutro (centro do mapa - disputado)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(-1200.0f, 2500.0f, 0.0f)); // Wind Spirits (Raptors)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(1200.0f, -2500.0f, 0.0f)); // Wind Spirits (Raptors)

    // Flux Crawlers (Scuttle equivalents) - no Fluxo Prismal
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(0.0f, 3000.0f, 0.0f));  // Superior
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(0.0f, -3000.0f, 0.0f)); // Inferior

    return CampPositions;
}

TArray<FVector> UAURACRONMapMeasurements::GetTowerPositions(int32 LaneIndex)
{
    TArray<FVector> TowerPositions;

    // Obter pontos da lane correspondente
    TArray<FVector> LanePoints;
    switch (LaneIndex)
    {
    case 0: // Top Lane
        LanePoints = GetTopLanePoints();
        break;
    case 1: // Mid Lane
        LanePoints = GetMidLanePoints();
        break;
    case 2: // Bot Lane
        LanePoints = GetBotLanePoints();
        break;
    default:
        return TowerPositions; // Lane inválida
    }

    if (LanePoints.Num() < 4)
    {
        return TowerPositions;
    }

    // Posicionar torres ao longo da lane (baseado no LoL: 3 torres por lane)
    // Torre externa (25% da lane)
    int32 OuterTowerIndex = LanePoints.Num() / 4;
    TowerPositions.Add(LanePoints[OuterTowerIndex]);

    // Torre interna (50% da lane)
    int32 InnerTowerIndex = LanePoints.Num() / 2;
    TowerPositions.Add(LanePoints[InnerTowerIndex]);

    // Torre do nexus (75% da lane)
    int32 NexusTowerIndex = (LanePoints.Num() * 3) / 4;
    TowerPositions.Add(LanePoints[NexusTowerIndex]);

    return TowerPositions;
}

TArray<FVector> UAURACRONMapMeasurements::GetStrategicObjectivePositions()
{
    TArray<FVector> ObjectivePositions;

    // PRISMAL NEXUS (Baron equivalent) - objetivo principal superior
    FVector PrismalNexusPosition = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::PRISMAL_NEXUS_X,
        FAURACRONMapDimensions::PRISMAL_NEXUS_Y,
        0.0f
    );
    ObjectivePositions.Add(PrismalNexusPosition);

    // ELEMENTAL ANCHORS (Dragon equivalents) - objetivos secundários

    // Radiant Anchor (Fire/Earth element)
    FVector RadiantAnchorPosition = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::RADIANT_ANCHOR_X,
        FAURACRONMapDimensions::RADIANT_ANCHOR_Y,
        0.0f
    );
    ObjectivePositions.Add(RadiantAnchorPosition);

    // Zephyr Anchor (Air/Lightning element)
    FVector ZephyrAnchorPosition = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::ZEPHYR_ANCHOR_X,
        FAURACRONMapDimensions::ZEPHYR_ANCHOR_Y,
        0.0f
    );
    ObjectivePositions.Add(ZephyrAnchorPosition);

    // Purgatory Anchor (Shadow/Spectral element)
    FVector PurgatoryAnchorPosition = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::PURGATORY_ANCHOR_X,
        FAURACRONMapDimensions::PURGATORY_ANCHOR_Y,
        0.0f
    );
    ObjectivePositions.Add(PurgatoryAnchorPosition);

    return ObjectivePositions;
}

TArray<FVector> UAURACRONMapMeasurements::GetBasePositions()
{
    TArray<FVector> BasePositions;

    // Bases posicionadas nos cantos opostos do mapa (layout exato do LoL)

    // Team 1 Base (canto inferior esquerdo)
    FVector Team1Base = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::TEAM1_BASE_X,
        FAURACRONMapDimensions::TEAM1_BASE_Y,
        0.0f
    );
    BasePositions.Add(Team1Base);

    // Team 2 Base (canto superior direito)
    FVector Team2Base = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::TEAM2_BASE_X,
        FAURACRONMapDimensions::TEAM2_BASE_Y,
        0.0f
    );
    BasePositions.Add(Team2Base);

    return BasePositions;
}

FVector UAURACRONMapMeasurements::GetLanePosition(int32 LaneIndex, float LaneProgress)
{
    // Obter pontos da lane correspondente
    TArray<FVector> LanePoints;
    switch (LaneIndex)
    {
    case 0: // Top Lane
        LanePoints = GetTopLanePoints();
        break;
    case 1: // Mid Lane
        LanePoints = GetMidLanePoints();
        break;
    case 2: // Bot Lane
        LanePoints = GetBotLanePoints();
        break;
    default:
        return FAURACRONMapDimensions::MAP_CENTER; // Lane inválida
    }

    if (LanePoints.Num() < 2)
    {
        return FAURACRONMapDimensions::MAP_CENTER;
    }

    // Clamp progress para [0, 1]
    LaneProgress = FMath::Clamp(LaneProgress, 0.0f, 1.0f);

    // Calcular índice no array de pontos
    float FloatIndex = LaneProgress * (LanePoints.Num() - 1);
    int32 Index1 = FMath::FloorToInt(FloatIndex);
    int32 Index2 = FMath::Min(Index1 + 1, LanePoints.Num() - 1);

    // Interpolar entre os dois pontos
    float Alpha = FloatIndex - Index1;
    return FMath::Lerp(LanePoints[Index1], LanePoints[Index2], Alpha);
}

bool UAURACRONMapMeasurements::IsPositionInLane(const FVector& Position, int32 LaneIndex, float Tolerance)
{
    // Obter pontos da lane correspondente
    TArray<FVector> LanePoints;
    switch (LaneIndex)
    {
    case 0: // Top Lane
        LanePoints = GetTopLanePoints();
        break;
    case 1: // Mid Lane
        LanePoints = GetMidLanePoints();
        break;
    case 2: // Bot Lane
        LanePoints = GetBotLanePoints();
        break;
    default:
        return false; // Lane inválida
    }

    // Verificar distância mínima para qualquer ponto da lane
    float MinDistance = MAX_FLT;
    for (const FVector& LanePoint : LanePoints)
    {
        float Distance = FVector::Dist2D(Position, LanePoint);
        MinDistance = FMath::Min(MinDistance, Distance);
    }

    return MinDistance <= Tolerance;
}

int32 UAURACRONMapMeasurements::GetClosestLane(const FVector& Position)
{
    float MinDistance = MAX_FLT;
    int32 ClosestLane = 0;

    // Verificar distância para cada lane
    for (int32 LaneIndex = 0; LaneIndex < 3; ++LaneIndex)
    {
        TArray<FVector> LanePoints;
        switch (LaneIndex)
        {
        case 0: // Top Lane
            LanePoints = GetTopLanePoints();
            break;
        case 1: // Mid Lane
            LanePoints = GetMidLanePoints();
            break;
        case 2: // Bot Lane
            LanePoints = GetBotLanePoints();
            break;
        }

        // Encontrar ponto mais próximo nesta lane
        for (const FVector& LanePoint : LanePoints)
        {
            float Distance = FVector::Dist2D(Position, LanePoint);
            if (Distance < MinDistance)
            {
                MinDistance = Distance;
                ClosestLane = LaneIndex;
            }
        }
    }

    return ClosestLane;
}
