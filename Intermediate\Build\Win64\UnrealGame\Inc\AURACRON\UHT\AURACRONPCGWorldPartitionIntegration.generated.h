// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGWorldPartitionIntegration.h"

#ifdef AURACRON_AURACRONPCGWorldPartitionIntegration_generated_h
#error "AURACRONPCGWorldPartitionIntegration.generated.h already included, missing '#pragma once' in AURACRONPCGWorldPartitionIntegration.h"
#endif
#define AURACRON_AURACRONPCGWorldPartitionIntegration_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class EAURACRONStreamingQualityProfile : uint8;
struct FAURACRONPCGStreamingConfig;
struct FAURACRONStreamingPerformanceStats;

// ********** Begin ScriptStruct FAURACRONStreamingPerformanceStats ********************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_32_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FAURACRONStreamingPerformanceStats;
// ********** End ScriptStruct FAURACRONStreamingPerformanceStats **********************************

// ********** Begin ScriptStruct FAURACRONHardwareStreamingConfig **********************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_67_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FAURACRONHardwareStreamingConfig;
// ********** End ScriptStruct FAURACRONHardwareStreamingConfig ************************************

// ********** Begin ScriptStruct FAURACRONPCGStreamingConfig ***************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_112_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONPCGStreamingConfig;
// ********** End ScriptStruct FAURACRONPCGStreamingConfig *****************************************

// ********** Begin ScriptStruct FAURACRONPCGWorldPartitionStreamingConfig *************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_171_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FAURACRONPCGStreamingConfig Super;


struct FAURACRONPCGWorldPartitionStreamingConfig;
// ********** End ScriptStruct FAURACRONPCGWorldPartitionStreamingConfig ***************************

// ********** Begin ScriptStruct FAURACRONPCGStreamingEntry ****************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_217_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONPCGStreamingEntry;
// ********** End ScriptStruct FAURACRONPCGStreamingEntry ******************************************

// ********** Begin ScriptStruct FAURACRONPCGStreamingRegion ***************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_249_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONPCGStreamingRegion;
// ********** End ScriptStruct FAURACRONPCGStreamingRegion *****************************************

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration ************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_284_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetStreamingPerformanceStats); \
	DECLARE_FUNCTION(execApplyOptimizationToAllElements); \
	DECLARE_FUNCTION(execOptimizeStreamingForCurrentPlatform); \
	DECLARE_FUNCTION(execSetStreamingQualityProfile); \
	DECLARE_FUNCTION(execGetHardwareStreamingDistanceMultiplier); \
	DECLARE_FUNCTION(execConfigureHardwareOptimizations); \
	DECLARE_FUNCTION(execDetectHardwareCapabilities); \
	DECLARE_FUNCTION(execUpdateStreamingForPlayerLocation); \
	DECLARE_FUNCTION(execRegisterPCGElementForStreaming); \
	DECLARE_FUNCTION(execInitializeWorldPartitionIntegration);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_284_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGWorldPartitionIntegration(); \
	friend struct Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGWorldPartitionIntegration, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGWorldPartitionIntegration)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_284_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGWorldPartitionIntegration(AAURACRONPCGWorldPartitionIntegration&&) = delete; \
	AAURACRONPCGWorldPartitionIntegration(const AAURACRONPCGWorldPartitionIntegration&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGWorldPartitionIntegration); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGWorldPartitionIntegration); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGWorldPartitionIntegration) \
	NO_API virtual ~AAURACRONPCGWorldPartitionIntegration();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_281_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_284_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_284_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_284_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h_284_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGWorldPartitionIntegration;

// ********** End Class AAURACRONPCGWorldPartitionIntegration **************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h

// ********** Begin Enum EAURACRONStreamingQualityProfile ******************************************
#define FOREACH_ENUM_EAURACRONSTREAMINGQUALITYPROFILE(op) \
	op(EAURACRONStreamingQualityProfile::Low) \
	op(EAURACRONStreamingQualityProfile::Medium) \
	op(EAURACRONStreamingQualityProfile::High) \
	op(EAURACRONStreamingQualityProfile::Ultra) \
	op(EAURACRONStreamingQualityProfile::Custom) 

enum class EAURACRONStreamingQualityProfile : uint8;
template<> struct TIsUEnumClass<EAURACRONStreamingQualityProfile> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONStreamingQualityProfile>();
// ********** End Enum EAURACRONStreamingQualityProfile ********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
