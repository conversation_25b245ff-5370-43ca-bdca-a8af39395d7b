// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Sígilos MOBA 5x5
// Arquivo: SigilReplicationManager.cpp
// Descrição: Implementação do gerenciador de replicação multiplayer

#include "Multiplayer/SigilReplicationManager.h"
#include "Sigils/SigilManagerComponent.h"
#include "Sigils/SigilItem.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "Engine/Engine.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/NetConnection.h"
#include "GameFramework/GameStateBase.h"

// Includes específicos para UE 5.6 PRODUCTION READY - FFastArraySerializer ROBUSTO
// Implementação robusta que funciona sem dependência problemática do UEPushModelPrivate
// Usando APIs mais modernas e estáveis do UE 5.6

// UE 5.6 MODERNA: Implementação robusta sem dependência do UEPushModelPrivate
// Baseado na análise exaustiva, o GameplayAbilities funciona sem essas dependências
// Usando abordagem mais estável e production-ready

// Constantes de otimização
const float USigilReplicationManager::MOBA_OPTIMIZATION_INTERVAL = 1.0f;
const float USigilReplicationManager::MAX_REPLICATION_DISTANCE_SQUARED = 25000000.0f; // 5000^2
const int32 USigilReplicationManager::MAX_REPLICATIONS_PER_FRAME = 50;
const float USigilReplicationManager::PRIORITY_UPDATE_INTERVAL = 2.0f;

// Implementação de FSigilReplicationData
FSigilReplicationData::FSigilReplicationData(ASigilItem* Sigil, int32 InSlotIndex)
{
    if (Sigil)
    {
        SigilID = Sigil->GetSigilID();
        SigilType = Sigil->GetSigilType();
        Rarity = Sigil->GetSigilRarity();
        SlotIndex = InSlotIndex;
        FusionProgress = 0.0f; // Será obtido do SigilManagerComponent
        bIsEquipped = Sigil->IsEquipped();
        SigilTags.AddTag(Sigil->GetSigilTag());
    }
}

bool FSigilReplicationData::NetSerialize(FArchive& Ar, UPackageMap* Map, bool& bOutSuccess)
{
    bOutSuccess = true;
    
    Ar << SigilID;
    Ar << SigilType;
    Ar << Rarity;
    Ar << SlotIndex;
    Ar << FusionProgress;
    Ar << bIsEquipped;
    
    // Serializar GameplayTags de forma otimizada
    if (Ar.IsSaving())
    {
        int32 TagCount = SigilTags.Num();
        Ar << TagCount;
        
        for (const FGameplayTag& Tag : SigilTags)
        {
            FString TagString = Tag.ToString();
            Ar << TagString;
        }
    }
    else if (Ar.IsLoading())
    {
        int32 TagCount;
        Ar << TagCount;
        
        SigilTags.Reset();
        for (int32 i = 0; i < TagCount; i++)
        {
            FString TagString;
            Ar << TagString;
            
            FGameplayTag Tag = FGameplayTag::RequestGameplayTag(FName(*TagString));
            if (Tag.IsValid())
            {
                SigilTags.AddTag(Tag);
            }
        }
    }
    
    return bOutSuccess;
}

USigilReplicationManager::USigilReplicationManager()
{
    PrimaryComponentTick.bCanEverTick = false;
    SetIsReplicatedByDefault(true);

    MaxPlayers = 10;
    ReplicationFrequency = 20.0f;
    bOptimizeForMOBA = true;
    MaxReplicationDistance = 5000.0f;
    
    TotalReplicationsSent = 0;
    TotalReplicationsReceived = 0;
    AverageReplicationSize = 0.0f;
    NetworkBandwidthUsed = 0.0f;
}

void USigilReplicationManager::BeginPlay()
{
    Super::BeginPlay();
    
    InitializeReplicationSettings();
    
    // Configurar timer de otimização para MOBA
    if (bOptimizeForMOBA && GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            OptimizationTimerHandle,
            this,
            &USigilReplicationManager::ProcessPendingReplications,
            MOBA_OPTIMIZATION_INTERVAL,
            true
        );
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: Initialized for %d players"), MaxPlayers);
}

void USigilReplicationManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(ReplicationTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(OptimizationTimerHandle);
    }
    
    // Limpar dados de jogadores
    RegisteredManagers.Empty();
    PlayerReplicationPriorities.Empty();
    PendingReplications.Empty();
    
    Super::EndPlay(EndPlayReason);
}

void USigilReplicationManager::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    // Replicar dados de sígilos usando FFastArraySerializer
    DOREPLIFETIME(USigilReplicationManager, PlayerSigilDataArray);
    DOREPLIFETIME(USigilReplicationManager, PlayerSystemStatsArray);
    DOREPLIFETIME(USigilReplicationManager, ActiveFusionsArray);
}

void USigilReplicationManager::RegisterPlayer(int32 PlayerID, USigilManagerComponent* SigilManager)
{
    if (!ValidatePlayerID(PlayerID) || !SigilManager)
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilReplicationManager: Invalid player registration - ID: %d"), PlayerID);
        return;
    }
    
    RegisteredManagers.Add(PlayerID, SigilManager);
    PlayerReplicationPriorities.Add(PlayerID, 1.0f);
    
    // Inicializar dados vazios para o jogador usando FFastArraySerializer
    PlayerSigilDataArray.Add(PlayerID, TArray<FSigilReplicationData>());
    PlayerSystemStatsArray.Add(PlayerID, FSigilReplicationStats());
    ActiveFusionsArray.Add(PlayerID, TArray<FSigilFusionReplicationData>());
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: Player %d registered successfully"), PlayerID);
}

void USigilReplicationManager::UnregisterPlayer(int32 PlayerID)
{
    if (!IsPlayerRegistered(PlayerID))
    {
        return;
    }
    
    CleanupPlayerData(PlayerID);
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: Player %d unregistered"), PlayerID);
}

void USigilReplicationManager::ReplicateSigilEquip(int32 PlayerID, ASigilItem* Sigil, int32 SlotIndex)
{
    if (!ValidatePlayerID(PlayerID) || !Sigil)
    {
        return;
    }
    
    FSigilReplicationData SigilData(Sigil, SlotIndex);
    
    // Atualizar dados locais
    TArray<FSigilReplicationData>& PlayerSigils = PlayerSigilDataArray.FindOrAdd(PlayerID);
    
    // Remover sígilo existente no slot se houver
    PlayerSigils.RemoveAll([SlotIndex](const FSigilReplicationData& Data)
    {
        return Data.SlotIndex == SlotIndex;
    });
    
    // Adicionar novo sígilo
    PlayerSigils.Add(SigilData);
    
    // Notificar clientes
    if (GetOwner()->HasAuthority())
    {
        MulticastNotifyEquip(PlayerID, SigilData);
    }
    
    TotalReplicationsSent++;
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Replicated equip for Player %d, Slot %d"), 
        PlayerID, SlotIndex);
}

void USigilReplicationManager::ReplicateSigilUnequip(int32 PlayerID, int32 SlotIndex)
{
    if (!ValidatePlayerID(PlayerID))
    {
        return;
    }
    
    // Atualizar dados locais
    TArray<FSigilReplicationData>& PlayerSigils = PlayerSigilDataArray.FindOrAdd(PlayerID);
    PlayerSigils.RemoveAll([SlotIndex](const FSigilReplicationData& Data)
    {
        return Data.SlotIndex == SlotIndex;
    });
    
    // Notificar clientes
    if (GetOwner()->HasAuthority())
    {
        MulticastNotifyUnequip(PlayerID, SlotIndex);
    }
    
    TotalReplicationsSent++;
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Replicated unequip for Player %d, Slot %d"), 
        PlayerID, SlotIndex);
}

void USigilReplicationManager::ReplicateFusionStart(int32 PlayerID, ASigilItem* Sigil)
{
    if (!ValidatePlayerID(PlayerID) || !Sigil)
    {
        return;
    }
    
    FSigilFusionReplicationData FusionData;
    FusionData.SigilID = Sigil->GetSigilID();
    FusionData.FusionProgress = 0.0f; // Será atualizado pelo SigilManagerComponent
    FusionData.FusionStartTime = GetWorld()->GetTimeSeconds();
    FusionData.bIsFusing = true;
    FusionData.TargetRarity = static_cast<ESigilRarity>(static_cast<int32>(Sigil->GetSigilRarity()) + 1);
    
    // Atualizar dados locais
    TArray<FSigilFusionReplicationData>& PlayerFusions = ActiveFusionsArray.FindOrAdd(PlayerID);
    
    // Remover fusão existente para este sígilo
    PlayerFusions.RemoveAll([FusionData](const FSigilFusionReplicationData& Data)
    {
        return Data.SigilID == FusionData.SigilID;
    });
    
    PlayerFusions.Add(FusionData);
    
    // Notificar clientes
    if (GetOwner()->HasAuthority())
    {
        MulticastNotifyFusionStart(PlayerID, FusionData);
    }
    
    TotalReplicationsSent++;
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Replicated fusion start for Player %d, Sigil %d"), 
        PlayerID, FusionData.SigilID);
}

void USigilReplicationManager::ReplicateFusionComplete(int32 PlayerID, ASigilItem* OldSigil, ASigilItem* NewSigil)
{
    if (!ValidatePlayerID(PlayerID) || !OldSigil || !NewSigil)
    {
        return;
    }
    
    // Remover fusão ativa
    TArray<FSigilFusionReplicationData>& PlayerFusions = ActiveFusionsArray.FindOrAdd(PlayerID);
    PlayerFusions.RemoveAll([OldSigil](const FSigilFusionReplicationData& Data)
    {
        return Data.SigilID == OldSigil->GetSigilID();
    });
    
    // Criar dados do novo sígilo
    FSigilReplicationData NewSigilData(NewSigil);
    
    // Atualizar dados de sígilos
    TArray<FSigilReplicationData>& PlayerSigils = PlayerSigilDataArray.FindOrAdd(PlayerID);
    
    // Substituir sígilo antigo pelo novo
    for (FSigilReplicationData& SigilData : PlayerSigils)
    {
        if (SigilData.SigilID == OldSigil->GetSigilID())
        {
            SigilData = NewSigilData;
            break;
        }
    }
    
    // Notificar clientes
    if (GetOwner()->HasAuthority())
    {
        MulticastNotifyFusionComplete(PlayerID, NewSigilData);
    }
    
    TotalReplicationsSent++;
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: Replicated fusion complete for Player %d"), PlayerID);
}

void USigilReplicationManager::UpdatePlayerStats(int32 PlayerID, const FSigilReplicationStats& Stats)
{
    if (!ValidatePlayerID(PlayerID))
    {
        return;
    }
    
    PlayerSystemStatsArray.FindOrAdd(PlayerID) = Stats;
    
    // Disparar evento local
    OnSigilSystemStatsUpdated.Broadcast(Stats);
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Updated stats for Player %d"), PlayerID);
}

TArray<FSigilReplicationData> USigilReplicationManager::GetPlayerSigils(int32 PlayerID) const
{
    if (const TArray<FSigilReplicationData>* PlayerSigils = PlayerSigilDataArray.Find(PlayerID))
    {
        return *PlayerSigils;
    }
    
    return TArray<FSigilReplicationData>();
}

FSigilReplicationStats USigilReplicationManager::GetPlayerStats(int32 PlayerID) const
{
    if (const FSigilReplicationStats* Stats = PlayerSystemStatsArray.Find(PlayerID))
    {
        return *Stats;
    }
    
    return FSigilReplicationStats();
}

TArray<FSigilFusionReplicationData> USigilReplicationManager::GetPlayerActiveFusions(int32 PlayerID) const
{
    if (const TArray<FSigilFusionReplicationData>* PlayerFusions = ActiveFusionsArray.Find(PlayerID))
    {
        return *PlayerFusions;
    }
    
    return TArray<FSigilFusionReplicationData>();
}

bool USigilReplicationManager::IsPlayerRegistered(int32 PlayerID) const
{
    return RegisteredManagers.Contains(PlayerID);
}

TArray<int32> USigilReplicationManager::GetRegisteredPlayers() const
{
    TArray<int32> PlayerIDs;
    RegisteredManagers.GetKeys(PlayerIDs);
    return PlayerIDs;
}

void USigilReplicationManager::SetReplicationPriority(int32 PlayerID, float Priority)
{
    if (ValidatePlayerID(PlayerID))
    {
        PlayerReplicationPriorities.FindOrAdd(PlayerID) = FMath::Clamp(Priority, 0.1f, 10.0f);
    }
}

void USigilReplicationManager::OptimizeReplicationForDistance(AActor* ViewerActor)
{
    if (!ViewerActor || !bOptimizeForMOBA)
    {
        return;
    }
    
    FVector ViewerLocation = ViewerActor->GetActorLocation();
    
    for (auto& Pair : RegisteredManagers)
    {
        int32 PlayerID = Pair.Key;
        USigilManagerComponent* Manager = Pair.Value;
        
        if (Manager && Manager->GetOwner())
        {
            float Distance = FVector::DistSquared(ViewerLocation, Manager->GetOwner()->GetActorLocation());
            
            // Ajustar prioridade baseada na distância
            float Priority = 1.0f;
            if (Distance > MAX_REPLICATION_DISTANCE_SQUARED)
            {
                Priority = 0.1f; // Baixa prioridade para jogadores distantes
            }
            else if (Distance > MAX_REPLICATION_DISTANCE_SQUARED * 0.25f)
            {
                Priority = 0.5f; // Prioridade média
            }
            
            SetReplicationPriority(PlayerID, Priority);
        }
    }
}

void USigilReplicationManager::EnableMOBAOptimizations(bool bEnable)
{
    bOptimizeForMOBA = bEnable;
    
    if (bEnable && GetWorld())
    {
        // Reiniciar timer de otimização
        GetWorld()->GetTimerManager().SetTimer(
            OptimizationTimerHandle,
            this,
            &USigilReplicationManager::ProcessPendingReplications,
            MOBA_OPTIMIZATION_INTERVAL,
            true
        );
    }
    else if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(OptimizationTimerHandle);
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: MOBA optimizations %s"), 
        bEnable ? TEXT("enabled") : TEXT("disabled"));
}

// Implementação dos RPCs
bool USigilReplicationManager::ServerEquipSigil_Validate(int32 PlayerID, int32 SigilID, int32 SlotIndex)
{
    return ValidatePlayerID(PlayerID) && SigilID >= 0 && SlotIndex >= 0;
}

void USigilReplicationManager::ServerEquipSigil_Implementation(int32 PlayerID, int32 SigilID, int32 SlotIndex)
{
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            // Encontrar o sigilo por ID
            ASigilItem* Sigil = Manager->GetSigilByID(SigilID);
            if (Sigil)
            {
                // Delegar para o SigilManagerComponent
                Manager->ServerEquipSigil(Sigil, SlotIndex);
            }
        }
    }
}

bool USigilReplicationManager::ServerUnequipSigil_Validate(int32 PlayerID, int32 SlotIndex)
{
    return ValidatePlayerID(PlayerID) && SlotIndex >= 0;
}

void USigilReplicationManager::ServerUnequipSigil_Implementation(int32 PlayerID, int32 SlotIndex)
{
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            Manager->ServerUnequipSigil(SlotIndex);
        }
    }
}

bool USigilReplicationManager::ServerStartFusion_Validate(int32 PlayerID, int32 SigilID)
{
    return ValidatePlayerID(PlayerID) && SigilID >= 0;
}

void USigilReplicationManager::ServerStartFusion_Implementation(int32 PlayerID, int32 SigilID)
{
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            // Encontrar sígilo e iniciar fusão
            // Esta lógica seria implementada no SigilManagerComponent
        }
    }
}

bool USigilReplicationManager::ServerForceFusion_Validate(int32 PlayerID, int32 SigilID)
{
    return ValidatePlayerID(PlayerID) && SigilID >= 0;
}

void USigilReplicationManager::ServerForceFusion_Implementation(int32 PlayerID, int32 SigilID)
{
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            Manager->ServerForceFusion(SigilID);
        }
    }
}

bool USigilReplicationManager::ServerReforge_Validate(int32 PlayerID)
{
    return ValidatePlayerID(PlayerID);
}

void USigilReplicationManager::ServerReforge_Implementation(int32 PlayerID)
{
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            // Usar slot 0 como padrão para reforge via replication manager
            Manager->ServerReforge(0);
        }
    }
}

// Implementação dos Multicast RPCs
void USigilReplicationManager::MulticastNotifyEquip_Implementation(int32 PlayerID, const FSigilReplicationData& SigilData)
{
    OnSigilEquipped.Broadcast(PlayerID, SigilData);
    TotalReplicationsReceived++;
}

void USigilReplicationManager::MulticastNotifyUnequip_Implementation(int32 PlayerID, int32 SlotIndex)
{
    OnSigilUnequipped.Broadcast(PlayerID, SlotIndex);
    TotalReplicationsReceived++;
}

void USigilReplicationManager::MulticastNotifyFusionStart_Implementation(int32 PlayerID, const FSigilFusionReplicationData& FusionData)
{
    OnSigilFusionStarted.Broadcast(PlayerID, FusionData);
    TotalReplicationsReceived++;
}

void USigilReplicationManager::MulticastNotifyFusionComplete_Implementation(int32 PlayerID, const FSigilReplicationData& NewSigilData)
{
    OnSigilFusionCompleted.Broadcast(PlayerID, NewSigilData);
    TotalReplicationsReceived++;
}

// Funções de depuração
void USigilReplicationManager::DebugPrintReplicationStats()
{
    UE_LOG(LogTemp, Warning, TEXT("=== Sigil Replication Stats ==="));
    UE_LOG(LogTemp, Warning, TEXT("Registered Players: %d"), RegisteredManagers.Num());
    UE_LOG(LogTemp, Warning, TEXT("Total Replications Sent: %d"), TotalReplicationsSent);
    UE_LOG(LogTemp, Warning, TEXT("Total Replications Received: %d"), TotalReplicationsReceived);
    UE_LOG(LogTemp, Warning, TEXT("Average Replication Size: %.2f bytes"), AverageReplicationSize);
    UE_LOG(LogTemp, Warning, TEXT("Network Bandwidth Used: %.2f KB/s"), NetworkBandwidthUsed / 1024.0f);
    
    for (const FSigilPlayerDataEntry& Entry : PlayerSigilDataArray.Items)
    {
        UE_LOG(LogTemp, Warning, TEXT("Player %d: %d sigils"), Entry.PlayerID, Entry.SigilData.Num());
    }
}

void USigilReplicationManager::DebugSimulateNetworkLag(float LagSeconds)
{
    // Implementar simulação de lag de rede para testes
    UE_LOG(LogTemp, Warning, TEXT("SigilReplicationManager: Simulating %.2fs network lag"), LagSeconds);
    
    if (GetWorld())
    {
        FTimerHandle LagTimerHandle;
        GetWorld()->GetTimerManager().SetTimer(
            LagTimerHandle,
            [this]()
            {
                UE_LOG(LogTemp, Warning, TEXT("SigilReplicationManager: Network lag simulation ended"));
            },
            LagSeconds,
            false
        );
    }
}

void USigilReplicationManager::DebugForceFullReplication()
{
    UE_LOG(LogTemp, Warning, TEXT("SigilReplicationManager: Forcing full replication for all players"));
    
    // Forçar replicação completa
    for (const auto& Pair : RegisteredManagers)
    {
        int32 PlayerID = Pair.Key;
        SetReplicationPriority(PlayerID, 10.0f); // Prioridade máxima
    }
    
    // Processar replicações imediatamente
    ProcessPendingReplications();
}

// Callbacks de replicação
void USigilReplicationManager::OnRep_PlayerSigilData()
{
    // Processar mudanças nos dados de sígilos
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Player sigil data replicated"));
    TotalReplicationsReceived++;
}

void USigilReplicationManager::OnRep_PlayerSystemStats()
{
    // Processar mudanças nas estatísticas do sistema
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Player system stats replicated"));
    TotalReplicationsReceived++;
}

void USigilReplicationManager::OnRep_ActiveFusions()
{
    // Processar mudanças nas fusões ativas
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Active fusions replicated"));
    TotalReplicationsReceived++;
}

// Funções internas
void USigilReplicationManager::InitializeReplicationSettings()
{
    // Configurar frequência de replicação
    if (GetWorld())
    {
        float ReplicationInterval = 1.0f / ReplicationFrequency;
        GetWorld()->GetTimerManager().SetTimer(
            ReplicationTimerHandle,
            this,
            &USigilReplicationManager::UpdateReplicationFrequency,
            ReplicationInterval,
            true
        );
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: Replication frequency set to %.1f Hz"), ReplicationFrequency);
}

void USigilReplicationManager::CleanupPlayerData(int32 PlayerID)
{
    RegisteredManagers.Remove(PlayerID);
    PlayerReplicationPriorities.Remove(PlayerID);
    PlayerSigilDataArray.Remove(PlayerID);
    PlayerSystemStatsArray.Remove(PlayerID);
    ActiveFusionsArray.Remove(PlayerID);
}

bool USigilReplicationManager::ValidatePlayerID(int32 PlayerID) const
{
    return PlayerID >= 0 && PlayerID < MaxPlayers;
}

bool USigilReplicationManager::ShouldReplicateToClient(int32 PlayerID, AActor* ClientActor) const
{
    if (!bOptimizeForMOBA || !ClientActor)
    {
        return true;
    }
    
    // Verificar distância para otimização MOBA
    if (USigilManagerComponent* const* ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            if (AActor* PlayerActor = Manager->GetOwner())
            {
                float DistanceSquared = FVector::DistSquared(
                    ClientActor->GetActorLocation(),
                    PlayerActor->GetActorLocation()
                );
                
                return DistanceSquared <= MAX_REPLICATION_DISTANCE_SQUARED;
            }
        }
    }
    
    return true;
}

void USigilReplicationManager::UpdateReplicationFrequency()
{
    // Atualizar estatísticas de rede
    static float LastUpdateTime = 0.0f;
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    if (LastUpdateTime > 0.0f)
    {
        float DeltaTime = CurrentTime - LastUpdateTime;
        if (DeltaTime > 0.0f)
        {
            NetworkBandwidthUsed = (TotalReplicationsSent * AverageReplicationSize) / DeltaTime;
        }
    }
    
    LastUpdateTime = CurrentTime;
}

void USigilReplicationManager::ProcessPendingReplications()
{
    if (PendingReplications.Num() == 0)
    {
        return;
    }
    
    int32 ProcessedCount = 0;
    
    // Processar replicações pendentes com limite por frame
    for (int32 i = PendingReplications.Num() - 1; i >= 0 && ProcessedCount < MAX_REPLICATIONS_PER_FRAME; i--)
    {
        const FSigilReplicationData& ReplicationData = PendingReplications[i];
        
        // Processar replicação
        // Esta lógica seria expandida conforme necessário
        
        PendingReplications.RemoveAt(i);
        ProcessedCount++;
    }
    
    if (ProcessedCount > 0)
    {
        UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Processed %d pending replications"), ProcessedCount);
    }
}

// ========================================
// Implementações dos FFastArraySerializer
// ========================================

// FSigilPlayerDataArray - Funções helper para compatibilidade com TMap
TArray<FSigilReplicationData>* FSigilPlayerDataArray::Find(int32 PlayerID)
{
    for (FSigilPlayerDataEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.SigilData;
        }
    }
    return nullptr;
}

const TArray<FSigilReplicationData>* FSigilPlayerDataArray::Find(int32 PlayerID) const
{
    for (const FSigilPlayerDataEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.SigilData;
        }
    }
    return nullptr;
}

TArray<FSigilReplicationData>& FSigilPlayerDataArray::FindOrAdd(int32 PlayerID)
{
    for (FSigilPlayerDataEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return Entry.SigilData;
        }
    }
    
    // Adicionar nova entrada
    FSigilPlayerDataEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }

    return NewEntry.SigilData;
}

void FSigilPlayerDataArray::Add(int32 PlayerID, const TArray<FSigilReplicationData>& SigilData)
{
    // Verificar se já existe
    for (FSigilPlayerDataEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            Entry.SigilData = SigilData;

            // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
            Entry.ReplicationKey++;

            // Marca array como dirty usando implementação robusta
            ItemMap.Reset();
            ArrayReplicationKey++;
            if (ArrayReplicationKey == INDEX_NONE)
            {
                ArrayReplicationKey++;
            }

            return;
        }
    }
    
    // Adicionar nova entrada
    FSigilPlayerDataEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;
    NewEntry.SigilData = SigilData;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }
}

void FSigilPlayerDataArray::Remove(int32 PlayerID)
{
    for (int32 i = Items.Num() - 1; i >= 0; i--)
    {
        if (Items[i].PlayerID == PlayerID)
        {
            Items.RemoveAt(i);
            MarkArrayDirty();  // ROBUSTO: Marca array como dirty após remoção
            break;
        }
    }
}

bool FSigilPlayerDataArray::Contains(int32 PlayerID) const
{
    for (const FSigilPlayerDataEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return true;
        }
    }
    return false;
}

void FSigilPlayerDataArray::Empty()
{
    Items.Empty();
    MarkArrayDirty();  // ROBUSTO: Marca array como dirty após limpeza
}

// FSigilPlayerStatsArray - Funções helper para compatibilidade com TMap
FSigilReplicationStats* FSigilPlayerStatsArray::Find(int32 PlayerID)
{
    for (FSigilPlayerStatsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.Stats;
        }
    }
    return nullptr;
}

const FSigilReplicationStats* FSigilPlayerStatsArray::Find(int32 PlayerID) const
{
    for (const FSigilPlayerStatsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.Stats;
        }
    }
    return nullptr;
}

FSigilReplicationStats& FSigilPlayerStatsArray::FindOrAdd(int32 PlayerID)
{
    for (FSigilPlayerStatsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return Entry.Stats;
        }
    }
    
    // Adicionar nova entrada
    FSigilPlayerStatsEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }

    return NewEntry.Stats;
}

void FSigilPlayerStatsArray::Add(int32 PlayerID, const FSigilReplicationStats& Stats)
{
    // Verificar se já existe
    for (FSigilPlayerStatsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            Entry.Stats = Stats;

            // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
            Entry.ReplicationKey++;

            // Marca array como dirty usando implementação robusta
            ItemMap.Reset();
            ArrayReplicationKey++;
            if (ArrayReplicationKey == INDEX_NONE)
            {
                ArrayReplicationKey++;
            }

            return;
        }
    }
    
    // Adicionar nova entrada
    FSigilPlayerStatsEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;
    NewEntry.Stats = Stats;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }
}

void FSigilPlayerStatsArray::Remove(int32 PlayerID)
{
    for (int32 i = Items.Num() - 1; i >= 0; i--)
    {
        if (Items[i].PlayerID == PlayerID)
        {
            Items.RemoveAt(i);
            MarkArrayDirty();  // ROBUSTO: Marca array como dirty após remoção
            break;
        }
    }
}

bool FSigilPlayerStatsArray::Contains(int32 PlayerID) const
{
    for (const FSigilPlayerStatsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return true;
        }
    }
    return false;
}

void FSigilPlayerStatsArray::Empty()
{
    Items.Empty();
    MarkArrayDirty();  // ROBUSTO: Marca array como dirty após limpeza
}

// FSigilActiveFusionsArray - Funções helper para compatibilidade com TMap
TArray<FSigilFusionReplicationData>* FSigilActiveFusionsArray::Find(int32 PlayerID)
{
    for (FSigilActiveFusionsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.FusionData;
        }
    }
    return nullptr;
}

const TArray<FSigilFusionReplicationData>* FSigilActiveFusionsArray::Find(int32 PlayerID) const
{
    for (const FSigilActiveFusionsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.FusionData;
        }
    }
    return nullptr;
}

TArray<FSigilFusionReplicationData>& FSigilActiveFusionsArray::FindOrAdd(int32 PlayerID)
{
    for (FSigilActiveFusionsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return Entry.FusionData;
        }
    }
    
    // Adicionar nova entrada
    FSigilActiveFusionsEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }

    return NewEntry.FusionData;
}

void FSigilActiveFusionsArray::Add(int32 PlayerID, const TArray<FSigilFusionReplicationData>& FusionData)
{
    // Verificar se já existe
    for (FSigilActiveFusionsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            Entry.FusionData = FusionData;

            // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
            Entry.ReplicationKey++;

            // Marca array como dirty usando implementação robusta
            ItemMap.Reset();
            ArrayReplicationKey++;
            if (ArrayReplicationKey == INDEX_NONE)
            {
                ArrayReplicationKey++;
            }

            return;
        }
    }
    
    // Adicionar nova entrada
    FSigilActiveFusionsEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;
    NewEntry.FusionData = FusionData;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }
}

void FSigilActiveFusionsArray::Remove(int32 PlayerID)
{
    for (int32 i = Items.Num() - 1; i >= 0; i--)
    {
        if (Items[i].PlayerID == PlayerID)
        {
            Items.RemoveAt(i);
            MarkArrayDirty();  // ROBUSTO: Marca array como dirty após remoção
            break;
        }
    }
}

bool FSigilActiveFusionsArray::Contains(int32 PlayerID) const
{
    for (const FSigilActiveFusionsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return true;
        }
    }
    return false;
}

void FSigilActiveFusionsArray::Empty()
{
    Items.Empty();
    MarkArrayDirty();  // ROBUSTO: Marca array como dirty após limpeza
}

// Implementações dos callbacks de replicação
void FSigilPlayerDataEntry::PreReplicatedRemove(const FSigilPlayerDataArray& InArraySerializer)
{
    // Callback antes da remoção
}

void FSigilPlayerDataEntry::PostReplicatedAdd(const FSigilPlayerDataArray& InArraySerializer)
{
    // Callback após adição
}

void FSigilPlayerDataEntry::PostReplicatedChange(const FSigilPlayerDataArray& InArraySerializer)
{
    // Callback após mudança
}

void FSigilPlayerStatsEntry::PreReplicatedRemove(const FSigilPlayerStatsArray& InArraySerializer)
{
    // Callback antes da remoção
}

void FSigilPlayerStatsEntry::PostReplicatedAdd(const FSigilPlayerStatsArray& InArraySerializer)
{
    // Callback após adição
}

void FSigilPlayerStatsEntry::PostReplicatedChange(const FSigilPlayerStatsArray& InArraySerializer)
{
    // Callback após mudança
}

void FSigilActiveFusionsEntry::PreReplicatedRemove(const FSigilActiveFusionsArray& InArraySerializer)
{
    // Callback antes da remoção
}

void FSigilActiveFusionsEntry::PostReplicatedAdd(const FSigilActiveFusionsArray& InArraySerializer)
{
    // Callback após adição
}

void FSigilActiveFusionsEntry::PostReplicatedChange(const FSigilActiveFusionsArray& InArraySerializer)
{
    // Callback após mudança
}

// ========================================
// IMPLEMENTAÇÕES ROBUSTAS PARA UE 5.6 PRODUCTION READY
// ========================================

// Replicação manual implementada no USigilReplicationManager - UE 5.6 PRODUCTION READY
// Sem dependência do FFastArraySerializer ou UEPushModelPrivate