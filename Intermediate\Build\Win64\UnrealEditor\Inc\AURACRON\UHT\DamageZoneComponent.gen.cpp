// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Components/DamageZoneComponent.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeDamageZoneComponent() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UDamageZoneComponent();
AURACRON_API UClass* Z_Construct_UClass_UDamageZoneComponent_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UDamageZoneComponent Function SetDamagePerSecond *************************
struct Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics
{
	struct DamageZoneComponent_eventSetDamagePerSecond_Parms
	{
		float NewDamagePerSecond;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define a quantidade de dano aplicado por segundo aos jogadores fora da \xc3\xa1rea segura\n     * @param NewDamagePerSecond - Novo valor de dano por segundo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define a quantidade de dano aplicado por segundo aos jogadores fora da \xc3\xa1rea segura\n@param NewDamagePerSecond - Novo valor de dano por segundo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewDamagePerSecond;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::NewProp_NewDamagePerSecond = { "NewDamagePerSecond", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventSetDamagePerSecond_Parms, NewDamagePerSecond), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::NewProp_NewDamagePerSecond,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetDamagePerSecond", Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::DamageZoneComponent_eventSetDamagePerSecond_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::DamageZoneComponent_eventSetDamagePerSecond_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetDamagePerSecond)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewDamagePerSecond);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDamagePerSecond(Z_Param_NewDamagePerSecond);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetDamagePerSecond ***************************

// ********** Begin Class UDamageZoneComponent Function SetDamageScalingFactor *********************
struct Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics
{
	struct DamageZoneComponent_eventSetDamageScalingFactor_Parms
	{
		float NewScalingFactor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define o fator de escala do dano conforme o jogador se afasta da \xc3\xa1rea segura\n     * @param NewScalingFactor - Novo fator de escala (1.0 = sem escala)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define o fator de escala do dano conforme o jogador se afasta da \xc3\xa1rea segura\n@param NewScalingFactor - Novo fator de escala (1.0 = sem escala)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewScalingFactor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::NewProp_NewScalingFactor = { "NewScalingFactor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventSetDamageScalingFactor_Parms, NewScalingFactor), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::NewProp_NewScalingFactor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetDamageScalingFactor", Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::DamageZoneComponent_eventSetDamageScalingFactor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::DamageZoneComponent_eventSetDamageScalingFactor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetDamageScalingFactor)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewScalingFactor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDamageScalingFactor(Z_Param_NewScalingFactor);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetDamageScalingFactor ***********************

// ********** Begin Class UDamageZoneComponent Function SetDamageZoneActive ************************
struct Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics
{
	struct DamageZoneComponent_eventSetDamageZoneActive_Parms
	{
		bool bNewActive;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativa ou desativa o componente de zona de dano\n     * @param bNewActive - Novo estado de ativa\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativa ou desativa o componente de zona de dano\n@param bNewActive - Novo estado de ativa\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bNewActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNewActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::NewProp_bNewActive_SetBit(void* Obj)
{
	((DamageZoneComponent_eventSetDamageZoneActive_Parms*)Obj)->bNewActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::NewProp_bNewActive = { "bNewActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DamageZoneComponent_eventSetDamageZoneActive_Parms), &Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::NewProp_bNewActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::NewProp_bNewActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetDamageZoneActive", Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::DamageZoneComponent_eventSetDamageZoneActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::DamageZoneComponent_eventSetDamageZoneActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetDamageZoneActive)
{
	P_GET_UBOOL(Z_Param_bNewActive);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDamageZoneActive(Z_Param_bNewActive);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetDamageZoneActive **************************

// ********** Begin Class UDamageZoneComponent Function SetSafeRadius ******************************
struct Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics
{
	struct DamageZoneComponent_eventSetSafeRadius_Parms
	{
		float NewRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define o raio da \xc3\xa1rea segura onde jogadores n\xc3\xa3o recebem dano\n     * @param NewRadius - Novo raio da \xc3\xa1rea segura em unidades do mundo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define o raio da \xc3\xa1rea segura onde jogadores n\xc3\xa3o recebem dano\n@param NewRadius - Novo raio da \xc3\xa1rea segura em unidades do mundo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::NewProp_NewRadius = { "NewRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventSetSafeRadius_Parms, NewRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::NewProp_NewRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetSafeRadius", Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::DamageZoneComponent_eventSetSafeRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::DamageZoneComponent_eventSetSafeRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetSafeRadius)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSafeRadius(Z_Param_NewRadius);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetSafeRadius ********************************

// ********** Begin Class UDamageZoneComponent Function SetWarningRadius ***************************
struct Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics
{
	struct DamageZoneComponent_eventSetWarningRadius_Parms
	{
		float NewWarningRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define o raio da \xc3\xa1rea de aviso, onde jogadores recebem alertas visuais\n     * @param NewWarningRadius - Novo raio da \xc3\xa1rea de aviso em unidades do mundo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define o raio da \xc3\xa1rea de aviso, onde jogadores recebem alertas visuais\n@param NewWarningRadius - Novo raio da \xc3\xa1rea de aviso em unidades do mundo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewWarningRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::NewProp_NewWarningRadius = { "NewWarningRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventSetWarningRadius_Parms, NewWarningRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::NewProp_NewWarningRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetWarningRadius", Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::DamageZoneComponent_eventSetWarningRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::DamageZoneComponent_eventSetWarningRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetWarningRadius)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewWarningRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWarningRadius(Z_Param_NewWarningRadius);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetWarningRadius *****************************

// ********** Begin Class UDamageZoneComponent *****************************************************
void UDamageZoneComponent::StaticRegisterNativesUDamageZoneComponent()
{
	UClass* Class = UDamageZoneComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "SetDamagePerSecond", &UDamageZoneComponent::execSetDamagePerSecond },
		{ "SetDamageScalingFactor", &UDamageZoneComponent::execSetDamageScalingFactor },
		{ "SetDamageZoneActive", &UDamageZoneComponent::execSetDamageZoneActive },
		{ "SetSafeRadius", &UDamageZoneComponent::execSetSafeRadius },
		{ "SetWarningRadius", &UDamageZoneComponent::execSetWarningRadius },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UDamageZoneComponent;
UClass* UDamageZoneComponent::GetPrivateStaticClass()
{
	using TClass = UDamageZoneComponent;
	if (!Z_Registration_Info_UClass_UDamageZoneComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("DamageZoneComponent"),
			Z_Registration_Info_UClass_UDamageZoneComponent.InnerSingleton,
			StaticRegisterNativesUDamageZoneComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UDamageZoneComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_UDamageZoneComponent_NoRegister()
{
	return UDamageZoneComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UDamageZoneComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Componente respons\xc3\xa1vel por aplicar dano a jogadores que saem da \xc3\xa1rea segura do mapa\n * durante a fase de Resolu\xc3\xa7\xc3\xa3o quando ocorre a contra\xc3\xa7\xc3\xa3o do mapa.\n */" },
#endif
		{ "IncludePath", "Components/DamageZoneComponent.h" },
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente respons\xc3\xa1vel por aplicar dano a jogadores que saem da \xc3\xa1rea segura do mapa\ndurante a fase de Resolu\xc3\xa7\xc3\xa3o quando ocorre a contra\xc3\xa7\xc3\xa3o do mapa." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SafeRadius_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio da \xc3\xa1rea segura onde jogadores n\xc3\xa3o recebem dano */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da \xc3\xa1rea segura onde jogadores n\xc3\xa3o recebem dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningRadius_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio da \xc3\xa1rea de aviso, onde jogadores recebem alertas visuais */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da \xc3\xa1rea de aviso, onde jogadores recebem alertas visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamagePerSecond_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quantidade de dano aplicado por segundo aos jogadores fora da \xc3\xa1rea segura */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quantidade de dano aplicado por segundo aos jogadores fora da \xc3\xa1rea segura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageScalingFactor_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fator de escala do dano conforme o jogador se afasta da \xc3\xa1rea segura */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fator de escala do dano conforme o jogador se afasta da \xc3\xa1rea segura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDamageZoneActive_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Determina se a zona de dano est\xc3\xa1 ativa */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Determina se a zona de dano est\xc3\xa1 ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo acumulado desde o \xc3\xbaltimo tick de dano */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo acumulado desde o \xc3\xbaltimo tick de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageInterval_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo entre aplica\xc3\xa7\xc3\xb5""es de dano em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo entre aplica\xc3\xa7\xc3\xb5""es de dano em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneCenter_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Centro da zona de dano, normalmente o centro do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Centro da zona de dano, normalmente o centro do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SafeRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WarningRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamagePerSecond;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageScalingFactor;
	static void NewProp_bIsDamageZoneActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDamageZoneActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageInterval;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ZoneCenter;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond, "SetDamagePerSecond" }, // 617265192
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor, "SetDamageScalingFactor" }, // 4054332560
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive, "SetDamageZoneActive" }, // 2604692451
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius, "SetSafeRadius" }, // 1185038686
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius, "SetWarningRadius" }, // 4209193659
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UDamageZoneComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_SafeRadius = { "SafeRadius", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, SafeRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SafeRadius_MetaData), NewProp_SafeRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_WarningRadius = { "WarningRadius", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, WarningRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningRadius_MetaData), NewProp_WarningRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamagePerSecond = { "DamagePerSecond", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, DamagePerSecond), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamagePerSecond_MetaData), NewProp_DamagePerSecond_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamageScalingFactor = { "DamageScalingFactor", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, DamageScalingFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageScalingFactor_MetaData), NewProp_DamageScalingFactor_MetaData) };
void Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bIsDamageZoneActive_SetBit(void* Obj)
{
	((UDamageZoneComponent*)Obj)->bIsDamageZoneActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bIsDamageZoneActive = { "bIsDamageZoneActive", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UDamageZoneComponent), &Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bIsDamageZoneActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDamageZoneActive_MetaData), NewProp_bIsDamageZoneActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_AccumulatedTime = { "AccumulatedTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, AccumulatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedTime_MetaData), NewProp_AccumulatedTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamageInterval = { "DamageInterval", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, DamageInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageInterval_MetaData), NewProp_DamageInterval_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_ZoneCenter = { "ZoneCenter", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, ZoneCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneCenter_MetaData), NewProp_ZoneCenter_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UDamageZoneComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_SafeRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_WarningRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamagePerSecond,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamageScalingFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bIsDamageZoneActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_AccumulatedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamageInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_ZoneCenter,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UDamageZoneComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UDamageZoneComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UDamageZoneComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UDamageZoneComponent_Statics::ClassParams = {
	&UDamageZoneComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UDamageZoneComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UDamageZoneComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UDamageZoneComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UDamageZoneComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UDamageZoneComponent()
{
	if (!Z_Registration_Info_UClass_UDamageZoneComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UDamageZoneComponent.OuterSingleton, Z_Construct_UClass_UDamageZoneComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UDamageZoneComponent.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UDamageZoneComponent);
UDamageZoneComponent::~UDamageZoneComponent() {}
// ********** End Class UDamageZoneComponent *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UDamageZoneComponent, UDamageZoneComponent::StaticClass, TEXT("UDamageZoneComponent"), &Z_Registration_Info_UClass_UDamageZoneComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UDamageZoneComponent), 3874977403U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h__Script_AURACRON_2689074945(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
