// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "AURACRONStructs.h"
#include "PCG/AURACRONPCGWorldPartitionIntegration.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGPrismalFlow() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_APrismalFlowIsland();
AURACRON_API UClass* Z_Construct_UClass_APrismalFlowIsland_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowSegment();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UBoxComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPrismalFlowIslandType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPrismalFlowIslandType;
static UEnum* EPrismalFlowIslandType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPrismalFlowIslandType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPrismalFlowIslandType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EPrismalFlowIslandType"));
	}
	return Z_Registration_Info_UEnum_EPrismalFlowIslandType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EPrismalFlowIslandType>()
{
	return EPrismalFlowIslandType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Amplifier.Comment", "// Ilha para combates e confrontos\n" },
		{ "Amplifier.DisplayName", "Amplifier Island" },
		{ "Amplifier.Name", "EPrismalFlowIslandType::Amplifier" },
		{ "Amplifier.ToolTip", "Ilha para combates e confrontos" },
		{ "Arsenal.Comment", "// Ilha de ref\xc3\xbagio e recupera\xc3\xa7\xc3\xa3o\n" },
		{ "Arsenal.DisplayName", "Arsenal Island" },
		{ "Arsenal.Name", "EPrismalFlowIslandType::Arsenal" },
		{ "Arsenal.ToolTip", "Ilha de ref\xc3\xbagio e recupera\xc3\xa7\xc3\xa3o" },
		{ "Battlefield.Comment", "// Ilha com perigos ambientais e recompensas de alto risco\n" },
		{ "Battlefield.DisplayName", "Battlefield Island" },
		{ "Battlefield.Name", "EPrismalFlowIslandType::Battlefield" },
		{ "Battlefield.ToolTip", "Ilha com perigos ambientais e recompensas de alto risco" },
		{ "BlueprintType", "true" },
		{ "Chaos.Comment", "// Ilha com upgrades de armas e buffs tempor\xc3\xa1rios\n" },
		{ "Chaos.DisplayName", "Chaos Island" },
		{ "Chaos.Name", "EPrismalFlowIslandType::Chaos" },
		{ "Chaos.ToolTip", "Ilha com upgrades de armas e buffs tempor\xc3\xa1rios" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\xa7\xc3\xa3o para definir os tipos de ilhas no Prismal Flow\n * Alinhado com EAURACRONIslandType em AURACRONPCGSubsystem.h\n */" },
#endif
		{ "Corrupted.Comment", "// Ilha que permite teleporte\n" },
		{ "Corrupted.DisplayName", "Corrupted Island" },
		{ "Corrupted.Name", "EPrismalFlowIslandType::Corrupted" },
		{ "Corrupted.ToolTip", "Ilha que permite teleporte" },
		{ "Gateway.Comment", "// Ilha que amplifica poderes\n" },
		{ "Gateway.DisplayName", "Gateway Island" },
		{ "Gateway.Name", "EPrismalFlowIslandType::Gateway" },
		{ "Gateway.ToolTip", "Ilha que amplifica poderes" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
		{ "Nexus.Comment", "// Nenhum tipo espec\xc3\xad""fico\n" },
		{ "Nexus.DisplayName", "Nexus Island" },
		{ "Nexus.Name", "EPrismalFlowIslandType::Nexus" },
		{ "Nexus.ToolTip", "Nenhum tipo espec\xc3\xad""fico" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EPrismalFlowIslandType::None" },
		{ "Sanctuary.Comment", "// Ilha central com poderes especiais\n" },
		{ "Sanctuary.DisplayName", "Sanctuary Island" },
		{ "Sanctuary.Name", "EPrismalFlowIslandType::Sanctuary" },
		{ "Sanctuary.ToolTip", "Ilha central com poderes especiais" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\xa7\xc3\xa3o para definir os tipos de ilhas no Prismal Flow\nAlinhado com EAURACRONIslandType em AURACRONPCGSubsystem.h" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPrismalFlowIslandType::None", (int64)EPrismalFlowIslandType::None },
		{ "EPrismalFlowIslandType::Nexus", (int64)EPrismalFlowIslandType::Nexus },
		{ "EPrismalFlowIslandType::Sanctuary", (int64)EPrismalFlowIslandType::Sanctuary },
		{ "EPrismalFlowIslandType::Arsenal", (int64)EPrismalFlowIslandType::Arsenal },
		{ "EPrismalFlowIslandType::Chaos", (int64)EPrismalFlowIslandType::Chaos },
		{ "EPrismalFlowIslandType::Battlefield", (int64)EPrismalFlowIslandType::Battlefield },
		{ "EPrismalFlowIslandType::Amplifier", (int64)EPrismalFlowIslandType::Amplifier },
		{ "EPrismalFlowIslandType::Gateway", (int64)EPrismalFlowIslandType::Gateway },
		{ "EPrismalFlowIslandType::Corrupted", (int64)EPrismalFlowIslandType::Corrupted },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EPrismalFlowIslandType",
	"EPrismalFlowIslandType",
	Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType()
{
	if (!Z_Registration_Info_UEnum_EPrismalFlowIslandType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPrismalFlowIslandType.InnerSingleton, Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPrismalFlowIslandType.InnerSingleton;
}
// ********** End Enum EPrismalFlowIslandType ******************************************************

// ********** Begin Class APrismalFlowIsland Function ApplyIslandEffect ****************************
struct Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics
{
	struct PrismalFlowIsland_eventApplyIslandEffect_Parms
	{
		AActor* OverlappingActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aplica efeito ao jogador quando est\xc3\xa1 na ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica efeito ao jogador quando est\xc3\xa1 na ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappingActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::NewProp_OverlappingActor = { "OverlappingActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PrismalFlowIsland_eventApplyIslandEffect_Parms, OverlappingActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::NewProp_OverlappingActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APrismalFlowIsland, nullptr, "ApplyIslandEffect", Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::PrismalFlowIsland_eventApplyIslandEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::PrismalFlowIsland_eventApplyIslandEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APrismalFlowIsland::execApplyIslandEffect)
{
	P_GET_OBJECT(AActor,Z_Param_OverlappingActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyIslandEffect(Z_Param_OverlappingActor);
	P_NATIVE_END;
}
// ********** End Class APrismalFlowIsland Function ApplyIslandEffect ******************************

// ********** Begin Class APrismalFlowIsland Function GetFlowPosition ******************************
struct Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics
{
	struct PrismalFlowIsland_eventGetFlowPosition_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Retorna a posi\xc3\xa7\xc3\xa3o da ilha no flow (0-1)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Retorna a posi\xc3\xa7\xc3\xa3o da ilha no flow (0-1)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PrismalFlowIsland_eventGetFlowPosition_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APrismalFlowIsland, nullptr, "GetFlowPosition", Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::PrismalFlowIsland_eventGetFlowPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::PrismalFlowIsland_eventGetFlowPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APrismalFlowIsland::execGetFlowPosition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFlowPosition();
	P_NATIVE_END;
}
// ********** End Class APrismalFlowIsland Function GetFlowPosition ********************************

// ********** Begin Class APrismalFlowIsland Function GetIslandType ********************************
struct Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics
{
	struct PrismalFlowIsland_eventGetIslandType_Parms
	{
		EPrismalFlowIslandType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Retorna o tipo de ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Retorna o tipo de ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PrismalFlowIsland_eventGetIslandType_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType, METADATA_PARAMS(0, nullptr) }; // 2381565742
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APrismalFlowIsland, nullptr, "GetIslandType", Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::PropPointers), sizeof(Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::PrismalFlowIsland_eventGetIslandType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::Function_MetaDataParams), Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::PrismalFlowIsland_eventGetIslandType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APrismalFlowIsland_GetIslandType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APrismalFlowIsland_GetIslandType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APrismalFlowIsland::execGetIslandType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EPrismalFlowIslandType*)Z_Param__Result=P_THIS->GetIslandType();
	P_NATIVE_END;
}
// ********** End Class APrismalFlowIsland Function GetIslandType **********************************

// ********** Begin Class APrismalFlowIsland Function SetFlowPosition ******************************
struct Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics
{
	struct PrismalFlowIsland_eventSetFlowPosition_Parms
	{
		float InFlowPosition;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura a posi\xc3\xa7\xc3\xa3o da ilha no flow (0-1)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura a posi\xc3\xa7\xc3\xa3o da ilha no flow (0-1)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InFlowPosition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::NewProp_InFlowPosition = { "InFlowPosition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PrismalFlowIsland_eventSetFlowPosition_Parms, InFlowPosition), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::NewProp_InFlowPosition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APrismalFlowIsland, nullptr, "SetFlowPosition", Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::PrismalFlowIsland_eventSetFlowPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::PrismalFlowIsland_eventSetFlowPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APrismalFlowIsland::execSetFlowPosition)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_InFlowPosition);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFlowPosition(Z_Param_InFlowPosition);
	P_NATIVE_END;
}
// ********** End Class APrismalFlowIsland Function SetFlowPosition ********************************

// ********** Begin Class APrismalFlowIsland Function SetIslandActive ******************************
struct Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics
{
	struct PrismalFlowIsland_eventSetIslandActive_Parms
	{
		bool bActive;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Ativa/desativa a ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativa/desativa a ilha" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::NewProp_bActive_SetBit(void* Obj)
{
	((PrismalFlowIsland_eventSetIslandActive_Parms*)Obj)->bActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::NewProp_bActive = { "bActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PrismalFlowIsland_eventSetIslandActive_Parms), &Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::NewProp_bActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::NewProp_bActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APrismalFlowIsland, nullptr, "SetIslandActive", Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::PrismalFlowIsland_eventSetIslandActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::PrismalFlowIsland_eventSetIslandActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APrismalFlowIsland::execSetIslandActive)
{
	P_GET_UBOOL(Z_Param_bActive);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetIslandActive(Z_Param_bActive);
	P_NATIVE_END;
}
// ********** End Class APrismalFlowIsland Function SetIslandActive ********************************

// ********** Begin Class APrismalFlowIsland Function SetIslandType ********************************
struct Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics
{
	struct PrismalFlowIsland_eventSetIslandType_Parms
	{
		EPrismalFlowIslandType NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura o tipo de ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura o tipo de ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PrismalFlowIsland_eventSetIslandType_Parms, NewType), Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType, METADATA_PARAMS(0, nullptr) }; // 2381565742
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APrismalFlowIsland, nullptr, "SetIslandType", Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::PropPointers), sizeof(Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::PrismalFlowIsland_eventSetIslandType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::Function_MetaDataParams), Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::PrismalFlowIsland_eventSetIslandType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APrismalFlowIsland_SetIslandType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APrismalFlowIsland_SetIslandType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APrismalFlowIsland::execSetIslandType)
{
	P_GET_ENUM(EPrismalFlowIslandType,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetIslandType(EPrismalFlowIslandType(Z_Param_NewType));
	P_NATIVE_END;
}
// ********** End Class APrismalFlowIsland Function SetIslandType **********************************

// ********** Begin Class APrismalFlowIsland *******************************************************
void APrismalFlowIsland::StaticRegisterNativesAPrismalFlowIsland()
{
	UClass* Class = APrismalFlowIsland::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyIslandEffect", &APrismalFlowIsland::execApplyIslandEffect },
		{ "GetFlowPosition", &APrismalFlowIsland::execGetFlowPosition },
		{ "GetIslandType", &APrismalFlowIsland::execGetIslandType },
		{ "SetFlowPosition", &APrismalFlowIsland::execSetFlowPosition },
		{ "SetIslandActive", &APrismalFlowIsland::execSetIslandActive },
		{ "SetIslandType", &APrismalFlowIsland::execSetIslandType },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_APrismalFlowIsland;
UClass* APrismalFlowIsland::GetPrivateStaticClass()
{
	using TClass = APrismalFlowIsland;
	if (!Z_Registration_Info_UClass_APrismalFlowIsland.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PrismalFlowIsland"),
			Z_Registration_Info_UClass_APrismalFlowIsland.InnerSingleton,
			StaticRegisterNativesAPrismalFlowIsland,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_APrismalFlowIsland.InnerSingleton;
}
UClass* Z_Construct_UClass_APrismalFlowIsland_NoRegister()
{
	return APrismalFlowIsland::GetPrivateStaticClass();
}
struct Z_Construct_UClass_APrismalFlowIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe base para todas as ilhas no Prismal Flow\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGPrismalFlow.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe base para todas as ilhas no Prismal Flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandMesh_MetaData[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Malha da ilha\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Malha da ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual da ilha\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual da ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionArea_MetaData[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xc3\x81rea de intera\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x81rea de intera\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandType_MetaData[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tipo de ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowPosition_MetaData[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Posi\xc3\xa7\xc3\xa3o no flow (0-1)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o no flow (0-1)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Prismal Flow|Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estado de ativa\xc3\xa7\xc3\xa3o da ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado de ativa\xc3\xa7\xc3\xa3o da ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandMaterial_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material din\xc3\xa2mico da ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material din\xc3\xa2mico da ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractionArea;
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowPosition;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandMaterial;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_APrismalFlowIsland_ApplyIslandEffect, "ApplyIslandEffect" }, // 1529288902
		{ &Z_Construct_UFunction_APrismalFlowIsland_GetFlowPosition, "GetFlowPosition" }, // 946463800
		{ &Z_Construct_UFunction_APrismalFlowIsland_GetIslandType, "GetIslandType" }, // 2143293614
		{ &Z_Construct_UFunction_APrismalFlowIsland_SetFlowPosition, "SetFlowPosition" }, // 3933258041
		{ &Z_Construct_UFunction_APrismalFlowIsland_SetIslandActive, "SetIslandActive" }, // 968520279
		{ &Z_Construct_UFunction_APrismalFlowIsland_SetIslandType, "SetIslandType" }, // 1890683823
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<APrismalFlowIsland>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_IslandMesh = { "IslandMesh", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APrismalFlowIsland, IslandMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandMesh_MetaData), NewProp_IslandMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_IslandEffect = { "IslandEffect", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APrismalFlowIsland, IslandEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandEffect_MetaData), NewProp_IslandEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_InteractionArea = { "InteractionArea", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APrismalFlowIsland, InteractionArea), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionArea_MetaData), NewProp_InteractionArea_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APrismalFlowIsland, IslandType), Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandType_MetaData), NewProp_IslandType_MetaData) }; // 2381565742
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_FlowPosition = { "FlowPosition", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APrismalFlowIsland, FlowPosition), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowPosition_MetaData), NewProp_FlowPosition_MetaData) };
void Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((APrismalFlowIsland*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APrismalFlowIsland), &Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_IslandMaterial = { "IslandMaterial", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APrismalFlowIsland, IslandMaterial), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandMaterial_MetaData), NewProp_IslandMaterial_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_APrismalFlowIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_IslandMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_IslandEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_InteractionArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_FlowPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APrismalFlowIsland_Statics::NewProp_IslandMaterial,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APrismalFlowIsland_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_APrismalFlowIsland_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APrismalFlowIsland_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_APrismalFlowIsland_Statics::ClassParams = {
	&APrismalFlowIsland::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_APrismalFlowIsland_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_APrismalFlowIsland_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_APrismalFlowIsland_Statics::Class_MetaDataParams), Z_Construct_UClass_APrismalFlowIsland_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_APrismalFlowIsland()
{
	if (!Z_Registration_Info_UClass_APrismalFlowIsland.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_APrismalFlowIsland.OuterSingleton, Z_Construct_UClass_APrismalFlowIsland_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_APrismalFlowIsland.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(APrismalFlowIsland);
APrismalFlowIsland::~APrismalFlowIsland() {}
// ********** End Class APrismalFlowIsland *********************************************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function AddIslandAtPosition *********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics
{
	struct AURACRONPCGPrismalFlow_eventAddIslandAtPosition_Parms
	{
		float FlowPosition;
		EPrismalFlowIslandType IslandType;
		APrismalFlowIsland* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Islands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adicionar uma ilha estrat\xc3\xa9gica em uma posi\xc3\xa7\xc3\xa3o espec\xc3\xad""fica do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar uma ilha estrat\xc3\xa9gica em uma posi\xc3\xa7\xc3\xa3o espec\xc3\xad""fica do flow" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowPosition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::NewProp_FlowPosition = { "FlowPosition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventAddIslandAtPosition_Parms, FlowPosition), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventAddIslandAtPosition_Parms, IslandType), Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType, METADATA_PARAMS(0, nullptr) }; // 2381565742
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventAddIslandAtPosition_Parms, ReturnValue), Z_Construct_UClass_APrismalFlowIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::NewProp_FlowPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "AddIslandAtPosition", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::AURACRONPCGPrismalFlow_eventAddIslandAtPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::AURACRONPCGPrismalFlow_eventAddIslandAtPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execAddIslandAtPosition)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_FlowPosition);
	P_GET_ENUM(EPrismalFlowIslandType,Z_Param_IslandType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(APrismalFlowIsland**)Z_Param__Result=P_THIS->AddIslandAtPosition(Z_Param_FlowPosition,EPrismalFlowIslandType(Z_Param_IslandType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function AddIslandAtPosition ***********************

// ********** Begin Class AAURACRONPCGPrismalFlow Function ApplyPhaseVisualEffects *****************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics
{
	struct AURACRONPCGPrismalFlow_eventApplyPhaseVisualEffects_Parms
	{
		FLinearColor PhaseColor;
		float PhaseIntensity;
		FString EffectName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar efeitos visuais da fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos visuais da fase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseColor_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhaseColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseIntensity;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EffectName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::NewProp_PhaseColor = { "PhaseColor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventApplyPhaseVisualEffects_Parms, PhaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseColor_MetaData), NewProp_PhaseColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::NewProp_PhaseIntensity = { "PhaseIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventApplyPhaseVisualEffects_Parms, PhaseIntensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::NewProp_EffectName = { "EffectName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventApplyPhaseVisualEffects_Parms, EffectName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectName_MetaData), NewProp_EffectName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::NewProp_PhaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::NewProp_PhaseIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::NewProp_EffectName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "ApplyPhaseVisualEffects", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::AURACRONPCGPrismalFlow_eventApplyPhaseVisualEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::AURACRONPCGPrismalFlow_eventApplyPhaseVisualEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execApplyPhaseVisualEffects)
{
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_PhaseColor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_PhaseIntensity);
	P_GET_PROPERTY(FStrProperty,Z_Param_EffectName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyPhaseVisualEffects(Z_Param_Out_PhaseColor,Z_Param_PhaseIntensity,Z_Param_EffectName);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function ApplyPhaseVisualEffects *******************

// ********** Begin Class AAURACRONPCGPrismalFlow Function AssociateWithDataLayer ******************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics
{
	struct AURACRONPCGPrismalFlow_eventAssociateWithDataLayer_Parms
	{
		FName DataLayerName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|DataLayers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Associar o flow a uma Data Layer espec\xc3\xad""fica */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Associar o flow a uma Data Layer espec\xc3\xad""fica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_DataLayerName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::NewProp_DataLayerName = { "DataLayerName", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventAssociateWithDataLayer_Parms, DataLayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerName_MetaData), NewProp_DataLayerName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::NewProp_DataLayerName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "AssociateWithDataLayer", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::AURACRONPCGPrismalFlow_eventAssociateWithDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::AURACRONPCGPrismalFlow_eventAssociateWithDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execAssociateWithDataLayer)
{
	P_GET_PROPERTY_REF(FNameProperty,Z_Param_Out_DataLayerName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AssociateWithDataLayer(Z_Param_Out_DataLayerName);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function AssociateWithDataLayer ********************

// ********** Begin Class AAURACRONPCGPrismalFlow Function ConfigureForAwakeningPhase **************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics
{
	struct AURACRONPCGPrismalFlow_eventConfigureForAwakeningPhase_Parms
	{
		bool bIsEntryDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PrismalFlow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar para Fase 1: Despertar\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar para Fase 1: Despertar" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGPrismalFlow_eventConfigureForAwakeningPhase_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPrismalFlow_eventConfigureForAwakeningPhase_Parms), &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "ConfigureForAwakeningPhase", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::AURACRONPCGPrismalFlow_eventConfigureForAwakeningPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::AURACRONPCGPrismalFlow_eventConfigureForAwakeningPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execConfigureForAwakeningPhase)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForAwakeningPhase(Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function ConfigureForAwakeningPhase ****************

// ********** Begin Class AAURACRONPCGPrismalFlow Function ConfigureForConvergencePhase ************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics
{
	struct AURACRONPCGPrismalFlow_eventConfigureForConvergencePhase_Parms
	{
		bool bIsEntryDevice;
		bool bIsMidDevice;
		bool bIsHighDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PrismalFlow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar para Fase 2: Converg\xc3\xaancia (fortalecimento gradual)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar para Fase 2: Converg\xc3\xaancia (fortalecimento gradual)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static void NewProp_bIsMidDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMidDevice;
	static void NewProp_bIsHighDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsHighDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGPrismalFlow_eventConfigureForConvergencePhase_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPrismalFlow_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice_SetBit(void* Obj)
{
	((AURACRONPCGPrismalFlow_eventConfigureForConvergencePhase_Parms*)Obj)->bIsMidDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice = { "bIsMidDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPrismalFlow_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice_SetBit(void* Obj)
{
	((AURACRONPCGPrismalFlow_eventConfigureForConvergencePhase_Parms*)Obj)->bIsHighDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice = { "bIsHighDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPrismalFlow_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "ConfigureForConvergencePhase", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::AURACRONPCGPrismalFlow_eventConfigureForConvergencePhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::AURACRONPCGPrismalFlow_eventConfigureForConvergencePhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execConfigureForConvergencePhase)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_GET_UBOOL(Z_Param_bIsMidDevice);
	P_GET_UBOOL(Z_Param_bIsHighDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForConvergencePhase(Z_Param_bIsEntryDevice,Z_Param_bIsMidDevice,Z_Param_bIsHighDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function ConfigureForConvergencePhase **************

// ********** Begin Class AAURACRONPCGPrismalFlow Function ConfigureStreamingSettings **************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics
{
	struct AURACRONPCGPrismalFlow_eventConfigureStreamingSettings_Parms
	{
		FAURACRONPCGStreamingConfig StreamingConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es p\xc3\xba""blicas para configura\xc3\xa7\xc3\xa3o de streaming\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es p\xc3\xba""blicas para configura\xc3\xa7\xc3\xa3o de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::NewProp_StreamingConfig = { "StreamingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventConfigureStreamingSettings_Parms, StreamingConfig), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingConfig_MetaData), NewProp_StreamingConfig_MetaData) }; // 3330627406
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::NewProp_StreamingConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "ConfigureStreamingSettings", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::AURACRONPCGPrismalFlow_eventConfigureStreamingSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::AURACRONPCGPrismalFlow_eventConfigureStreamingSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execConfigureStreamingSettings)
{
	P_GET_STRUCT_REF(FAURACRONPCGStreamingConfig,Z_Param_Out_StreamingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureStreamingSettings(Z_Param_Out_StreamingConfig);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function ConfigureStreamingSettings ****************

// ********** Begin Class AAURACRONPCGPrismalFlow Function ConfigureWorldPartitionStreaming ********
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics
{
	struct AURACRONPCGPrismalFlow_eventConfigureWorldPartitionStreaming_Parms
	{
		FAURACRONPCGStreamingConfig StreamingConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar streaming para o Prismal Flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar streaming para o Prismal Flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::NewProp_StreamingConfig = { "StreamingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventConfigureWorldPartitionStreaming_Parms, StreamingConfig), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingConfig_MetaData), NewProp_StreamingConfig_MetaData) }; // 3330627406
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::NewProp_StreamingConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "ConfigureWorldPartitionStreaming", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::AURACRONPCGPrismalFlow_eventConfigureWorldPartitionStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::AURACRONPCGPrismalFlow_eventConfigureWorldPartitionStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execConfigureWorldPartitionStreaming)
{
	P_GET_STRUCT_REF(FAURACRONPCGStreamingConfig,Z_Param_Out_StreamingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureWorldPartitionStreaming(Z_Param_Out_StreamingConfig);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function ConfigureWorldPartitionStreaming **********

// ********** Begin Class AAURACRONPCGPrismalFlow Function GenerateFlowPath ************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GenerateFlowPath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar caminho do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar caminho do flow" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GenerateFlowPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GenerateFlowPath", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GenerateFlowPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GenerateFlowPath_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GenerateFlowPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GenerateFlowPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGenerateFlowPath)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateFlowPath();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GenerateFlowPath **************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GeneratePrismalFlow *********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar o Prismal Flow completo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar o Prismal Flow completo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GeneratePrismalFlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGeneratePrismalFlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GeneratePrismalFlow();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GeneratePrismalFlow ***********************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetAllIslands ***************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetAllIslands_Parms
	{
		TArray<APrismalFlowIsland*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Islands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter todas as ilhas do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todas as ilhas do flow" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APrismalFlowIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetAllIslands_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetAllIslands", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::AURACRONPCGPrismalFlow_eventGetAllIslands_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::AURACRONPCGPrismalFlow_eventGetAllIslands_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetAllIslands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<APrismalFlowIsland*>*)Z_Param__Result=P_THIS->GetAllIslands();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetAllIslands *****************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetAssociatedDataLayer ******************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetAssociatedDataLayer_Parms
	{
		FName ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|DataLayers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter a Data Layer associada */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter a Data Layer associada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetAssociatedDataLayer_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetAssociatedDataLayer", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::AURACRONPCGPrismalFlow_eventGetAssociatedDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::AURACRONPCGPrismalFlow_eventGetAssociatedDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetAssociatedDataLayer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FName*)Z_Param__Result=P_THIS->GetAssociatedDataLayer();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetAssociatedDataLayer ********************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetControllingTeam **********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetControllingTeam_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter equipe controladora atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter equipe controladora atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetControllingTeam_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetControllingTeam", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::AURACRONPCGPrismalFlow_eventGetControllingTeam_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::AURACRONPCGPrismalFlow_eventGetControllingTeam_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetControllingTeam)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetControllingTeam();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetControllingTeam ************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetCurrentMapPhase **********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetCurrentMapPhase_Parms
	{
		EAURACRONMapPhase ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter a fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter a fase atual do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetCurrentMapPhase_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetCurrentMapPhase", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::AURACRONPCGPrismalFlow_eventGetCurrentMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::AURACRONPCGPrismalFlow_eventGetCurrentMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetCurrentMapPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONMapPhase*)Z_Param__Result=P_THIS->GetCurrentMapPhase();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetCurrentMapPhase ************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetFlowColorForPhase ********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetFlowColorForPhase_Parms
	{
		EAURACRONMapPhase Phase;
		FLinearColor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter cor do flow para fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter cor do flow para fase" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Phase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Phase;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::NewProp_Phase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::NewProp_Phase = { "Phase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowColorForPhase_Parms, Phase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowColorForPhase_Parms, ReturnValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::NewProp_Phase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::NewProp_Phase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetFlowColorForPhase", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::AURACRONPCGPrismalFlow_eventGetFlowColorForPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::AURACRONPCGPrismalFlow_eventGetFlowColorForPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetFlowColorForPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_Phase);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLinearColor*)Z_Param__Result=P_THIS->GetFlowColorForPhase(EAURACRONMapPhase(Z_Param_Phase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetFlowColorForPhase **********************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetFlowColorForTeam *********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetFlowColorForTeam_Parms
	{
		FLinearColor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter cor do flow baseada na equipe controladora */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter cor do flow baseada na equipe controladora" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowColorForTeam_Parms, ReturnValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetFlowColorForTeam", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::AURACRONPCGPrismalFlow_eventGetFlowColorForTeam_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::AURACRONPCGPrismalFlow_eventGetFlowColorForTeam_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetFlowColorForTeam)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLinearColor*)Z_Param__Result=P_THIS->GetFlowColorForTeam();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetFlowColorForTeam ***********************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetFlowControlPoints ********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetFlowControlPoints_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter pontos de controle do fluxo para navega\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter pontos de controle do fluxo para navega\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowControlPoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetFlowControlPoints", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::AURACRONPCGPrismalFlow_eventGetFlowControlPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::AURACRONPCGPrismalFlow_eventGetFlowControlPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetFlowControlPoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GetFlowControlPoints();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetFlowControlPoints **********************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetFlowPositionAtT **********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetFlowPositionAtT_Parms
	{
		float T;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter posi\xc3\xa7\xc3\xa3o no flow baseada em par\xc3\xa2metro T (0-1) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter posi\xc3\xa7\xc3\xa3o no flow baseada em par\xc3\xa2metro T (0-1)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_T;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::NewProp_T = { "T", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowPositionAtT_Parms, T), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowPositionAtT_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::NewProp_T,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetFlowPositionAtT", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::AURACRONPCGPrismalFlow_eventGetFlowPositionAtT_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::AURACRONPCGPrismalFlow_eventGetFlowPositionAtT_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetFlowPositionAtT)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_T);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetFlowPositionAtT(Z_Param_T);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetFlowPositionAtT ************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetFlowWidthAtT *************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetFlowWidthAtT_Parms
	{
		float T;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter largura do flow em uma posi\xc3\xa7\xc3\xa3o espec\xc3\xad""fica */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter largura do flow em uma posi\xc3\xa7\xc3\xa3o espec\xc3\xad""fica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_T;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::NewProp_T = { "T", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowWidthAtT_Parms, T), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowWidthAtT_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::NewProp_T,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetFlowWidthAtT", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::AURACRONPCGPrismalFlow_eventGetFlowWidthAtT_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::AURACRONPCGPrismalFlow_eventGetFlowWidthAtT_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetFlowWidthAtT)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_T);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFlowWidthAtT(Z_Param_T);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetFlowWidthAtT ***************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetHardwareVolatilityMultiplier *********
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetHardwareVolatilityMultiplier_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Hardware" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetHardwareVolatilityMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetHardwareVolatilityMultiplier", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::AURACRONPCGPrismalFlow_eventGetHardwareVolatilityMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::AURACRONPCGPrismalFlow_eventGetHardwareVolatilityMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetHardwareVolatilityMultiplier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetHardwareVolatilityMultiplier();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetHardwareVolatilityMultiplier ***********

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetIslandsByType ************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetIslandsByType_Parms
	{
		EPrismalFlowIslandType IslandType;
		TArray<APrismalFlowIsland*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Islands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter ilhas de um tipo espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter ilhas de um tipo espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetIslandsByType_Parms, IslandType), Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType, METADATA_PARAMS(0, nullptr) }; // 2381565742
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APrismalFlowIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetIslandsByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetIslandsByType", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::AURACRONPCGPrismalFlow_eventGetIslandsByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::AURACRONPCGPrismalFlow_eventGetIslandsByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetIslandsByType)
{
	P_GET_ENUM(EPrismalFlowIslandType,Z_Param_IslandType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<APrismalFlowIsland*>*)Z_Param__Result=P_THIS->GetIslandsByType(EPrismalFlowIslandType(Z_Param_IslandType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetIslandsByType **************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetPCGComponent *************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetPCGComponent_Parms
	{
		UPCGComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter componente PCG para acesso externo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter componente PCG para acesso externo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetPCGComponent_Parms, ReturnValue), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetPCGComponent", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::AURACRONPCGPrismalFlow_eventGetPCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::AURACRONPCGPrismalFlow_eventGetPCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetPCGComponent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGComponent**)Z_Param__Result=P_THIS->GetPCGComponent();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetPCGComponent ***************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetStreamingConfiguration ***************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetStreamingConfiguration_Parms
	{
		FAURACRONPCGStreamingConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter a configura\xc3\xa7\xc3\xa3o de streaming atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter a configura\xc3\xa7\xc3\xa3o de streaming atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetStreamingConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(0, nullptr) }; // 3330627406
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetStreamingConfiguration", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::AURACRONPCGPrismalFlow_eventGetStreamingConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::AURACRONPCGPrismalFlow_eventGetStreamingConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetStreamingConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONPCGStreamingConfig*)Z_Param__Result=P_THIS->GetStreamingConfiguration();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetStreamingConfiguration *****************

// ********** Begin Class AAURACRONPCGPrismalFlow Function InitializePrismalFlow *******************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_InitializePrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PrismalFlow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Inicializar o sistema de fluxo prismal\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar o sistema de fluxo prismal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_InitializePrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "InitializePrismalFlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_InitializePrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_InitializePrismalFlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_InitializePrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_InitializePrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execInitializePrismalFlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePrismalFlow();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function InitializePrismalFlow *********************

// ********** Begin Class AAURACRONPCGPrismalFlow Function IsPositionInFlow ************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics
{
	struct AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms
	{
		FVector Position;
		float Tolerance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se uma posi\xc3\xa7\xc3\xa3o est\xc3\xa1 dentro do flow */" },
#endif
		{ "CPP_Default_Tolerance", "100.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se uma posi\xc3\xa7\xc3\xa3o est\xc3\xa1 dentro do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms), &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "IsPositionInFlow", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execIsPositionInFlow)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionInFlow(Z_Param_Out_Position,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function IsPositionInFlow **************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function OnFlowActivated *************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowActivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback quando flow \xc3\xa9 ativado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback quando flow \xc3\xa9 ativado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowActivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "OnFlowActivated", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowActivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowActivated_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowActivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowActivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execOnFlowActivated)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnFlowActivated();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function OnFlowActivated ***************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function OnFlowDeactivated ***********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowDeactivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback quando flow \xc3\xa9 desativado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback quando flow \xc3\xa9 desativado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowDeactivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "OnFlowDeactivated", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowDeactivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowDeactivated_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowDeactivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowDeactivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execOnFlowDeactivated)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnFlowDeactivated();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function OnFlowDeactivated *************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function RegisterStrategicIsland *****************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics
{
	struct AURACRONPCGPrismalFlow_eventRegisterStrategicIsland_Parms
	{
		APrismalFlowIsland* Island;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Islands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es para gerenciamento de ilhas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es para gerenciamento de ilhas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Island;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::NewProp_Island = { "Island", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventRegisterStrategicIsland_Parms, Island), Z_Construct_UClass_APrismalFlowIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::NewProp_Island,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "RegisterStrategicIsland", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::AURACRONPCGPrismalFlow_eventRegisterStrategicIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::AURACRONPCGPrismalFlow_eventRegisterStrategicIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execRegisterStrategicIsland)
{
	P_GET_OBJECT(APrismalFlowIsland,Z_Param_Island);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterStrategicIsland(Z_Param_Island);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function RegisterStrategicIsland *******************

// ********** Begin Class AAURACRONPCGPrismalFlow Function RemoveIsland ****************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics
{
	struct AURACRONPCGPrismalFlow_eventRemoveIsland_Parms
	{
		APrismalFlowIsland* Island;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Islands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remover uma ilha espec\xc3\xad""fica */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover uma ilha espec\xc3\xad""fica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Island;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::NewProp_Island = { "Island", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventRemoveIsland_Parms, Island), Z_Construct_UClass_APrismalFlowIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::NewProp_Island,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "RemoveIsland", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::AURACRONPCGPrismalFlow_eventRemoveIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::AURACRONPCGPrismalFlow_eventRemoveIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execRemoveIsland)
{
	P_GET_OBJECT(APrismalFlowIsland,Z_Param_Island);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveIsland(Z_Param_Island);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function RemoveIsland ******************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function SetActivityScale ************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics
{
	struct AURACRONPCGPrismalFlow_eventSetActivityScale_Parms
	{
		float NewActivityScale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir escala de atividade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir escala de atividade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewActivityScale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::NewProp_NewActivityScale = { "NewActivityScale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventSetActivityScale_Parms, NewActivityScale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::NewProp_NewActivityScale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "SetActivityScale", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::AURACRONPCGPrismalFlow_eventSetActivityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::AURACRONPCGPrismalFlow_eventSetActivityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execSetActivityScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewActivityScale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActivityScale(Z_Param_NewActivityScale);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function SetActivityScale **************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function SetControllingTeam **********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics
{
	struct AURACRONPCGPrismalFlow_eventSetControllingTeam_Parms
	{
		int32 TeamID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir equipe controladora do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir equipe controladora do flow" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventSetControllingTeam_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::NewProp_TeamID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "SetControllingTeam", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::AURACRONPCGPrismalFlow_eventSetControllingTeam_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::AURACRONPCGPrismalFlow_eventSetControllingTeam_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execSetControllingTeam)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetControllingTeam(Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function SetControllingTeam ************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function SetFlowIntensity ************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics
{
	struct AURACRONPCGPrismalFlow_eventSetFlowIntensity_Parms
	{
		float NewIntensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir intensidade global do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir intensidade global do flow" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::NewProp_NewIntensity = { "NewIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventSetFlowIntensity_Parms, NewIntensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::NewProp_NewIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "SetFlowIntensity", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::AURACRONPCGPrismalFlow_eventSetFlowIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::AURACRONPCGPrismalFlow_eventSetFlowIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execSetFlowIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewIntensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFlowIntensity(Z_Param_NewIntensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function SetFlowIntensity **************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function SetPredeterminedPattern *****************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics
{
	struct AURACRONPCGPrismalFlow_eventSetPredeterminedPattern_Parms
	{
		bool bUsePredeterminedPattern;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PrismalFlow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir padr\xc3\xa3o predeterminado do fluxo prismal\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir padr\xc3\xa3o predeterminado do fluxo prismal" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bUsePredeterminedPattern_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePredeterminedPattern;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::NewProp_bUsePredeterminedPattern_SetBit(void* Obj)
{
	((AURACRONPCGPrismalFlow_eventSetPredeterminedPattern_Parms*)Obj)->bUsePredeterminedPattern = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::NewProp_bUsePredeterminedPattern = { "bUsePredeterminedPattern", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPrismalFlow_eventSetPredeterminedPattern_Parms), &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::NewProp_bUsePredeterminedPattern_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::NewProp_bUsePredeterminedPattern,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "SetPredeterminedPattern", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::AURACRONPCGPrismalFlow_eventSetPredeterminedPattern_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::AURACRONPCGPrismalFlow_eventSetPredeterminedPattern_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execSetPredeterminedPattern)
{
	P_GET_UBOOL(Z_Param_bUsePredeterminedPattern);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPredeterminedPattern(Z_Param_bUsePredeterminedPattern);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function SetPredeterminedPattern *******************

// ********** Begin Class AAURACRONPCGPrismalFlow Function SetStreamingEnabled *********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics
{
	struct AURACRONPCGPrismalFlow_eventSetStreamingEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Streaming" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AURACRONPCGPrismalFlow_eventSetStreamingEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPrismalFlow_eventSetStreamingEnabled_Parms), &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "SetStreamingEnabled", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::AURACRONPCGPrismalFlow_eventSetStreamingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::AURACRONPCGPrismalFlow_eventSetStreamingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execSetStreamingEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetStreamingEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function SetStreamingEnabled ***********************

// ********** Begin Class AAURACRONPCGPrismalFlow Function SetVisualEffectsEnabled *****************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics
{
	struct AURACRONPCGPrismalFlow_eventSetVisualEffectsEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es para efeitos visuais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es para efeitos visuais" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AURACRONPCGPrismalFlow_eventSetVisualEffectsEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPrismalFlow_eventSetVisualEffectsEnabled_Parms), &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "SetVisualEffectsEnabled", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::AURACRONPCGPrismalFlow_eventSetVisualEffectsEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::AURACRONPCGPrismalFlow_eventSetVisualEffectsEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execSetVisualEffectsEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetVisualEffectsEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function SetVisualEffectsEnabled *******************

// ********** Begin Class AAURACRONPCGPrismalFlow Function UnregisterStrategicIsland ***************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics
{
	struct AURACRONPCGPrismalFlow_eventUnregisterStrategicIsland_Parms
	{
		APrismalFlowIsland* Island;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Islands" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Island;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::NewProp_Island = { "Island", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventUnregisterStrategicIsland_Parms, Island), Z_Construct_UClass_APrismalFlowIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::NewProp_Island,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "UnregisterStrategicIsland", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::AURACRONPCGPrismalFlow_eventUnregisterStrategicIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::AURACRONPCGPrismalFlow_eventUnregisterStrategicIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execUnregisterStrategicIsland)
{
	P_GET_OBJECT(APrismalFlowIsland,Z_Param_Island);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterStrategicIsland(Z_Param_Island);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function UnregisterStrategicIsland *****************

// ********** Begin Class AAURACRONPCGPrismalFlow Function UpdateFlowPhase *************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics
{
	struct AURACRONPCGPrismalFlow_eventUpdateFlowPhase_Parms
	{
		EAURACRONMapPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar flow para nova fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar flow para nova fase" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventUpdateFlowPhase_Parms, NewPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "UpdateFlowPhase", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::AURACRONPCGPrismalFlow_eventUpdateFlowPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::AURACRONPCGPrismalFlow_eventUpdateFlowPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execUpdateFlowPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_NewPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFlowPhase(EAURACRONMapPhase(Z_Param_NewPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function UpdateFlowPhase ***************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function UpdateForMapPhase ***********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics
{
	struct AURACRONPCGPrismalFlow_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar o flow para uma fase espec\xc3\xad""fica do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar o flow para uma fase espec\xc3\xad""fica do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::AURACRONPCGPrismalFlow_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::AURACRONPCGPrismalFlow_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function UpdateForMapPhase *************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function UpdateVisualEffectsForPhase *************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics
{
	struct AURACRONPCGPrismalFlow_eventUpdateVisualEffectsForPhase_Parms
	{
		EAURACRONMapPhase Phase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Visual Effects" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Phase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Phase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::NewProp_Phase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::NewProp_Phase = { "Phase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventUpdateVisualEffectsForPhase_Parms, Phase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::NewProp_Phase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::NewProp_Phase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "UpdateVisualEffectsForPhase", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::AURACRONPCGPrismalFlow_eventUpdateVisualEffectsForPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::AURACRONPCGPrismalFlow_eventUpdateVisualEffectsForPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execUpdateVisualEffectsForPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_Phase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateVisualEffectsForPhase(EAURACRONMapPhase(Z_Param_Phase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function UpdateVisualEffectsForPhase ***************

// ********** Begin Class AAURACRONPCGPrismalFlow Function UpdateVolatilityForHardware *************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVolatilityForHardware_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Hardware" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es para volatilidade adaptada ao hardware\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es para volatilidade adaptada ao hardware" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVolatilityForHardware_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "UpdateVolatilityForHardware", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVolatilityForHardware_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVolatilityForHardware_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVolatilityForHardware()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVolatilityForHardware_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execUpdateVolatilityForHardware)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateVolatilityForHardware();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function UpdateVolatilityForHardware ***************

// ********** Begin Class AAURACRONPCGPrismalFlow **************************************************
void AAURACRONPCGPrismalFlow::StaticRegisterNativesAAURACRONPCGPrismalFlow()
{
	UClass* Class = AAURACRONPCGPrismalFlow::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddIslandAtPosition", &AAURACRONPCGPrismalFlow::execAddIslandAtPosition },
		{ "ApplyPhaseVisualEffects", &AAURACRONPCGPrismalFlow::execApplyPhaseVisualEffects },
		{ "AssociateWithDataLayer", &AAURACRONPCGPrismalFlow::execAssociateWithDataLayer },
		{ "ConfigureForAwakeningPhase", &AAURACRONPCGPrismalFlow::execConfigureForAwakeningPhase },
		{ "ConfigureForConvergencePhase", &AAURACRONPCGPrismalFlow::execConfigureForConvergencePhase },
		{ "ConfigureStreamingSettings", &AAURACRONPCGPrismalFlow::execConfigureStreamingSettings },
		{ "ConfigureWorldPartitionStreaming", &AAURACRONPCGPrismalFlow::execConfigureWorldPartitionStreaming },
		{ "GenerateFlowPath", &AAURACRONPCGPrismalFlow::execGenerateFlowPath },
		{ "GeneratePrismalFlow", &AAURACRONPCGPrismalFlow::execGeneratePrismalFlow },
		{ "GetAllIslands", &AAURACRONPCGPrismalFlow::execGetAllIslands },
		{ "GetAssociatedDataLayer", &AAURACRONPCGPrismalFlow::execGetAssociatedDataLayer },
		{ "GetControllingTeam", &AAURACRONPCGPrismalFlow::execGetControllingTeam },
		{ "GetCurrentMapPhase", &AAURACRONPCGPrismalFlow::execGetCurrentMapPhase },
		{ "GetFlowColorForPhase", &AAURACRONPCGPrismalFlow::execGetFlowColorForPhase },
		{ "GetFlowColorForTeam", &AAURACRONPCGPrismalFlow::execGetFlowColorForTeam },
		{ "GetFlowControlPoints", &AAURACRONPCGPrismalFlow::execGetFlowControlPoints },
		{ "GetFlowPositionAtT", &AAURACRONPCGPrismalFlow::execGetFlowPositionAtT },
		{ "GetFlowWidthAtT", &AAURACRONPCGPrismalFlow::execGetFlowWidthAtT },
		{ "GetHardwareVolatilityMultiplier", &AAURACRONPCGPrismalFlow::execGetHardwareVolatilityMultiplier },
		{ "GetIslandsByType", &AAURACRONPCGPrismalFlow::execGetIslandsByType },
		{ "GetPCGComponent", &AAURACRONPCGPrismalFlow::execGetPCGComponent },
		{ "GetStreamingConfiguration", &AAURACRONPCGPrismalFlow::execGetStreamingConfiguration },
		{ "InitializePrismalFlow", &AAURACRONPCGPrismalFlow::execInitializePrismalFlow },
		{ "IsPositionInFlow", &AAURACRONPCGPrismalFlow::execIsPositionInFlow },
		{ "OnFlowActivated", &AAURACRONPCGPrismalFlow::execOnFlowActivated },
		{ "OnFlowDeactivated", &AAURACRONPCGPrismalFlow::execOnFlowDeactivated },
		{ "RegisterStrategicIsland", &AAURACRONPCGPrismalFlow::execRegisterStrategicIsland },
		{ "RemoveIsland", &AAURACRONPCGPrismalFlow::execRemoveIsland },
		{ "SetActivityScale", &AAURACRONPCGPrismalFlow::execSetActivityScale },
		{ "SetControllingTeam", &AAURACRONPCGPrismalFlow::execSetControllingTeam },
		{ "SetFlowIntensity", &AAURACRONPCGPrismalFlow::execSetFlowIntensity },
		{ "SetPredeterminedPattern", &AAURACRONPCGPrismalFlow::execSetPredeterminedPattern },
		{ "SetStreamingEnabled", &AAURACRONPCGPrismalFlow::execSetStreamingEnabled },
		{ "SetVisualEffectsEnabled", &AAURACRONPCGPrismalFlow::execSetVisualEffectsEnabled },
		{ "UnregisterStrategicIsland", &AAURACRONPCGPrismalFlow::execUnregisterStrategicIsland },
		{ "UpdateFlowPhase", &AAURACRONPCGPrismalFlow::execUpdateFlowPhase },
		{ "UpdateForMapPhase", &AAURACRONPCGPrismalFlow::execUpdateForMapPhase },
		{ "UpdateVisualEffectsForPhase", &AAURACRONPCGPrismalFlow::execUpdateVisualEffectsForPhase },
		{ "UpdateVolatilityForHardware", &AAURACRONPCGPrismalFlow::execUpdateVolatilityForHardware },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow;
UClass* AAURACRONPCGPrismalFlow::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGPrismalFlow;
	if (!Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGPrismalFlow"),
			Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGPrismalFlow,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister()
{
	return AAURACRONPCGPrismalFlow::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ator para gerenciar o Prismal Flow serpentino que conecta os tr\xc3\xaas ambientes\n * O flow tem largura vari\xc3\xa1vel, curvas matem\xc3\xa1ticas precisas e ilhas estrat\xc3\xa9gicas\n * Integra com World Partition e Data Layers para streaming eficiente\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGPrismalFlow.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator para gerenciar o Prismal Flow serpentino que conecta os tr\xc3\xaas ambientes\nO flow tem largura vari\xc3\xa1vel, curvas matem\xc3\xa1ticas precisas e ilhas estrat\xc3\xa9gicas\nIntegra com World Partition e Data Layers para streaming eficiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "Category", "Prismal Flow|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente PCG principal */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente PCG principal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpline_MetaData[] = {
		{ "Category", "Prismal Flow|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Spline que define o caminho do flow */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline que define o caminho do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MainFlowEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de efeitos visuais principais */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de efeitos visuais principais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowMesh_MetaData[] = {
		{ "Category", "Prismal Flow|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mesh do flow (alias para compatibilidade) */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh do flow (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito do flow (alias para compatibilidade) */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito do flow (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowCollision_MetaData[] = {
		{ "Category", "Prismal Flow|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de colis\xc3\xa3o para o flow */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de colis\xc3\xa3o para o flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalFlowIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Settings" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade global do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade global do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseFlowSpeed_MetaData[] = {
		{ "Category", "Prismal Flow|Settings" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade base do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade base do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControllingTeam_MetaData[] = {
		{ "Category", "Prismal Flow|Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Equipe controladora atual do flow (0 = neutro, 1 = Equipe A, 2 = Equipe B) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipe controladora atual do flow (0 = neutro, 1 = Equipe A, 2 = Equipe B)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumControlPoints_MetaData[] = {
		{ "Category", "Prismal Flow|Settings" },
		{ "ClampMax", "50" },
		{ "ClampMin", "10" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de pontos de controle para a curva serpentina */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de pontos de controle para a curva serpentina" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SerpentineAmplitude_MetaData[] = {
		{ "Category", "Prismal Flow|Settings" },
		{ "ClampMax", "3000.0" },
		{ "ClampMin", "500.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Amplitude das curvas serpentinas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Amplitude das curvas serpentinas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SerpentineFrequency_MetaData[] = {
		{ "Category", "Prismal Flow|Settings" },
		{ "ClampMax", "6.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frequ\xc3\xaancia das curvas serpentinas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\xaancia das curvas serpentinas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityScale_MetaData[] = {
		{ "Category", "Prismal Flow|Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala de atividade do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de atividade do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Islands_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ilhas estrat\xc3\xa9gicas no flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ilhas estrat\xc3\xa9gicas no flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxIslandCount_MetaData[] = {
		{ "Category", "Prismal Flow|Islands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es para gera\xc3\xa7\xc3\xa3o de ilhas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es para gera\xc3\xa7\xc3\xa3o de ilhas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinIslandSpacing_MetaData[] = {
		{ "Category", "Prismal Flow|Islands" },
		{ "ClampMax", "0.5" },
		{ "ClampMin", "0.05" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia m\xc3\xadnima entre ilhas (em unidades de flow T 0-1) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia m\xc3\xadnima entre ilhas (em unidades de flow T 0-1)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandClasses_MetaData[] = {
		{ "Category", "Prismal Flow|Islands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Classes de ilhas para cada tipo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classes de ilhas para cada tipo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingConfiguration_MetaData[] = {
		{ "Category", "Prismal Flow|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o de streaming para World Partition */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de streaming para World Partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssociatedDataLayer_MetaData[] = {
		{ "Category", "Prismal Flow|DataLayers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome da Data Layer associada */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome da Data Layer associada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSegments_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Segmentos do flow gerados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Segmentos do flow gerados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes gerados dinamicamente */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes gerados dinamicamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo acumulado para anima\xc3\xa7\xc3\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo acumulado para anima\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingEntries_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Entradas de streaming para World Partition */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entradas de streaming para World Partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowMaterialInstance_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material din\xc3\xa2mico para o flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material din\xc3\xa2mico para o flow" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowSpline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MainFlowEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowCollision;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalFlowIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseFlowSpeed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ControllingTeam;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumControlPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SerpentineAmplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SerpentineFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityScale;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Islands_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Islands;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxIslandCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinIslandSpacing;
	static const UECodeGen_Private::FClassPropertyParams NewProp_IslandClasses_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandClasses_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandClasses_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_IslandClasses;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingConfiguration;
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssociatedDataLayer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowSegments_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FlowSegments;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GeneratedComponents;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingEntries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_StreamingEntries;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowMaterialInstance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AddIslandAtPosition, "AddIslandAtPosition" }, // 143342475
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ApplyPhaseVisualEffects, "ApplyPhaseVisualEffects" }, // 3401867664
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_AssociateWithDataLayer, "AssociateWithDataLayer" }, // 4033855766
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForAwakeningPhase, "ConfigureForAwakeningPhase" }, // 3232254496
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureForConvergencePhase, "ConfigureForConvergencePhase" }, // 434024714
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureStreamingSettings, "ConfigureStreamingSettings" }, // 3222316633
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_ConfigureWorldPartitionStreaming, "ConfigureWorldPartitionStreaming" }, // 2220462667
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GenerateFlowPath, "GenerateFlowPath" }, // 4139756046
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow, "GeneratePrismalFlow" }, // 2267577079
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAllIslands, "GetAllIslands" }, // 761838223
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetAssociatedDataLayer, "GetAssociatedDataLayer" }, // 3934438250
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetControllingTeam, "GetControllingTeam" }, // 90007448
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetCurrentMapPhase, "GetCurrentMapPhase" }, // 127132003
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForPhase, "GetFlowColorForPhase" }, // 3217307334
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowColorForTeam, "GetFlowColorForTeam" }, // 3063937895
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowControlPoints, "GetFlowControlPoints" }, // 1208195681
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT, "GetFlowPositionAtT" }, // 1000829814
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT, "GetFlowWidthAtT" }, // 346491451
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetHardwareVolatilityMultiplier, "GetHardwareVolatilityMultiplier" }, // 4121537066
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetIslandsByType, "GetIslandsByType" }, // 178408126
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent, "GetPCGComponent" }, // 1851100007
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetStreamingConfiguration, "GetStreamingConfiguration" }, // 3517371692
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_InitializePrismalFlow, "InitializePrismalFlow" }, // 2563124801
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow, "IsPositionInFlow" }, // 3560795598
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowActivated, "OnFlowActivated" }, // 3806611180
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_OnFlowDeactivated, "OnFlowDeactivated" }, // 2157482631
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RegisterStrategicIsland, "RegisterStrategicIsland" }, // 1997458120
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_RemoveIsland, "RemoveIsland" }, // 185275576
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale, "SetActivityScale" }, // 401917080
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetControllingTeam, "SetControllingTeam" }, // 2941601322
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity, "SetFlowIntensity" }, // 2683948079
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetPredeterminedPattern, "SetPredeterminedPattern" }, // 1499476408
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetStreamingEnabled, "SetStreamingEnabled" }, // 1529443773
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetVisualEffectsEnabled, "SetVisualEffectsEnabled" }, // 1717047535
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UnregisterStrategicIsland, "UnregisterStrategicIsland" }, // 3602935434
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateFlowPhase, "UpdateFlowPhase" }, // 3138999892
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase, "UpdateForMapPhase" }, // 3984731786
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVisualEffectsForPhase, "UpdateVisualEffectsForPhase" }, // 3364161828
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateVolatilityForHardware, "UpdateVolatilityForHardware" }, // 943715049
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGPrismalFlow>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSpline = { "FlowSpline", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, FlowSpline), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpline_MetaData), NewProp_FlowSpline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_MainFlowEffect = { "MainFlowEffect", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, MainFlowEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MainFlowEffect_MetaData), NewProp_MainFlowEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowMesh = { "FlowMesh", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, FlowMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowMesh_MetaData), NewProp_FlowMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowEffect = { "FlowEffect", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, FlowEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowEffect_MetaData), NewProp_FlowEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowCollision = { "FlowCollision", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, FlowCollision), Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowCollision_MetaData), NewProp_FlowCollision_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GlobalFlowIntensity = { "GlobalFlowIntensity", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, GlobalFlowIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalFlowIntensity_MetaData), NewProp_GlobalFlowIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_BaseFlowSpeed = { "BaseFlowSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, BaseFlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseFlowSpeed_MetaData), NewProp_BaseFlowSpeed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_ControllingTeam = { "ControllingTeam", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, ControllingTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControllingTeam_MetaData), NewProp_ControllingTeam_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_NumControlPoints = { "NumControlPoints", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, NumControlPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumControlPoints_MetaData), NewProp_NumControlPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_SerpentineAmplitude = { "SerpentineAmplitude", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, SerpentineAmplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SerpentineAmplitude_MetaData), NewProp_SerpentineAmplitude_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_SerpentineFrequency = { "SerpentineFrequency", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, SerpentineFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SerpentineFrequency_MetaData), NewProp_SerpentineFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_ActivityScale = { "ActivityScale", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, ActivityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityScale_MetaData), NewProp_ActivityScale_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_Islands_Inner = { "Islands", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APrismalFlowIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_Islands = { "Islands", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, Islands), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Islands_MetaData), NewProp_Islands_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_MaxIslandCount = { "MaxIslandCount", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, MaxIslandCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxIslandCount_MetaData), NewProp_MaxIslandCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_MinIslandSpacing = { "MinIslandSpacing", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, MinIslandSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinIslandSpacing_MetaData), NewProp_MinIslandSpacing_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_IslandClasses_ValueProp = { "IslandClasses", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UClass, Z_Construct_UClass_APrismalFlowIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_IslandClasses_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_IslandClasses_Key_KeyProp = { "IslandClasses_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EPrismalFlowIslandType, METADATA_PARAMS(0, nullptr) }; // 2381565742
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_IslandClasses = { "IslandClasses", nullptr, (EPropertyFlags)0x0024080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, IslandClasses), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandClasses_MetaData), NewProp_IslandClasses_MetaData) }; // 2381565742
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_StreamingConfiguration = { "StreamingConfiguration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, StreamingConfiguration), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingConfiguration_MetaData), NewProp_StreamingConfiguration_MetaData) }; // 3330627406
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_AssociatedDataLayer = { "AssociatedDataLayer", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, AssociatedDataLayer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssociatedDataLayer_MetaData), NewProp_AssociatedDataLayer_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSegments_Inner = { "FlowSegments", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPrismalFlowSegment, METADATA_PARAMS(0, nullptr) }; // 757446967
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSegments = { "FlowSegments", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, FlowSegments), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSegments_MetaData), NewProp_FlowSegments_MetaData) }; // 757446967
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GeneratedComponents_Inner = { "GeneratedComponents", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UActorComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GeneratedComponents = { "GeneratedComponents", nullptr, (EPropertyFlags)0x0040008000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, GeneratedComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedComponents_MetaData), NewProp_GeneratedComponents_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_AccumulatedTime = { "AccumulatedTime", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, AccumulatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedTime_MetaData), NewProp_AccumulatedTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_StreamingEntries_Inner = { "StreamingEntries", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry, METADATA_PARAMS(0, nullptr) }; // 2197663406
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_StreamingEntries = { "StreamingEntries", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, StreamingEntries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingEntries_MetaData), NewProp_StreamingEntries_MetaData) }; // 2197663406
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowMaterialInstance = { "FlowMaterialInstance", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, FlowMaterialInstance), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowMaterialInstance_MetaData), NewProp_FlowMaterialInstance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_MainFlowEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GlobalFlowIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_BaseFlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_ControllingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_NumControlPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_SerpentineAmplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_SerpentineFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_ActivityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_Islands_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_Islands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_MaxIslandCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_MinIslandSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_IslandClasses_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_IslandClasses_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_IslandClasses_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_IslandClasses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_StreamingConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_AssociatedDataLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSegments_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSegments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GeneratedComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GeneratedComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_AccumulatedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_StreamingEntries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_StreamingEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowMaterialInstance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::ClassParams = {
	&AAURACRONPCGPrismalFlow::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.OuterSingleton, Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AAURACRONPCGPrismalFlow::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_ControllingTeam(TEXT("ControllingTeam"));
	const bool bIsValid = true
		&& Name_ControllingTeam == ClassReps[(int32)ENetFields_Private::ControllingTeam].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AAURACRONPCGPrismalFlow"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGPrismalFlow);
AAURACRONPCGPrismalFlow::~AAURACRONPCGPrismalFlow() {}
// ********** End Class AAURACRONPCGPrismalFlow ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPrismalFlowIslandType_StaticEnum, TEXT("EPrismalFlowIslandType"), &Z_Registration_Info_UEnum_EPrismalFlowIslandType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2381565742U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_APrismalFlowIsland, APrismalFlowIsland::StaticClass, TEXT("APrismalFlowIsland"), &Z_Registration_Info_UClass_APrismalFlowIsland, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(APrismalFlowIsland), 2811913223U) },
		{ Z_Construct_UClass_AAURACRONPCGPrismalFlow, AAURACRONPCGPrismalFlow::StaticClass, TEXT("AAURACRONPCGPrismalFlow"), &Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGPrismalFlow), 1991476392U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_4121661647(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
