// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGUtility.h"

#ifdef AURACRON_AURACRONPCGUtility_generated_h
#error "AURACRONPCGUtility.generated.h already included, missing '#pragma once' in AURACRONPCGUtility.h"
#endif
#define AURACRON_AURACRONPCGUtility_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UObject;
class UWorld;
struct FAURACRONPCGActorReferences;
struct FAURACRONPCGSearchOptions;

// ********** Begin ScriptStruct FAURACRONPCGActorReferences ***************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h_26_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONPCGActorReferences;
// ********** End ScriptStruct FAURACRONPCGActorReferences *****************************************

// ********** Begin ScriptStruct FAURACRONPCGSearchOptions *****************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h_79_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONPCGSearchOptions;
// ********** End ScriptStruct FAURACRONPCGSearchOptions *******************************************

// ********** Begin Class UAURACRONPCGUtility ******************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h_151_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execRegisterPCGActorChangeCallback); \
	DECLARE_FUNCTION(execApplyConfigurationToAllActors); \
	DECLARE_FUNCTION(execGetPCGActorStatistics); \
	DECLARE_FUNCTION(execIsValidPCGActor); \
	DECLARE_FUNCTION(execFindPCGActorsOfClass); \
	DECLARE_FUNCTION(execFindAllPCGActors); \
	DECLARE_FUNCTION(execFindPCGActors);


AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGUtility_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h_151_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAURACRONPCGUtility(); \
	friend struct Z_Construct_UClass_UAURACRONPCGUtility_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGUtility_NoRegister(); \
public: \
	DECLARE_CLASS2(UAURACRONPCGUtility, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UAURACRONPCGUtility_NoRegister) \
	DECLARE_SERIALIZER(UAURACRONPCGUtility)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h_151_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAURACRONPCGUtility(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAURACRONPCGUtility(UAURACRONPCGUtility&&) = delete; \
	UAURACRONPCGUtility(const UAURACRONPCGUtility&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAURACRONPCGUtility); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAURACRONPCGUtility); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAURACRONPCGUtility) \
	NO_API virtual ~UAURACRONPCGUtility();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h_148_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h_151_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h_151_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h_151_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h_151_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAURACRONPCGUtility;

// ********** End Class UAURACRONPCGUtility ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
