// AURACRONPCGSpectralGuardian.cpp
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Implementação do Guardião Espectral no Reino Purgatório

#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGSettings.h"
#include "PCGVolume.h"
#include "Engine/World.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraFunctionLibrary.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Components/PrimitiveComponent.h"

void AAURACRONPCGEnvironment::GenerateSpectralGuardian()
{
    // Limpar dados existentes
    if (SpectralGuardianData.GuardianActor)
    {
        if (SpectralGuardianData.AuraComponent)
        {
            SpectralGuardianData.AuraComponent->DestroyComponent();
            SpectralGuardianData.AuraComponent = nullptr;
        }
        
        if (IsValid(SpectralGuardianData.GuardianActor))
        {
            SpectralGuardianData.GuardianActor->Destroy();
            SpectralGuardianData.GuardianActor = nullptr;
        }
    }
    
    // Verificar se o componente PCG é válido
    if (!PCGComponent || !GetWorld())
    {
        UE_LOG(LogTemp, Warning, TEXT("PCGComponent ou World inválido ao gerar Guardião Espectral"));
        return;
    }
    
    // Verificar se estamos no Reino Purgatório
    if (EnvironmentType != EAURACRONEnvironmentType::PurgatoryRealm)
    {
        return; // O Guardião Espectral só existe no Reino Purgatório
    }
    
    // Calcular raios do mapa
    float OuterRadius = FAURACRONMapDimensions::MAP_OUTER_RADIUS_CM;
    float InnerRadius = FAURACRONMapDimensions::MAP_INNER_RADIUS_CM;
    float MiddleRadius = (OuterRadius + InnerRadius) * 0.5f;
    
    // Determinar posição do Guardião Espectral
    // Posicionado em uma área específica do Reino Purgatório, entre os Nexos Sombrios e as Torres de Lamentação
    float GuardianRadius = MiddleRadius * 0.7f; // Mais próximo do centro que as Torres de Lamentação
    float GuardianAngle = FMath::FRand() * 2.0f * PI; // Ângulo aleatório
    
    // Adicionar variação à posição
    float RadiusVariation = FMath::FRandRange(-300.0f, 300.0f);
    float AngleVariation = FMath::FRandRange(-PI/8.0f, PI/8.0f);
    
    // Calcular posição final
    float FinalRadius = GuardianRadius + RadiusVariation;
    float FinalAngle = GuardianAngle + AngleVariation;
    
    FVector Position;
    Position.X = FinalRadius * FMath::Cos(FinalAngle);
    Position.Y = FinalRadius * FMath::Sin(FinalAngle);
    Position.Z = 0.0f; // Será ajustado com base na topografia
    
    // Ajustar altura com base na topografia
    FVector TraceStart = Position + FVector(0, 0, 5000.0f);
    FVector TraceEnd = Position - FVector(0, 0, 5000.0f);
    FHitResult HitResult;
    
    if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_Visibility))
    {
        Position.Z = HitResult.ImpactPoint.Z + 200.0f; // Elevar acima do solo
    }
    else
    {
        Position.Z = 200.0f; // Altura padrão se não encontrar o solo
    }
    
    // Configurar dados do Guardião Espectral
    SpectralGuardianData.Position = Position;
    SpectralGuardianData.Radius = 800.0f;
    SpectralGuardianData.RotationSpeed = 0.2f;
    SpectralGuardianData.PulsationAmplitude = 0.3f;
    SpectralGuardianData.PulsationSpeed = 0.25f;
    SpectralGuardianData.IsActive = false; // Inicialmente inativo, será ativado na fase de intensificação
    
    // Configurar parâmetros PCG
    UPCGGraph* PCGGraph = PCGComponent->GetGraph();
    if (PCGGraph)
    {
        // Adicionar ponto para o Guardião Espectral
        PCGGraph->SetPointData("SpectralGuardianPosition", Position);
        PCGGraph->SetFloatData("SpectralGuardianRadius", SpectralGuardianData.Radius);
        PCGGraph->SetBoolData("SpectralGuardianActive", SpectralGuardianData.IsActive);
    }
    
    // Criar ator para o Guardião Espectral
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    
    AStaticMeshActor* GuardianActor = GetWorld()->SpawnActor<AStaticMeshActor>(Position, FRotator::ZeroRotator, SpawnParams);
    if (GuardianActor)
    {
        // Configurar o ator
        GuardianActor->SetMobility(EComponentMobility::Movable);
        UStaticMeshComponent* MeshComponent = GuardianActor->GetStaticMeshComponent();
        
        if (MeshComponent)
        {
            // Configurar a malha (deve ser definida no Blueprint)
            // MeshComponent->SetStaticMesh(...); // Será configurado via Blueprint
            MeshComponent->SetRelativeScale3D(FVector(2.0f)); // Escala maior para destacar
            MeshComponent->SetCollisionProfileName(TEXT("BlockAll"));
        }
        
        SpectralGuardianData.GuardianActor = GuardianActor;
    }
    
    // Criar sistema de partículas para a aura do Guardião Espectral
    if (SpectralGuardianParticleSystem && GuardianActor)
    {
        UNiagaraComponent* NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            SpectralGuardianParticleSystem,
            GuardianActor->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::SnapToTarget,
            true
        );
        
        if (NiagaraComponent)
        {
            // Configurar parâmetros do sistema de partículas
            NiagaraComponent->SetVariableFloat(TEXT("Radius"), SpectralGuardianData.Radius);
            NiagaraComponent->SetVariableFloat(TEXT("PulsationSpeed"), SpectralGuardianData.PulsationSpeed);
            NiagaraComponent->SetVariableFloat(TEXT("PulsationAmplitude"), SpectralGuardianData.PulsationAmplitude);
            NiagaraComponent->SetVariableFloat(TEXT("EnergyIntensity"), 1.0f);
            NiagaraComponent->SetVariableLinearColor(TEXT("AuraColor"), FLinearColor(0.5f, 0.0f, 1.0f, 1.0f)); // Cor roxa espectral
            
            SpectralGuardianData.AuraComponent = NiagaraComponent;
        }
    }
}

void AAURACRONPCGEnvironment::UpdateSpectralGuardian(float CurrentTime)
{
    // Verificar se estamos no Reino Purgatório
    if (EnvironmentType != EAURACRONEnvironmentType::PurgatoryRealm)
    {
        return;
    }
    
    // Verificar se o Guardião Espectral está configurado
    if (!SpectralGuardianData.GuardianActor || !SpectralGuardianData.AuraComponent)
    {
        return;
    }
    
    // Verificar se o Guardião Espectral deve estar ativo com base na fase atual
    UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>();
    if (PCGSubsystem)
    {
        EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
        bool ShouldBeActive = (CurrentPhase >= EAURACRONMapPhase::Intensification);
        
        // Atualizar estado de ativação se necessário
        if (SpectralGuardianData.IsActive != ShouldBeActive)
        {
            SpectralGuardianData.IsActive = ShouldBeActive;
            
            // Atualizar visibilidade do ator
            if (SpectralGuardianData.GuardianActor)
            {
                SpectralGuardianData.GuardianActor->SetActorHiddenInGame(!ShouldBeActive);
                
                // Atualizar colisão
                UStaticMeshComponent* MeshComponent = SpectralGuardianData.GuardianActor->GetStaticMeshComponent();
                if (MeshComponent)
                {
                    MeshComponent->SetCollisionEnabled(ShouldBeActive ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::NoCollision);
                }
            }
            
            // Atualizar sistema de partículas
            if (SpectralGuardianData.AuraComponent)
            {
                if (ShouldBeActive)
                {
                    SpectralGuardianData.AuraComponent->Activate(true);
                }
                else
                {
                    SpectralGuardianData.AuraComponent->Deactivate();
                }
            }
            
            // Atualizar parâmetros PCG
            UPCGGraph* PCGGraph = PCGComponent->GetGraph();
            if (PCGGraph)
            {
                PCGGraph->SetBoolData("SpectralGuardianActive", SpectralGuardianData.IsActive);
            }
        }
    }
    
    // Se não estiver ativo, não continuar com as atualizações visuais
    if (!SpectralGuardianData.IsActive)
    {
        return;
    }
    
    // Calcular pulsação de energia baseada no tempo
    float TimeFactor = CurrentTime * SpectralGuardianData.PulsationSpeed;
    float EnergyPulsation = 1.0f + FMath::Sin(TimeFactor) * SpectralGuardianData.PulsationAmplitude;
    
    // Atualizar variáveis do sistema de partículas
    if (SpectralGuardianData.AuraComponent)
    {
        SpectralGuardianData.AuraComponent->SetVariableFloat(TEXT("EnergyIntensity"), EnergyPulsation);
    }
    
    // Aplicar rotação lenta ao Guardião Espectral
    if (SpectralGuardianData.GuardianActor)
    {
        FRotator CurrentRotation = SpectralGuardianData.GuardianActor->GetActorRotation();
        float RotationDelta = SpectralGuardianData.RotationSpeed * GetWorld()->GetDeltaSeconds();
        FRotator NewRotation = CurrentRotation + FRotator(0.0f, RotationDelta, 0.0f);
        SpectralGuardianData.GuardianActor->SetActorRotation(NewRotation);
        
        // Aplicar oscilação vertical suave
        FVector CurrentLocation = SpectralGuardianData.GuardianActor->GetActorLocation();
        float HeightOffset = FMath::Sin(CurrentTime * 0.5f) * 30.0f; // Oscilação suave de 30 unidades
        FVector NewLocation = FVector(CurrentLocation.X, CurrentLocation.Y, SpectralGuardianData.Position.Z + HeightOffset);
        SpectralGuardianData.GuardianActor->SetActorLocation(NewLocation);
    }
    
    // Aplicar mecânicas invertidas específicas do Reino Purgatório
    ApplyInvertedPhysicsMechanics(CurrentTime);
}

void AAURACRONPCGEnvironment::ApplyInvertedPhysicsMechanics(float CurrentTime)
{
    // Verificar se estamos no Reino Purgatório e o Guardião está ativo
    if (EnvironmentType != EAURACRONEnvironmentType::PurgatoryRealm || !SpectralGuardianData.IsActive)
    {
        return;
    }
    
    // Aplicar física invertida: gravidade reversa em área ao redor do Guardião
    float InvertedGravityRadius = SpectralGuardianData.Radius * 1.5f;
    FVector GuardianPosition = SpectralGuardianData.Position;
    
    // Encontrar todos os atores na área de influência
    TArray<AActor*> OverlappingActors;
    UKismetSystemLibrary::SphereOverlapActors(
        GetWorld(),
        GuardianPosition,
        InvertedGravityRadius,
        TArray<TEnumAsByte<EObjectTypeQuery>>(),
        nullptr,
        TArray<AActor*>(),
        OverlappingActors
    );
    
    // Aplicar efeitos de física invertida
    for (AActor* Actor : OverlappingActors)
    {
        if (Actor && Actor != SpectralGuardianData.GuardianActor)
        {
            // Aplicar força anti-gravitacional
            if (UPrimitiveComponent* PrimComp = Actor->FindComponentByClass<UPrimitiveComponent>())
            {
                if (PrimComp->IsSimulatingPhysics())
                {
                    FVector AntiGravityForce = FVector(0, 0, 980.0f) * PrimComp->GetMass(); // Força oposta à gravidade
                    float DistanceFromGuardian = FVector::Dist(Actor->GetActorLocation(), GuardianPosition);
                    float ForceMultiplier = 1.0f - (DistanceFromGuardian / InvertedGravityRadius);
                    
                    PrimComp->AddForce(AntiGravityForce * ForceMultiplier);
                }
            }
        }
    }
    
    // Aplicar habilidades invertidas: tempo reverso em área limitada
    ApplyTimeReversalMechanics(CurrentTime, GuardianPosition, InvertedGravityRadius * 0.7f);
}

void AAURACRONPCGEnvironment::ApplyTimeReversalMechanics(float CurrentTime, const FVector& Center, float Radius)
{
    // Implementar mecânica de reversão temporal limitada
    float TimeReversalIntensity = FMath::Sin(CurrentTime * 0.1f) * 0.5f + 0.5f; // Intensidade variável
    
    // Aplicar efeitos visuais de distorção temporal
    if (SpectralGuardianData.AuraComponent)
    {
        SpectralGuardianData.AuraComponent->SetVariableFloat(TEXT("TimeDistortion"), TimeReversalIntensity);
        SpectralGuardianData.AuraComponent->SetVariableFloat(TEXT("ReversalRadius"), Radius);
        
        // Cor que muda com a intensidade da reversão temporal
        FLinearColor TimeColor = FLinearColor::LerpUsingHSV(
            FLinearColor(0.5f, 0.0f, 1.0f, 1.0f), // Roxo normal
            FLinearColor(1.0f, 0.5f, 0.0f, 1.0f), // Laranja durante reversão
            TimeReversalIntensity
        );
        
        SpectralGuardianData.AuraComponent->SetVariableLinearColor(TEXT("AuraColor"), TimeColor);
    }
    
    // Aplicar parâmetros PCG para efeitos de reversão temporal
    if (PCGComponent && PCGComponent->GetGraph())
    {
        PCGComponent->GetGraph()->SetFloatData("TimeReversalIntensity", TimeReversalIntensity);
        PCGComponent->GetGraph()->SetFloatData("ReversalRadius", Radius);
        PCGComponent->GetGraph()->SetPointData("ReversalCenter", Center);
    }
}