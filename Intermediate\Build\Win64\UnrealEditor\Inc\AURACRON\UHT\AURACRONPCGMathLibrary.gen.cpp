// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGMathLibrary.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGMathLibrary() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGMathLibrary();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGMathLibrary_NoRegister();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONNoisePattern();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONSplineCurve();
COREUOBJECT_API UEnum* Z_Construct_UEnum_CoreUObject_EInterpCurveMode();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UBlueprintFunctionLibrary();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAURACRONSplineCurve **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONSplineCurve;
class UScriptStruct* FAURACRONSplineCurve::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONSplineCurve.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONSplineCurve.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONSplineCurve, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONSplineCurve"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONSplineCurve.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para definir uma curva spline matem\xc3\xa1tica\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para definir uma curva spline matem\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControlPoints_MetaData[] = {
		{ "Category", "AURACRONSplineCurve" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos de controle da curva */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos de controle da curva" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tangents_MetaData[] = {
		{ "Category", "AURACRONSplineCurve" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tangentes nos pontos de controle */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tangentes nos pontos de controle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterpolationMode_MetaData[] = {
		{ "Category", "AURACRONSplineCurve" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de interpola\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de interpola\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ControlPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ControlPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tangents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Tangents;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterpolationMode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONSplineCurve>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewProp_ControlPoints_Inner = { "ControlPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewProp_ControlPoints = { "ControlPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONSplineCurve, ControlPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControlPoints_MetaData), NewProp_ControlPoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewProp_Tangents_Inner = { "Tangents", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewProp_Tangents = { "Tangents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONSplineCurve, Tangents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tangents_MetaData), NewProp_Tangents_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewProp_InterpolationMode = { "InterpolationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONSplineCurve, InterpolationMode), Z_Construct_UEnum_CoreUObject_EInterpCurveMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterpolationMode_MetaData), NewProp_InterpolationMode_MetaData) }; // 899754437
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewProp_ControlPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewProp_ControlPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewProp_Tangents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewProp_Tangents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewProp_InterpolationMode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONSplineCurve",
	Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::PropPointers),
	sizeof(FAURACRONSplineCurve),
	alignof(FAURACRONSplineCurve),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONSplineCurve()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONSplineCurve.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONSplineCurve.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONSplineCurve.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONSplineCurve ************************************************

// ********** Begin ScriptStruct FAURACRONNoisePattern *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONNoisePattern;
class UScriptStruct* FAURACRONNoisePattern::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONNoisePattern.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONNoisePattern.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONNoisePattern, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONNoisePattern"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONNoisePattern.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para definir padr\xc3\xb5""es de ru\xc3\xad""do procedural\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para definir padr\xc3\xb5""es de ru\xc3\xad""do procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Frequency_MetaData[] = {
		{ "Category", "AURACRONNoisePattern" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.001" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frequ\xc3\xaancia do ru\xc3\xad""do */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\xaancia do ru\xc3\xad""do" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Amplitude_MetaData[] = {
		{ "Category", "AURACRONNoisePattern" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Amplitude do ru\xc3\xad""do */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Amplitude do ru\xc3\xad""do" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Octaves_MetaData[] = {
		{ "Category", "AURACRONNoisePattern" },
		{ "ClampMax", "8" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de oitavas para ru\xc3\xad""do fractal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de oitavas para ru\xc3\xad""do fractal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Persistence_MetaData[] = {
		{ "Category", "AURACRONNoisePattern" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Persist\xc3\xaancia entre oitavas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Persist\xc3\xaancia entre oitavas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Lacunarity_MetaData[] = {
		{ "Category", "AURACRONNoisePattern" },
		{ "ClampMax", "4.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lacunaridade (frequ\xc3\xaancia entre oitavas) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lacunaridade (frequ\xc3\xaancia entre oitavas)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Seed_MetaData[] = {
		{ "Category", "AURACRONNoisePattern" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Seed para reprodutibilidade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seed para reprodutibilidade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Amplitude;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Octaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Persistence;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Lacunarity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONNoisePattern>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONNoisePattern, Frequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Frequency_MetaData), NewProp_Frequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Amplitude = { "Amplitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONNoisePattern, Amplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Amplitude_MetaData), NewProp_Amplitude_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Octaves = { "Octaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONNoisePattern, Octaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Octaves_MetaData), NewProp_Octaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Persistence = { "Persistence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONNoisePattern, Persistence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Persistence_MetaData), NewProp_Persistence_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Lacunarity = { "Lacunarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONNoisePattern, Lacunarity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Lacunarity_MetaData), NewProp_Lacunarity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONNoisePattern, Seed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Seed_MetaData), NewProp_Seed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Amplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Octaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Persistence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Lacunarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewProp_Seed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONNoisePattern",
	Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::PropPointers),
	sizeof(FAURACRONNoisePattern),
	alignof(FAURACRONNoisePattern),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONNoisePattern()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONNoisePattern.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONNoisePattern.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONNoisePattern.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONNoisePattern ***********************************************

// ********** Begin Class UAURACRONPCGMathLibrary Function CalculateLODLevel ***********************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics
{
	struct AURACRONPCGMathLibrary_eventCalculateLODLevel_Parms
	{
		FVector ObjectPosition;
		FVector ViewerPosition;
		float LOD0Distance;
		float LOD1Distance;
		float LOD2Distance;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular LOD baseado na dist\xc3\xa2ncia */" },
#endif
		{ "CPP_Default_LOD0Distance", "1000.000000" },
		{ "CPP_Default_LOD1Distance", "2000.000000" },
		{ "CPP_Default_LOD2Distance", "4000.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular LOD baseado na dist\xc3\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD0Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD1Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD2Distance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_ObjectPosition = { "ObjectPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateLODLevel_Parms, ObjectPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectPosition_MetaData), NewProp_ObjectPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_ViewerPosition = { "ViewerPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateLODLevel_Parms, ViewerPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerPosition_MetaData), NewProp_ViewerPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_LOD0Distance = { "LOD0Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateLODLevel_Parms, LOD0Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_LOD1Distance = { "LOD1Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateLODLevel_Parms, LOD1Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_LOD2Distance = { "LOD2Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateLODLevel_Parms, LOD2Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateLODLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_ObjectPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_ViewerPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_LOD0Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_LOD1Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_LOD2Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "CalculateLODLevel", Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::AURACRONPCGMathLibrary_eventCalculateLODLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::AURACRONPCGMathLibrary_eventCalculateLODLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execCalculateLODLevel)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ObjectPosition);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerPosition);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LOD0Distance);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LOD1Distance);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LOD2Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAURACRONPCGMathLibrary::CalculateLODLevel(Z_Param_Out_ObjectPosition,Z_Param_Out_ViewerPosition,Z_Param_LOD0Distance,Z_Param_LOD1Distance,Z_Param_LOD2Distance);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function CalculateLODLevel *************************

// ********** Begin Class UAURACRONPCGMathLibrary Function CalculateObjectDensity ******************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics
{
	struct AURACRONPCGMathLibrary_eventCalculateObjectDensity_Parms
	{
		float BaselineFrameRate;
		float CurrentFrameRate;
		float MinDensity;
		float MaxDensity;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular densidade de objetos baseada na performance */" },
#endif
		{ "CPP_Default_BaselineFrameRate", "60.000000" },
		{ "CPP_Default_CurrentFrameRate", "60.000000" },
		{ "CPP_Default_MaxDensity", "1.000000" },
		{ "CPP_Default_MinDensity", "0.100000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular densidade de objetos baseada na performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaselineFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::NewProp_BaselineFrameRate = { "BaselineFrameRate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateObjectDensity_Parms, BaselineFrameRate), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::NewProp_CurrentFrameRate = { "CurrentFrameRate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateObjectDensity_Parms, CurrentFrameRate), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::NewProp_MinDensity = { "MinDensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateObjectDensity_Parms, MinDensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::NewProp_MaxDensity = { "MaxDensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateObjectDensity_Parms, MaxDensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCalculateObjectDensity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::NewProp_BaselineFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::NewProp_CurrentFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::NewProp_MinDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::NewProp_MaxDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "CalculateObjectDensity", Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::AURACRONPCGMathLibrary_eventCalculateObjectDensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::AURACRONPCGMathLibrary_eventCalculateObjectDensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execCalculateObjectDensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_BaselineFrameRate);
	P_GET_PROPERTY(FFloatProperty,Z_Param_CurrentFrameRate);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MinDensity);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxDensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONPCGMathLibrary::CalculateObjectDensity(Z_Param_BaselineFrameRate,Z_Param_CurrentFrameRate,Z_Param_MinDensity,Z_Param_MaxDensity);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function CalculateObjectDensity ********************

// ********** Begin Class UAURACRONPCGMathLibrary Function CreateOrbitalPath ***********************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics
{
	struct AURACRONPCGMathLibrary_eventCreateOrbitalPath_Parms
	{
		FVector Center;
		float Radius;
		float Height;
		int32 NumPoints;
		float PhaseOffset;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar curva orbital para ilhas flutuantes */" },
#endif
		{ "CPP_Default_NumPoints", "16" },
		{ "CPP_Default_PhaseOffset", "0.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar curva orbital para ilhas flutuantes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateOrbitalPath_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateOrbitalPath_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateOrbitalPath_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_NumPoints = { "NumPoints", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateOrbitalPath_Parms, NumPoints), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_PhaseOffset = { "PhaseOffset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateOrbitalPath_Parms, PhaseOffset), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateOrbitalPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_NumPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_PhaseOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "CreateOrbitalPath", Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::AURACRONPCGMathLibrary_eventCreateOrbitalPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::AURACRONPCGMathLibrary_eventCreateOrbitalPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execCreateOrbitalPath)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Height);
	P_GET_PROPERTY(FIntProperty,Z_Param_NumPoints);
	P_GET_PROPERTY(FFloatProperty,Z_Param_PhaseOffset);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONPCGMathLibrary::CreateOrbitalPath(Z_Param_Out_Center,Z_Param_Radius,Z_Param_Height,Z_Param_NumPoints,Z_Param_PhaseOffset);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function CreateOrbitalPath *************************

// ********** Begin Class UAURACRONPCGMathLibrary Function CreateSerpentineCurve *******************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics
{
	struct AURACRONPCGMathLibrary_eventCreateSerpentineCurve_Parms
	{
		FVector StartPoint;
		FVector EndPoint;
		int32 NumControlPoints;
		float Amplitude;
		float Frequency;
		FAURACRONSplineCurve ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar uma curva serpentina suave entre pontos */" },
#endif
		{ "CPP_Default_Amplitude", "1000.000000" },
		{ "CPP_Default_Frequency", "2.000000" },
		{ "CPP_Default_NumControlPoints", "10" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar uma curva serpentina suave entre pontos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPoint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPoint;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumControlPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Amplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_StartPoint = { "StartPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateSerpentineCurve_Parms, StartPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPoint_MetaData), NewProp_StartPoint_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_EndPoint = { "EndPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateSerpentineCurve_Parms, EndPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPoint_MetaData), NewProp_EndPoint_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_NumControlPoints = { "NumControlPoints", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateSerpentineCurve_Parms, NumControlPoints), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_Amplitude = { "Amplitude", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateSerpentineCurve_Parms, Amplitude), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateSerpentineCurve_Parms, Frequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventCreateSerpentineCurve_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONSplineCurve, METADATA_PARAMS(0, nullptr) }; // 2427236723
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_StartPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_EndPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_NumControlPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_Amplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "CreateSerpentineCurve", Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::AURACRONPCGMathLibrary_eventCreateSerpentineCurve_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::AURACRONPCGMathLibrary_eventCreateSerpentineCurve_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execCreateSerpentineCurve)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPoint);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPoint);
	P_GET_PROPERTY(FIntProperty,Z_Param_NumControlPoints);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Amplitude);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Frequency);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONSplineCurve*)Z_Param__Result=UAURACRONPCGMathLibrary::CreateSerpentineCurve(Z_Param_Out_StartPoint,Z_Param_Out_EndPoint,Z_Param_NumControlPoints,Z_Param_Amplitude,Z_Param_Frequency);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function CreateSerpentineCurve *********************

// ********** Begin Class UAURACRONPCGMathLibrary Function DistributePointsAlongCurve **************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics
{
	struct AURACRONPCGMathLibrary_eventDistributePointsAlongCurve_Parms
	{
		FAURACRONSplineCurve Curve;
		float Spacing;
		bool bAlignToTangent;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Distribuir pontos ao longo de uma curva */" },
#endif
		{ "CPP_Default_bAlignToTangent", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distribuir pontos ao longo de uma curva" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Curve_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Curve;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Spacing;
	static void NewProp_bAlignToTangent_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAlignToTangent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_Curve = { "Curve", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventDistributePointsAlongCurve_Parms, Curve), Z_Construct_UScriptStruct_FAURACRONSplineCurve, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Curve_MetaData), NewProp_Curve_MetaData) }; // 2427236723
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_Spacing = { "Spacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventDistributePointsAlongCurve_Parms, Spacing), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_bAlignToTangent_SetBit(void* Obj)
{
	((AURACRONPCGMathLibrary_eventDistributePointsAlongCurve_Parms*)Obj)->bAlignToTangent = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_bAlignToTangent = { "bAlignToTangent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGMathLibrary_eventDistributePointsAlongCurve_Parms), &Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_bAlignToTangent_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventDistributePointsAlongCurve_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_Curve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_Spacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_bAlignToTangent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "DistributePointsAlongCurve", Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::AURACRONPCGMathLibrary_eventDistributePointsAlongCurve_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::AURACRONPCGMathLibrary_eventDistributePointsAlongCurve_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execDistributePointsAlongCurve)
{
	P_GET_STRUCT_REF(FAURACRONSplineCurve,Z_Param_Out_Curve);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Spacing);
	P_GET_UBOOL(Z_Param_bAlignToTangent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONPCGMathLibrary::DistributePointsAlongCurve(Z_Param_Out_Curve,Z_Param_Spacing,Z_Param_bAlignToTangent);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function DistributePointsAlongCurve ****************

// ********** Begin Class UAURACRONPCGMathLibrary Function EvaluateSplineCurve *********************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics
{
	struct AURACRONPCGMathLibrary_eventEvaluateSplineCurve_Parms
	{
		FAURACRONSplineCurve Curve;
		float T;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Avaliar posi\xc3\xa7\xc3\xa3o em uma curva spline */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Avaliar posi\xc3\xa7\xc3\xa3o em uma curva spline" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Curve_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Curve;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_T;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::NewProp_Curve = { "Curve", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventEvaluateSplineCurve_Parms, Curve), Z_Construct_UScriptStruct_FAURACRONSplineCurve, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Curve_MetaData), NewProp_Curve_MetaData) }; // 2427236723
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::NewProp_T = { "T", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventEvaluateSplineCurve_Parms, T), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventEvaluateSplineCurve_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::NewProp_Curve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::NewProp_T,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "EvaluateSplineCurve", Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::AURACRONPCGMathLibrary_eventEvaluateSplineCurve_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::AURACRONPCGMathLibrary_eventEvaluateSplineCurve_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execEvaluateSplineCurve)
{
	P_GET_STRUCT_REF(FAURACRONSplineCurve,Z_Param_Out_Curve);
	P_GET_PROPERTY(FFloatProperty,Z_Param_T);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAURACRONPCGMathLibrary::EvaluateSplineCurve(Z_Param_Out_Curve,Z_Param_T);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function EvaluateSplineCurve ***********************

// ********** Begin Class UAURACRONPCGMathLibrary Function GenerateBreathingForestPositions ********
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics
{
	struct AURACRONPCGMathLibrary_eventGenerateBreathingForestPositions_Parms
	{
		FVector Center;
		float Radius;
		int32 TreeCount;
		float MinDistance;
		float BreathingAmplitude;
		float Time;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar posi\xc3\xa7\xc3\xb5""es para \xc3\xa1rvores em floresta respirante */" },
#endif
		{ "CPP_Default_BreathingAmplitude", "50.000000" },
		{ "CPP_Default_MinDistance", "200.000000" },
		{ "CPP_Default_Time", "0.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar posi\xc3\xa7\xc3\xb5""es para \xc3\xa1rvores em floresta respirante" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TreeCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BreathingAmplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Time;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateBreathingForestPositions_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateBreathingForestPositions_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_TreeCount = { "TreeCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateBreathingForestPositions_Parms, TreeCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_MinDistance = { "MinDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateBreathingForestPositions_Parms, MinDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_BreathingAmplitude = { "BreathingAmplitude", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateBreathingForestPositions_Parms, BreathingAmplitude), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_Time = { "Time", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateBreathingForestPositions_Parms, Time), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateBreathingForestPositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_TreeCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_MinDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_BreathingAmplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_Time,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GenerateBreathingForestPositions", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::AURACRONPCGMathLibrary_eventGenerateBreathingForestPositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::AURACRONPCGMathLibrary_eventGenerateBreathingForestPositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGenerateBreathingForestPositions)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FIntProperty,Z_Param_TreeCount);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MinDistance);
	P_GET_PROPERTY(FFloatProperty,Z_Param_BreathingAmplitude);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Time);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONPCGMathLibrary::GenerateBreathingForestPositions(Z_Param_Out_Center,Z_Param_Radius,Z_Param_TreeCount,Z_Param_MinDistance,Z_Param_BreathingAmplitude,Z_Param_Time);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GenerateBreathingForestPositions **********

// ********** Begin Class UAURACRONPCGMathLibrary Function GenerateCrystallinePlateauVertices ******
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics
{
	struct AURACRONPCGMathLibrary_eventGenerateCrystallinePlateauVertices_Parms
	{
		FVector Center;
		float Radius;
		float Height;
		int32 NumSides;
		float Irregularity;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar v\xc3\xa9rtices para um plat\xc3\xb4 cristalino */" },
#endif
		{ "CPP_Default_Irregularity", "0.200000" },
		{ "CPP_Default_NumSides", "6" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar v\xc3\xa9rtices para um plat\xc3\xb4 cristalino" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumSides;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Irregularity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateCrystallinePlateauVertices_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateCrystallinePlateauVertices_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateCrystallinePlateauVertices_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_NumSides = { "NumSides", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateCrystallinePlateauVertices_Parms, NumSides), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_Irregularity = { "Irregularity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateCrystallinePlateauVertices_Parms, Irregularity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateCrystallinePlateauVertices_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_NumSides,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_Irregularity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GenerateCrystallinePlateauVertices", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::AURACRONPCGMathLibrary_eventGenerateCrystallinePlateauVertices_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::AURACRONPCGMathLibrary_eventGenerateCrystallinePlateauVertices_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGenerateCrystallinePlateauVertices)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Height);
	P_GET_PROPERTY(FIntProperty,Z_Param_NumSides);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Irregularity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONPCGMathLibrary::GenerateCrystallinePlateauVertices(Z_Param_Out_Center,Z_Param_Radius,Z_Param_Height,Z_Param_NumSides,Z_Param_Irregularity);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GenerateCrystallinePlateauVertices ********

// ********** Begin Class UAURACRONPCGMathLibrary Function GenerateFractalNoise ********************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics
{
	struct AURACRONPCGMathLibrary_eventGenerateFractalNoise_Parms
	{
		float X;
		float Y;
		FAURACRONNoisePattern Pattern;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar ru\xc3\xad""do fractal (m\xc3\xbaltiplas oitavas) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar ru\xc3\xad""do fractal (m\xc3\xbaltiplas oitavas)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Pattern_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_X;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Y;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Pattern;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateFractalNoise_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateFractalNoise_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::NewProp_Pattern = { "Pattern", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateFractalNoise_Parms, Pattern), Z_Construct_UScriptStruct_FAURACRONNoisePattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Pattern_MetaData), NewProp_Pattern_MetaData) }; // 3144493556
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateFractalNoise_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::NewProp_Pattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GenerateFractalNoise", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::AURACRONPCGMathLibrary_eventGenerateFractalNoise_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::AURACRONPCGMathLibrary_eventGenerateFractalNoise_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGenerateFractalNoise)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_X);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Y);
	P_GET_STRUCT_REF(FAURACRONNoisePattern,Z_Param_Out_Pattern);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONPCGMathLibrary::GenerateFractalNoise(Z_Param_X,Z_Param_Y,Z_Param_Out_Pattern);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GenerateFractalNoise **********************

// ********** Begin Class UAURACRONPCGMathLibrary Function GenerateHeightMap ***********************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics
{
	struct AURACRONPCGMathLibrary_eventGenerateHeightMap_Parms
	{
		int32 Width;
		int32 Height;
		FAURACRONNoisePattern Pattern;
		float MinHeight;
		float MaxHeight;
		TArray<float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar mapa de altura usando ru\xc3\xad""do */" },
#endif
		{ "CPP_Default_MaxHeight", "1000.000000" },
		{ "CPP_Default_MinHeight", "0.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar mapa de altura usando ru\xc3\xad""do" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Pattern_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Width;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Height;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Pattern;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHeightMap_Parms, Width), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHeightMap_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_Pattern = { "Pattern", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHeightMap_Parms, Pattern), Z_Construct_UScriptStruct_FAURACRONNoisePattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Pattern_MetaData), NewProp_Pattern_MetaData) }; // 3144493556
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_MinHeight = { "MinHeight", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHeightMap_Parms, MinHeight), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_MaxHeight = { "MaxHeight", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHeightMap_Parms, MaxHeight), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHeightMap_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_Pattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_MinHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_MaxHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GenerateHeightMap", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::AURACRONPCGMathLibrary_eventGenerateHeightMap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::AURACRONPCGMathLibrary_eventGenerateHeightMap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGenerateHeightMap)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Width);
	P_GET_PROPERTY(FIntProperty,Z_Param_Height);
	P_GET_STRUCT_REF(FAURACRONNoisePattern,Z_Param_Out_Pattern);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MinHeight);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxHeight);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<float>*)Z_Param__Result=UAURACRONPCGMathLibrary::GenerateHeightMap(Z_Param_Width,Z_Param_Height,Z_Param_Out_Pattern,Z_Param_MinHeight,Z_Param_MaxHeight);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GenerateHeightMap *************************

// ********** Begin Class UAURACRONPCGMathLibrary Function GenerateHexagonalGrid *******************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics
{
	struct AURACRONPCGMathLibrary_eventGenerateHexagonalGrid_Parms
	{
		FVector Center;
		float Radius;
		float Spacing;
		bool bAddRandomOffset;
		float RandomOffsetAmount;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Distribuir pontos em padr\xc3\xa3o hexagonal */" },
#endif
		{ "CPP_Default_bAddRandomOffset", "true" },
		{ "CPP_Default_RandomOffsetAmount", "0.200000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distribuir pontos em padr\xc3\xa3o hexagonal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Spacing;
	static void NewProp_bAddRandomOffset_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAddRandomOffset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RandomOffsetAmount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHexagonalGrid_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHexagonalGrid_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_Spacing = { "Spacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHexagonalGrid_Parms, Spacing), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_bAddRandomOffset_SetBit(void* Obj)
{
	((AURACRONPCGMathLibrary_eventGenerateHexagonalGrid_Parms*)Obj)->bAddRandomOffset = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_bAddRandomOffset = { "bAddRandomOffset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGMathLibrary_eventGenerateHexagonalGrid_Parms), &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_bAddRandomOffset_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_RandomOffsetAmount = { "RandomOffsetAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHexagonalGrid_Parms, RandomOffsetAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateHexagonalGrid_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_Spacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_bAddRandomOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_RandomOffsetAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GenerateHexagonalGrid", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::AURACRONPCGMathLibrary_eventGenerateHexagonalGrid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::AURACRONPCGMathLibrary_eventGenerateHexagonalGrid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGenerateHexagonalGrid)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Spacing);
	P_GET_UBOOL(Z_Param_bAddRandomOffset);
	P_GET_PROPERTY(FFloatProperty,Z_Param_RandomOffsetAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONPCGMathLibrary::GenerateHexagonalGrid(Z_Param_Out_Center,Z_Param_Radius,Z_Param_Spacing,Z_Param_bAddRandomOffset,Z_Param_RandomOffsetAmount);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GenerateHexagonalGrid *********************

// ********** Begin Class UAURACRONPCGMathLibrary Function GenerateLivingCanyonPath ****************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics
{
	struct AURACRONPCGMathLibrary_eventGenerateLivingCanyonPath_Parms
	{
		FVector StartPoint;
		FVector EndPoint;
		float Width;
		float Depth;
		int32 NumSegments;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar pontos para um c\xc3\xa2nion vivo */" },
#endif
		{ "CPP_Default_NumSegments", "20" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar pontos para um c\xc3\xa2nion vivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPoint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPoint;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Depth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumSegments;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_StartPoint = { "StartPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateLivingCanyonPath_Parms, StartPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPoint_MetaData), NewProp_StartPoint_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_EndPoint = { "EndPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateLivingCanyonPath_Parms, EndPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPoint_MetaData), NewProp_EndPoint_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateLivingCanyonPath_Parms, Width), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_Depth = { "Depth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateLivingCanyonPath_Parms, Depth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_NumSegments = { "NumSegments", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateLivingCanyonPath_Parms, NumSegments), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateLivingCanyonPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_StartPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_EndPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_Depth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_NumSegments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GenerateLivingCanyonPath", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::AURACRONPCGMathLibrary_eventGenerateLivingCanyonPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::AURACRONPCGMathLibrary_eventGenerateLivingCanyonPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGenerateLivingCanyonPath)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPoint);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPoint);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Width);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Depth);
	P_GET_PROPERTY(FIntProperty,Z_Param_NumSegments);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONPCGMathLibrary::GenerateLivingCanyonPath(Z_Param_Out_StartPoint,Z_Param_Out_EndPoint,Z_Param_Width,Z_Param_Depth,Z_Param_NumSegments);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GenerateLivingCanyonPath ******************

// ********** Begin Class UAURACRONPCGMathLibrary Function GeneratePerlinNoise2D *******************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics
{
	struct AURACRONPCGMathLibrary_eventGeneratePerlinNoise2D_Parms
	{
		float X;
		float Y;
		FAURACRONNoisePattern Pattern;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar ru\xc3\xad""do Perlin 2D */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar ru\xc3\xad""do Perlin 2D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Pattern_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_X;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Y;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Pattern;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePerlinNoise2D_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePerlinNoise2D_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::NewProp_Pattern = { "Pattern", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePerlinNoise2D_Parms, Pattern), Z_Construct_UScriptStruct_FAURACRONNoisePattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Pattern_MetaData), NewProp_Pattern_MetaData) }; // 3144493556
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePerlinNoise2D_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::NewProp_Pattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GeneratePerlinNoise2D", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::AURACRONPCGMathLibrary_eventGeneratePerlinNoise2D_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::AURACRONPCGMathLibrary_eventGeneratePerlinNoise2D_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGeneratePerlinNoise2D)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_X);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Y);
	P_GET_STRUCT_REF(FAURACRONNoisePattern,Z_Param_Out_Pattern);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONPCGMathLibrary::GeneratePerlinNoise2D(Z_Param_X,Z_Param_Y,Z_Param_Out_Pattern);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GeneratePerlinNoise2D *********************

// ********** Begin Class UAURACRONPCGMathLibrary Function GeneratePerlinNoise3D *******************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics
{
	struct AURACRONPCGMathLibrary_eventGeneratePerlinNoise3D_Parms
	{
		float X;
		float Y;
		float Z;
		FAURACRONNoisePattern Pattern;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar ru\xc3\xad""do Perlin 3D */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar ru\xc3\xad""do Perlin 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Pattern_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_X;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Y;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Z;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Pattern;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePerlinNoise3D_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePerlinNoise3D_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::NewProp_Z = { "Z", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePerlinNoise3D_Parms, Z), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::NewProp_Pattern = { "Pattern", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePerlinNoise3D_Parms, Pattern), Z_Construct_UScriptStruct_FAURACRONNoisePattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Pattern_MetaData), NewProp_Pattern_MetaData) }; // 3144493556
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePerlinNoise3D_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::NewProp_Z,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::NewProp_Pattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GeneratePerlinNoise3D", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::AURACRONPCGMathLibrary_eventGeneratePerlinNoise3D_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::AURACRONPCGMathLibrary_eventGeneratePerlinNoise3D_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGeneratePerlinNoise3D)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_X);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Y);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Z);
	P_GET_STRUCT_REF(FAURACRONNoisePattern,Z_Param_Out_Pattern);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONPCGMathLibrary::GeneratePerlinNoise3D(Z_Param_X,Z_Param_Y,Z_Param_Z,Z_Param_Out_Pattern);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GeneratePerlinNoise3D *********************

// ********** Begin Class UAURACRONPCGMathLibrary Function GeneratePoissonDiscSampling *************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics
{
	struct AURACRONPCGMathLibrary_eventGeneratePoissonDiscSampling_Parms
	{
		FVector Center;
		float Radius;
		float MinDistance;
		int32 MaxAttempts;
		int32 Seed;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Distribuir pontos usando amostragem de Poisson */" },
#endif
		{ "CPP_Default_MaxAttempts", "30" },
		{ "CPP_Default_Seed", "12345" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distribuir pontos usando amostragem de Poisson" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxAttempts;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePoissonDiscSampling_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePoissonDiscSampling_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_MinDistance = { "MinDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePoissonDiscSampling_Parms, MinDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_MaxAttempts = { "MaxAttempts", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePoissonDiscSampling_Parms, MaxAttempts), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePoissonDiscSampling_Parms, Seed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGeneratePoissonDiscSampling_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_MinDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_MaxAttempts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GeneratePoissonDiscSampling", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::AURACRONPCGMathLibrary_eventGeneratePoissonDiscSampling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::AURACRONPCGMathLibrary_eventGeneratePoissonDiscSampling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGeneratePoissonDiscSampling)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MinDistance);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxAttempts);
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(Z_Param_Out_Center,Z_Param_Radius,Z_Param_MinDistance,Z_Param_MaxAttempts,Z_Param_Seed);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GeneratePoissonDiscSampling ***************

// ********** Begin Class UAURACRONPCGMathLibrary Function GenerateTectonicBridgePoints ************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics
{
	struct AURACRONPCGMathLibrary_eventGenerateTectonicBridgePoints_Parms
	{
		FVector StartPoint;
		FVector EndPoint;
		float Width;
		int32 NumSupports;
		float ArchHeight;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar pontos para ponte tect\xc3\xb4nica */" },
#endif
		{ "CPP_Default_ArchHeight", "300.000000" },
		{ "CPP_Default_NumSupports", "5" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar pontos para ponte tect\xc3\xb4nica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPoint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPoint;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumSupports;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ArchHeight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_StartPoint = { "StartPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateTectonicBridgePoints_Parms, StartPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPoint_MetaData), NewProp_StartPoint_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_EndPoint = { "EndPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateTectonicBridgePoints_Parms, EndPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPoint_MetaData), NewProp_EndPoint_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateTectonicBridgePoints_Parms, Width), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_NumSupports = { "NumSupports", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateTectonicBridgePoints_Parms, NumSupports), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_ArchHeight = { "ArchHeight", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateTectonicBridgePoints_Parms, ArchHeight), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGenerateTectonicBridgePoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_StartPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_EndPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_NumSupports,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_ArchHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GenerateTectonicBridgePoints", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::AURACRONPCGMathLibrary_eventGenerateTectonicBridgePoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::AURACRONPCGMathLibrary_eventGenerateTectonicBridgePoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGenerateTectonicBridgePoints)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPoint);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPoint);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Width);
	P_GET_PROPERTY(FIntProperty,Z_Param_NumSupports);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ArchHeight);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONPCGMathLibrary::GenerateTectonicBridgePoints(Z_Param_Out_StartPoint,Z_Param_Out_EndPoint,Z_Param_Width,Z_Param_NumSupports,Z_Param_ArchHeight);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GenerateTectonicBridgePoints **************

// ********** Begin Class UAURACRONPCGMathLibrary Function GetLunarPhaseIntensity ******************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics
{
	struct AURACRONPCGMathLibrary_eventGetLunarPhaseIntensity_Parms
	{
		float TimeOfDay;
		float LunarCycle;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular intensidade baseada na fase da lua */" },
#endif
		{ "CPP_Default_LunarCycle", "28.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular intensidade baseada na fase da lua" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeOfDay;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LunarCycle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::NewProp_TimeOfDay = { "TimeOfDay", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetLunarPhaseIntensity_Parms, TimeOfDay), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::NewProp_LunarCycle = { "LunarCycle", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetLunarPhaseIntensity_Parms, LunarCycle), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetLunarPhaseIntensity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::NewProp_TimeOfDay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::NewProp_LunarCycle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GetLunarPhaseIntensity", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::AURACRONPCGMathLibrary_eventGetLunarPhaseIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::AURACRONPCGMathLibrary_eventGetLunarPhaseIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGetLunarPhaseIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_TimeOfDay);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LunarCycle);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONPCGMathLibrary::GetLunarPhaseIntensity(Z_Param_TimeOfDay,Z_Param_LunarCycle);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GetLunarPhaseIntensity ********************

// ********** Begin Class UAURACRONPCGMathLibrary Function GetSolarIntensity ***********************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics
{
	struct AURACRONPCGMathLibrary_eventGetSolarIntensity_Parms
	{
		float TimeOfDay;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular intensidade solar baseada na hora do dia */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular intensidade solar baseada na hora do dia" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeOfDay;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::NewProp_TimeOfDay = { "TimeOfDay", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetSolarIntensity_Parms, TimeOfDay), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetSolarIntensity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::NewProp_TimeOfDay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GetSolarIntensity", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::AURACRONPCGMathLibrary_eventGetSolarIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::AURACRONPCGMathLibrary_eventGetSolarIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGetSolarIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_TimeOfDay);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONPCGMathLibrary::GetSolarIntensity(Z_Param_TimeOfDay);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GetSolarIntensity *************************

// ********** Begin Class UAURACRONPCGMathLibrary Function GetSplineTangent ************************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics
{
	struct AURACRONPCGMathLibrary_eventGetSplineTangent_Parms
	{
		FAURACRONSplineCurve Curve;
		float T;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter tangente em uma posi\xc3\xa7\xc3\xa3o da curva */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter tangente em uma posi\xc3\xa7\xc3\xa3o da curva" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Curve_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Curve;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_T;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::NewProp_Curve = { "Curve", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetSplineTangent_Parms, Curve), Z_Construct_UScriptStruct_FAURACRONSplineCurve, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Curve_MetaData), NewProp_Curve_MetaData) }; // 2427236723
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::NewProp_T = { "T", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetSplineTangent_Parms, T), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetSplineTangent_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::NewProp_Curve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::NewProp_T,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GetSplineTangent", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::AURACRONPCGMathLibrary_eventGetSplineTangent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::AURACRONPCGMathLibrary_eventGetSplineTangent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGetSplineTangent)
{
	P_GET_STRUCT_REF(FAURACRONSplineCurve,Z_Param_Out_Curve);
	P_GET_PROPERTY(FFloatProperty,Z_Param_T);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAURACRONPCGMathLibrary::GetSplineTangent(Z_Param_Out_Curve,Z_Param_T);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GetSplineTangent **************************

// ********** Begin Class UAURACRONPCGMathLibrary Function GetTimeBasedPosition ********************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics
{
	struct AURACRONPCGMathLibrary_eventGetTimeBasedPosition_Parms
	{
		FVector BasePosition;
		float TimeOfDay;
		float Amplitude;
		float Frequency;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular posi\xc3\xa7\xc3\xa3o baseada no ciclo dia/noite */" },
#endif
		{ "CPP_Default_Amplitude", "100.000000" },
		{ "CPP_Default_Frequency", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular posi\xc3\xa7\xc3\xa3o baseada no ciclo dia/noite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BasePosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BasePosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeOfDay;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Amplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::NewProp_BasePosition = { "BasePosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetTimeBasedPosition_Parms, BasePosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BasePosition_MetaData), NewProp_BasePosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::NewProp_TimeOfDay = { "TimeOfDay", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetTimeBasedPosition_Parms, TimeOfDay), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::NewProp_Amplitude = { "Amplitude", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetTimeBasedPosition_Parms, Amplitude), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetTimeBasedPosition_Parms, Frequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventGetTimeBasedPosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::NewProp_BasePosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::NewProp_TimeOfDay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::NewProp_Amplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "GetTimeBasedPosition", Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::AURACRONPCGMathLibrary_eventGetTimeBasedPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::AURACRONPCGMathLibrary_eventGetTimeBasedPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execGetTimeBasedPosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_BasePosition);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TimeOfDay);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Amplitude);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Frequency);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAURACRONPCGMathLibrary::GetTimeBasedPosition(Z_Param_Out_BasePosition,Z_Param_TimeOfDay,Z_Param_Amplitude,Z_Param_Frequency);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function GetTimeBasedPosition **********************

// ********** Begin Class UAURACRONPCGMathLibrary Function ShouldRenderObject **********************
struct Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics
{
	struct AURACRONPCGMathLibrary_eventShouldRenderObject_Parms
	{
		FVector ObjectPosition;
		float ObjectRadius;
		FVector ViewerPosition;
		FVector ViewerForward;
		float ViewDistance;
		float FOVDegrees;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Math" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se um objeto deve ser renderizado (frustum culling) */" },
#endif
		{ "CPP_Default_FOVDegrees", "90.000000" },
		{ "CPP_Default_ViewDistance", "5000.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se um objeto deve ser renderizado (frustum culling)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerForward_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerForward;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ViewDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FOVDegrees;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ObjectPosition = { "ObjectPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventShouldRenderObject_Parms, ObjectPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectPosition_MetaData), NewProp_ObjectPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ObjectRadius = { "ObjectRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventShouldRenderObject_Parms, ObjectRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ViewerPosition = { "ViewerPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventShouldRenderObject_Parms, ViewerPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerPosition_MetaData), NewProp_ViewerPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ViewerForward = { "ViewerForward", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventShouldRenderObject_Parms, ViewerForward), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerForward_MetaData), NewProp_ViewerForward_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ViewDistance = { "ViewDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventShouldRenderObject_Parms, ViewDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_FOVDegrees = { "FOVDegrees", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGMathLibrary_eventShouldRenderObject_Parms, FOVDegrees), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGMathLibrary_eventShouldRenderObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGMathLibrary_eventShouldRenderObject_Parms), &Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ObjectPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ObjectRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ViewerPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ViewerForward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ViewDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_FOVDegrees,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGMathLibrary, nullptr, "ShouldRenderObject", Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::AURACRONPCGMathLibrary_eventShouldRenderObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::AURACRONPCGMathLibrary_eventShouldRenderObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGMathLibrary::execShouldRenderObject)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ObjectPosition);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ObjectRadius);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerPosition);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerForward);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ViewDistance);
	P_GET_PROPERTY(FFloatProperty,Z_Param_FOVDegrees);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAURACRONPCGMathLibrary::ShouldRenderObject(Z_Param_Out_ObjectPosition,Z_Param_ObjectRadius,Z_Param_Out_ViewerPosition,Z_Param_Out_ViewerForward,Z_Param_ViewDistance,Z_Param_FOVDegrees);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGMathLibrary Function ShouldRenderObject ************************

// ********** Begin Class UAURACRONPCGMathLibrary **************************************************
void UAURACRONPCGMathLibrary::StaticRegisterNativesUAURACRONPCGMathLibrary()
{
	UClass* Class = UAURACRONPCGMathLibrary::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateLODLevel", &UAURACRONPCGMathLibrary::execCalculateLODLevel },
		{ "CalculateObjectDensity", &UAURACRONPCGMathLibrary::execCalculateObjectDensity },
		{ "CreateOrbitalPath", &UAURACRONPCGMathLibrary::execCreateOrbitalPath },
		{ "CreateSerpentineCurve", &UAURACRONPCGMathLibrary::execCreateSerpentineCurve },
		{ "DistributePointsAlongCurve", &UAURACRONPCGMathLibrary::execDistributePointsAlongCurve },
		{ "EvaluateSplineCurve", &UAURACRONPCGMathLibrary::execEvaluateSplineCurve },
		{ "GenerateBreathingForestPositions", &UAURACRONPCGMathLibrary::execGenerateBreathingForestPositions },
		{ "GenerateCrystallinePlateauVertices", &UAURACRONPCGMathLibrary::execGenerateCrystallinePlateauVertices },
		{ "GenerateFractalNoise", &UAURACRONPCGMathLibrary::execGenerateFractalNoise },
		{ "GenerateHeightMap", &UAURACRONPCGMathLibrary::execGenerateHeightMap },
		{ "GenerateHexagonalGrid", &UAURACRONPCGMathLibrary::execGenerateHexagonalGrid },
		{ "GenerateLivingCanyonPath", &UAURACRONPCGMathLibrary::execGenerateLivingCanyonPath },
		{ "GeneratePerlinNoise2D", &UAURACRONPCGMathLibrary::execGeneratePerlinNoise2D },
		{ "GeneratePerlinNoise3D", &UAURACRONPCGMathLibrary::execGeneratePerlinNoise3D },
		{ "GeneratePoissonDiscSampling", &UAURACRONPCGMathLibrary::execGeneratePoissonDiscSampling },
		{ "GenerateTectonicBridgePoints", &UAURACRONPCGMathLibrary::execGenerateTectonicBridgePoints },
		{ "GetLunarPhaseIntensity", &UAURACRONPCGMathLibrary::execGetLunarPhaseIntensity },
		{ "GetSolarIntensity", &UAURACRONPCGMathLibrary::execGetSolarIntensity },
		{ "GetSplineTangent", &UAURACRONPCGMathLibrary::execGetSplineTangent },
		{ "GetTimeBasedPosition", &UAURACRONPCGMathLibrary::execGetTimeBasedPosition },
		{ "ShouldRenderObject", &UAURACRONPCGMathLibrary::execShouldRenderObject },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAURACRONPCGMathLibrary;
UClass* UAURACRONPCGMathLibrary::GetPrivateStaticClass()
{
	using TClass = UAURACRONPCGMathLibrary;
	if (!Z_Registration_Info_UClass_UAURACRONPCGMathLibrary.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGMathLibrary"),
			Z_Registration_Info_UClass_UAURACRONPCGMathLibrary.InnerSingleton,
			StaticRegisterNativesUAURACRONPCGMathLibrary,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAURACRONPCGMathLibrary.InnerSingleton;
}
UClass* Z_Construct_UClass_UAURACRONPCGMathLibrary_NoRegister()
{
	return UAURACRONPCGMathLibrary::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAURACRONPCGMathLibrary_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Biblioteca de fun\xc3\xa7\xc3\xb5""es matem\xc3\xa1ticas avan\xc3\xa7""adas para gera\xc3\xa7\xc3\xa3o procedural do AURACRON\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGMathLibrary.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGMathLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biblioteca de fun\xc3\xa7\xc3\xb5""es matem\xc3\xa1ticas avan\xc3\xa7""adas para gera\xc3\xa7\xc3\xa3o procedural do AURACRON" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateLODLevel, "CalculateLODLevel" }, // 2890190890
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_CalculateObjectDensity, "CalculateObjectDensity" }, // 2157191684
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateOrbitalPath, "CreateOrbitalPath" }, // 3084436447
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_CreateSerpentineCurve, "CreateSerpentineCurve" }, // 3337960415
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_DistributePointsAlongCurve, "DistributePointsAlongCurve" }, // 1793462746
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_EvaluateSplineCurve, "EvaluateSplineCurve" }, // 1627119709
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateBreathingForestPositions, "GenerateBreathingForestPositions" }, // 822087191
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateCrystallinePlateauVertices, "GenerateCrystallinePlateauVertices" }, // 33793422
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateFractalNoise, "GenerateFractalNoise" }, // 1181630755
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHeightMap, "GenerateHeightMap" }, // 3520063555
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateHexagonalGrid, "GenerateHexagonalGrid" }, // 589186277
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateLivingCanyonPath, "GenerateLivingCanyonPath" }, // 1727004696
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise2D, "GeneratePerlinNoise2D" }, // 1563576254
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePerlinNoise3D, "GeneratePerlinNoise3D" }, // 1341050622
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GeneratePoissonDiscSampling, "GeneratePoissonDiscSampling" }, // 4040144463
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GenerateTectonicBridgePoints, "GenerateTectonicBridgePoints" }, // 2163314843
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetLunarPhaseIntensity, "GetLunarPhaseIntensity" }, // 3442620951
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSolarIntensity, "GetSolarIntensity" }, // 4271182974
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetSplineTangent, "GetSplineTangent" }, // 1820179598
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_GetTimeBasedPosition, "GetTimeBasedPosition" }, // 380734215
		{ &Z_Construct_UFunction_UAURACRONPCGMathLibrary_ShouldRenderObject, "ShouldRenderObject" }, // 4181682113
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAURACRONPCGMathLibrary>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAURACRONPCGMathLibrary_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UBlueprintFunctionLibrary,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONPCGMathLibrary_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAURACRONPCGMathLibrary_Statics::ClassParams = {
	&UAURACRONPCGMathLibrary::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONPCGMathLibrary_Statics::Class_MetaDataParams), Z_Construct_UClass_UAURACRONPCGMathLibrary_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAURACRONPCGMathLibrary()
{
	if (!Z_Registration_Info_UClass_UAURACRONPCGMathLibrary.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAURACRONPCGMathLibrary.OuterSingleton, Z_Construct_UClass_UAURACRONPCGMathLibrary_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAURACRONPCGMathLibrary.OuterSingleton;
}
UAURACRONPCGMathLibrary::UAURACRONPCGMathLibrary(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAURACRONPCGMathLibrary);
UAURACRONPCGMathLibrary::~UAURACRONPCGMathLibrary() {}
// ********** End Class UAURACRONPCGMathLibrary ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONSplineCurve::StaticStruct, Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics::NewStructOps, TEXT("AURACRONSplineCurve"), &Z_Registration_Info_UScriptStruct_FAURACRONSplineCurve, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONSplineCurve), 2427236723U) },
		{ FAURACRONNoisePattern::StaticStruct, Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics::NewStructOps, TEXT("AURACRONNoisePattern"), &Z_Registration_Info_UScriptStruct_FAURACRONNoisePattern, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONNoisePattern), 3144493556U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAURACRONPCGMathLibrary, UAURACRONPCGMathLibrary::StaticClass, TEXT("UAURACRONPCGMathLibrary"), &Z_Registration_Info_UClass_UAURACRONPCGMathLibrary, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAURACRONPCGMathLibrary), 3280906085U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h__Script_AURACRON_2458879627(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
