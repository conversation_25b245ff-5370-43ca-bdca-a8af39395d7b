// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Data/AURACRONEnums.h"
#include "AURACRONPCGTypes.generated.h"

// Usando definições centralizadas de Data/AURACRONEnums.h:
// - EAURACRONMapPhase
// - EAURACRONEnvironmentType

/**
 * Estrutura para vantagens táticas específicas de cada mapa
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONMapTacticalAdvantages
{
    GENERATED_BODY()

    /** Bônus de velocidade de movimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float MovementSpeedBonus = 0.0f;

    /** Bônus de alcance de habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float AbilityRangeBonus = 0.0f;

    /** Bônus de área de efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float AreaOfEffectBonus = 0.0f;

    /** Bônus de visão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float VisionRangeBonus = 0.0f;

    /** Bônus de regeneração de recursos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float ResourceRegenBonus = 0.0f;

    /** Mecânicas específicas do mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<FString, float> SpecificMechanics;

    /** Descrição das vantagens táticas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString Description;
};

/**
 * Estrutura para configurações específicas de cada tipo de mapa
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONMapTypeConfig
{
    GENERATED_BODY()

    /** Tipo de ambiente do mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONEnvironmentType EnvironmentType = EAURACRONEnvironmentType::RadiantPlains;

    /** Layout do mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString Layout;

    /** Recursos especiais do mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> SpecialResources;

    /** Objetivos específicos do mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> Objectives;

    /** Vantagens estratégicas do mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString StrategicAdvantage;

    /** Vantagens táticas específicas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FAURACRONMapTacticalAdvantages TacticalAdvantages;

    /** Altura base do terreno */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float BaseHeight = 0.0f;

    /** Cor dominante do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor DominantColor = FLinearColor::White;
};


/**
 * Estrutura para configurações de efeitos visuais
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONVisualEffectSettings
{
    GENERATED_BODY()

    /** Intensidade do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Intensity = 1.0f;

    /** Duração do efeito em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Duration = 3.0f;

    /** Escala de qualidade para ajuste de performance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float QualityScale = 1.0f;

    /** Cor principal do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor PrimaryColor = FLinearColor::White;

    /** Cor secundária do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor SecondaryColor = FLinearColor::Black;
};

/**
 * Estrutura para configurações de ambiente
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONEnvironmentConfig
{
    GENERATED_BODY()

    /** Tipo de ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONEnvironmentType EnvironmentType = EAURACRONEnvironmentType::RadiantPlains;

    /** Fase atual do mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONMapPhase CurrentPhase = EAURACRONMapPhase::Awakening;

    /** Intensidade da iluminação ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float AmbientLightIntensity = 1.0f;

    /** Cor da iluminação ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor AmbientLightColor = FLinearColor(0.5f, 0.5f, 0.5f, 1.0f);

    /** Cor primária do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor PrimaryColor = FLinearColor::White;

    /** Intensidade da luz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float LightIntensity = 1.0f;

    /** Rugosidade do material */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float MaterialRoughness = 0.5f;

    /** Escala de atividade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float ActivityScale = 1.0f;

    /** Intensidade da névoa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FogIntensity = 0.5f;

    /** Cor da névoa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor FogColor = FLinearColor(0.1f, 0.1f, 0.1f, 1.0f);
};