// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "PCG/AURACRONPCGChaosPortal.h"
#include "Data/AURACRONEnums.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/StaticMeshComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/AudioComponent.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "Engine/World.h"

// Sets default values
AAURACRONPCGChaosPortal::AAURACRONPCGChaosPortal()
{
    // Set this actor to call Tick() every frame
    PrimaryActorTick.bCanEverTick = true;

    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // Criar componente de malha estática
    PortalMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("PortalMesh"));
    PortalMesh->SetupAttachment(RootComponent);
    PortalMesh->SetCollisionProfileName(TEXT("NoCollision"));
    PortalMesh->SetGenerateOverlapEvents(false);

    // Criar componente de partículas Niagara
    PortalEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("PortalEffect"));
    PortalEffect->SetupAttachment(RootComponent);

    // Criar componente de luz
    PortalLight = CreateDefaultSubobject<UPointLightComponent>(TEXT("PortalLight"));
    PortalLight->SetupAttachment(RootComponent);
    PortalLight->SetLightColor(FLinearColor(0.8f, 0.2f, 0.9f, 1.0f)); // Roxo
    PortalLight->SetIntensity(8000.0f);
    PortalLight->SetAttenuationRadius(1000.0f);
    PortalLight->SetCastShadows(false);

    // Criar componente de áudio
    PortalSound = CreateDefaultSubobject<UAudioComponent>(TEXT("PortalSound"));
    PortalSound->SetupAttachment(RootComponent);
    PortalSound->bAutoActivate = false;

    // Criar componente de colisão para trigger
    TriggerSphere = CreateDefaultSubobject<USphereComponent>(TEXT("TriggerSphere"));
    TriggerSphere->SetupAttachment(RootComponent);
    TriggerSphere->SetSphereRadius(500.0f);
    TriggerSphere->SetCollisionProfileName(TEXT("Trigger"));
    TriggerSphere->SetGenerateOverlapEvents(true);

    // Valores padrão
    EffectRadius = 500.0f;
    PortalDuration = 0.0f; // Permanente por padrão
    PortalIntensity = 1.0f;
    PortalColor = FLinearColor(0.8f, 0.2f, 0.9f, 1.0f); // Roxo
    RotationSpeed = 20.0f;
    PulsateFrequency = 1.0f;
    PulsateIntensity = 0.3f;
    EffectInterval = 5.0f;
    QualityScale = 1.0f;
    CurrentMapPhase = EAURACRONMapPhase::Awakening;
    ElapsedTime = 0.0f;
    TimeSinceLastEffect = 0.0f;
    bPortalActive = false;
    bFadingOut = false;
    FadeOutTime = 1.0f;
    FadeOutElapsed = 0.0f;
}

// Called when the game starts or when spawned
void AAURACRONPCGChaosPortal::BeginPlay()
{
    Super::BeginPlay();
    
    // Configurar trigger events
    TriggerSphere->OnComponentBeginOverlap.AddDynamic(this, &AAURACRONPCGChaosPortal::OnPlayerEnterPortalRadius);
    TriggerSphere->OnComponentEndOverlap.AddDynamic(this, &AAURACRONPCGChaosPortal::OnPlayerExitPortalRadius);
    
    // Criar material dinâmico
    if (PortalMesh && PortalMesh->GetMaterial(0))
    {
        PortalDynamicMaterial = UMaterialInstanceDynamic::Create(PortalMesh->GetMaterial(0), this);
        PortalMesh->SetMaterial(0, PortalDynamicMaterial);
    }
    
    // Desativar portal inicialmente
    PortalMesh->SetVisibility(false);
    PortalEffect->Deactivate();
    PortalLight->SetVisibility(false);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosPortal: Inicializado"));
}

// Called every frame
void AAURACRONPCGChaosPortal::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Processar portal ativo
    if (bPortalActive)
    {
        // Atualizar tempo decorrido
        ElapsedTime += DeltaTime;
        TimeSinceLastEffect += DeltaTime;
        
        // Verificar se o portal expirou
        if (PortalDuration > 0.0f && ElapsedTime >= PortalDuration && !bFadingOut)
        {
            // Iniciar fade out
            DeactivatePortal(FadeOutTime);
            return;
        }
        
        // Processar fade out se necessário
        if (bFadingOut)
        {
            ProcessFadeOut(DeltaTime);
            return;
        }
        
        // Atualizar efeitos visuais
        UpdateVisualEffects(DeltaTime);
        
        // Verificar se é hora de disparar um efeito
        if (TimeSinceLastEffect >= EffectInterval)
        {
            // Resetar timer
            TimeSinceLastEffect = 0.0f;
            
            // Chance de disparar um efeito baseado na fase do mapa
            float SpawnProbability = 0.2f;
            switch (CurrentMapPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    SpawnProbability = 0.1f;
                    break;
                case EAURACRONMapPhase::Convergence:
                    SpawnProbability = 0.2f;
                    break;
                case EAURACRONMapPhase::Intensification:
                    SpawnProbability = 0.3f;
                    break;
                case EAURACRONMapPhase::Resolution:
                    SpawnProbability = 0.5f;
                    break;
                default:
                    break;
            }
            
            // Disparar efeito com probabilidade calculada
            TriggerPortalEffect(SpawnProbability);
        }
    }
}

void AAURACRONPCGChaosPortal::ActivatePortal(float Duration, float Intensity)
{
    // Configurar parâmetros do portal
    PortalDuration = Duration;
    PortalIntensity = Intensity;
    
    // Reiniciar timers
    ElapsedTime = 0.0f;
    TimeSinceLastEffect = 0.0f;
    
    // Ativar portal
    bPortalActive = true;
    bFadingOut = false;
    
    // Ativar efeitos visuais
    PortalMesh->SetVisibility(true);
    PortalEffect->Activate(true);
    PortalLight->SetVisibility(true);
    PortalSound->Play();
    
    // Atualizar raio do trigger
    TriggerSphere->SetSphereRadius(EffectRadius);
    
    // Atualizar efeitos visuais iniciais
    UpdateVisualEffects(0.0f);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosPortal: Portal ativado com intensidade %.2f e duração %.1f"), 
           Intensity, Duration);
}

void AAURACRONPCGChaosPortal::DeactivatePortal(float InFadeOutTime)
{
    // Configurar fade out
    bFadingOut = true;
    FadeOutTime = InFadeOutTime;
    FadeOutElapsed = 0.0f;
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosPortal: Iniciando desativação do portal com fade out de %.1f segundos"), 
           FadeOutTime);
}

void AAURACRONPCGChaosPortal::SetQualityScale(float NewQualityScale)
{
    QualityScale = FMath::Clamp(NewQualityScale, 0.1f, 1.0f);
    
    // Ajustar qualidade dos efeitos visuais
    if (PortalEffect)
    {
        // Ajustar densidade de partículas baseado na escala de qualidade
        PortalEffect->SetFloatParameter(FName("ParticleDensity"), QualityScale);
        
        // Ajustar qualidade de iluminação
        PortalLight->SetIntensity(8000.0f * QualityScale);
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Escala de qualidade ajustada para %.2f"), QualityScale);
}

void AAURACRONPCGChaosPortal::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    CurrentMapPhase = MapPhase;
    
    // Ajustar parâmetros baseados na fase do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            PortalColor = FLinearColor(0.5f, 0.2f, 0.8f, 1.0f); // Roxo claro
            PulsateIntensity = 0.2f;
            PulsateFrequency = 0.5f;
            RotationSpeed = 10.0f;
            break;
            
        case EAURACRONMapPhase::Convergence:
            PortalColor = FLinearColor(0.6f, 0.2f, 0.9f, 1.0f); // Roxo médio
            PulsateIntensity = 0.3f;
            PulsateFrequency = 0.8f;
            RotationSpeed = 15.0f;
            break;
            
        case EAURACRONMapPhase::Intensification:
            PortalColor = FLinearColor(0.7f, 0.1f, 1.0f, 1.0f); // Roxo intenso
            PulsateIntensity = 0.4f;
            PulsateFrequency = 1.2f;
            RotationSpeed = 20.0f;
            break;
            
        case EAURACRONMapPhase::Resolution:
            PortalColor = FLinearColor(0.9f, 0.1f, 1.0f, 1.0f); // Roxo brilhante
            PulsateIntensity = 0.5f;
            PulsateFrequency = 1.5f;
            RotationSpeed = 25.0f;
            break;
            
        default:
            break;
    }
    
    // Atualizar efeitos visuais
    if (bPortalActive)
    {
        UpdateVisualEffects(0.0f);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosPortal: Atualizado para fase %d"), (int32)CurrentMapPhase);
}

void AAURACRONPCGChaosPortal::TriggerPortalEffect(float SpawnProbability)
{
    // Verificar se devemos spawnar um efeito baseado na probabilidade
    if (FMath::FRand() > SpawnProbability)
    {
        return;
    }
    
    // Determinar tipo de efeito baseado na fase do mapa
    int32 EffectType = FMath::RandRange(0, 2);
    
    // Aumentar chance de efeitos mais intensos na fase Resolution
    if (CurrentMapPhase == EAURACRONMapPhase::Resolution)
    {
        EffectType = FMath::RandRange(1, 3);
    }
    
    // Aplicar efeito baseado no tipo
    switch (EffectType)
    {
        case 0: // Pulso de energia menor
            {
                // Criar efeito visual temporário
                UNiagaraSystem* PulseSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalPulse"));
                if (!PulseSystem)
                {
                    // Criar sistema de partículas específico para pulso do portal
                    PulseSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalPulse"));
                    if (!PulseSystem)
                    {
                        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosPortal: Sistema de pulso não encontrado, usando efeito padrão do portal"));
                        PulseSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalDefault"));
                    }
                }
                
                UNiagaraComponent* PulseEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    PulseSystem,
                    GetActorLocation(),
                    GetActorRotation(),
                    FVector(1.0f),
                    true,
                    true,
                    ENCPoolMethod::AutoRelease
                );
                
                if (PulseEffect)
                {
                    PulseEffect->SetColorParameter(FName("Color"), PortalColor);
                    PulseEffect->SetFloatParameter(FName("Size"), 200.0f * PortalIntensity);
                }
                
                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Efeito de pulso menor gerado"));
            }
            break;
            
        case 1: // Distorção espacial
            {
                // Criar efeito de distorção
                UNiagaraSystem* DistortionSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalDistortion"));
                if (!DistortionSystem)
                {
                    // Criar sistema de partículas específico para distorção do portal
                    DistortionSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalDistortionFallback"));
                    if (!DistortionSystem)
                    {
                        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosPortal: Sistema de distorção não encontrado, usando efeito padrão do portal"));
                        DistortionSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalDefault"));
                    }
                }
                
                UNiagaraComponent* DistortionEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    DistortionSystem,
                    GetActorLocation(),
                    GetActorRotation(),
                    FVector(1.0f),
                    true,
                    true,
                    ENCPoolMethod::AutoRelease
                );
                
                if (DistortionEffect)
                {
                    DistortionEffect->SetColorParameter(FName("Color"), PortalColor);
                    DistortionEffect->SetFloatParameter(FName("Intensity"), PortalIntensity);
                    DistortionEffect->SetFloatParameter(FName("Duration"), 3.0f);
                }
                
                // Aplicar efeito de distorção de tela aos jogadores próximos
                ApplyEffectsToPlayers();
                
                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Efeito de distorção espacial gerado"));
            }
            break;
            
        case 2: // Spawn de objeto de caos
            {
                // Calcular posição aleatória próxima ao portal
                FVector SpawnLocation = GetActorLocation();
                SpawnLocation += FMath::VRand() * FMath::RandRange(100.0f, 300.0f);
                SpawnLocation.Z = GetActorLocation().Z; // Manter mesma altura
                
                // Spawnar objeto de caos
                TSubclassOf<AActor> ChaosObjectClass = LoadClass<AActor>(nullptr, TEXT("/Game/Blueprints/Chaos/BP_ChaosObject"));
                if (!ChaosObjectClass)
                {
                    // Tentar carregar classe alternativa de objeto de caos
                    ChaosObjectClass = LoadClass<AActor>(nullptr, TEXT("/Game/Blueprints/Chaos/BP_ChaosObjectBasic"));
                    if (!ChaosObjectClass)
                    {
                        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosPortal: Classe de objeto de caos não encontrada, usando objeto padrão"));
                        ChaosObjectClass = LoadClass<AActor>(nullptr, TEXT("/Game/Blueprints/Chaos/BP_ChaosObjectDefault"));
                        if (!ChaosObjectClass)
                        {
                            ChaosObjectClass = AActor::StaticClass();
                        }
                    }
                }
                
                AActor* ChaosObject = GetWorld()->SpawnActor<AActor>(
                    ChaosObjectClass,
                    SpawnLocation,
                    FRotator::ZeroRotator
                );
                
                if (ChaosObject)
                {
                    // Configurar objeto de caos com duração limitada
                    ChaosObject->SetLifeSpan(10.0f + FMath::RandRange(-2.0f, 5.0f));
                    
                    // Adicionar componente de efeito visual se disponível
                    if (UStaticMeshComponent* MeshComp = ChaosObject->FindComponentByClass<UStaticMeshComponent>())
                    {
                        MeshComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
                        MeshComp->SetCollisionResponseToAllChannels(ECR_Overlap);
                    }
                }
                
                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Objeto de caos gerado"));
            }
            break;
            
        case 3: // Efeito de caos maior (apenas na fase Resolution)
            {
                // Criar efeito visual maior
                UNiagaraSystem* MajorSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalMajor"));
                if (!MajorSystem)
                {
                    // Fallback para sistema genérico
                    MajorSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Engine/VFX/Niagara/Systems/NS_GPUSprites"));
                }
                
                UNiagaraComponent* MajorEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    MajorSystem,
                    GetActorLocation(),
                    GetActorRotation(),
                    FVector(2.0f),
                    true,
                    true,
                    ENCPoolMethod::AutoRelease
                );
                
                if (MajorEffect)
                {
                    MajorEffect->SetColorParameter(FName("Color"), PortalColor);
                    MajorEffect->SetFloatParameter(FName("Intensity"), PortalIntensity * 2.0f);
                    MajorEffect->SetFloatParameter(FName("Duration"), 5.0f);
                }
                
                // Aplicar efeito maior aos jogadores próximos
                ApplyEffectsToPlayers();
                
                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Efeito de caos maior gerado"));
            }
            break;
            
        default:
            break;
    }
}

void AAURACRONPCGChaosPortal::UpdateVisualEffects(float DeltaTime)
{
    // Atualizar rotação do portal
    if (PortalMesh)
    {
        FRotator NewRotation = PortalMesh->GetRelativeRotation();
        NewRotation.Yaw += RotationSpeed * DeltaTime;
        PortalMesh->SetRelativeRotation(NewRotation);
    }
    
    // Calcular pulsação
    float PulseValue = FMath::Sin(ElapsedTime * PulsateFrequency * PI * 2.0f) * PulsateIntensity + 1.0f;
    
    // Atualizar material dinâmico
    if (PortalDynamicMaterial)
    {
        PortalDynamicMaterial->SetVectorParameterValue(FName("Color"), PortalColor);
        PortalDynamicMaterial->SetScalarParameterValue(FName("Intensity"), PortalIntensity * PulseValue);
        PortalDynamicMaterial->SetScalarParameterValue(FName("Opacity"), 1.0f);
    }
    
    // Atualizar efeito de partículas
    if (PortalEffect)
    {
        PortalEffect->SetColorParameter(FName("Color"), PortalColor);
        PortalEffect->SetFloatParameter(FName("Size"), EffectRadius * PulseValue * 0.5f);
        PortalEffect->SetFloatParameter(FName("Intensity"), PortalIntensity * PulseValue);
    }
    
    // Atualizar luz
    if (PortalLight)
    {
        PortalLight->SetLightColor(PortalColor);
        PortalLight->SetIntensity(8000.0f * PortalIntensity * PulseValue * QualityScale);
    }
}

void AAURACRONPCGChaosPortal::ApplyEffectsToPlayers()
{
    // Obter todos os jogadores
    TArray<AActor*> Players;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ACharacter::StaticClass(), Players);
    
    // Aplicar efeitos aos jogadores dentro do raio
    for (AActor* Player : Players)
    {
        if (!Player)
        {
            continue;
        }
        
        // Calcular distância do jogador ao portal
        float Distance = FVector::Dist(Player->GetActorLocation(), GetActorLocation());
        
        // Verificar se o jogador está dentro do raio de efeito
        if (Distance <= EffectRadius)
        {
            // Calcular intensidade baseada na distância (mais forte no centro)
            float DistanceFactor = 1.0f - (Distance / EffectRadius);
            float EffectIntensity = PortalIntensity * DistanceFactor;
            
            // Aplicar efeito ao jogador (distorção de tela, dano, etc.)
            // Implementação específica depende do design do jogo
            
            // Exemplo: Aplicar efeito de post-process ao jogador
            APlayerController* PC = Cast<APlayerController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));
            if (PC)
            {
                // Implementar efeito de post-process
            }
            
            UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Aplicando efeito ao jogador %s com intensidade %.2f"), 
                   *Player->GetName(), EffectIntensity);
        }
    }
}

void AAURACRONPCGChaosPortal::ProcessFadeOut(float DeltaTime)
{
    // Atualizar tempo de fade out
    FadeOutElapsed += DeltaTime;
    
    // Calcular alpha de fade out (1.0 -> 0.0)
    float Alpha = 1.0f - FMath::Clamp(FadeOutElapsed / FadeOutTime, 0.0f, 1.0f);
    
    // Atualizar material dinâmico
    if (PortalDynamicMaterial)
    {
        PortalDynamicMaterial->SetScalarParameterValue(FName("Opacity"), Alpha);
    }
    
    // Atualizar efeito de partículas
    if (PortalEffect)
    {
        PortalEffect->SetFloatParameter(FName("Intensity"), PortalIntensity * Alpha);
    }
    
    // Atualizar luz
    if (PortalLight)
    {
        PortalLight->SetIntensity(8000.0f * PortalIntensity * Alpha * QualityScale);
    }
    
    // Verificar se o fade out terminou
    if (FadeOutElapsed >= FadeOutTime)
    {
        // Desativar portal
        bPortalActive = false;
        bFadingOut = false;
        
        // Desativar efeitos visuais
        PortalMesh->SetVisibility(false);
        PortalEffect->Deactivate();
        PortalLight->SetVisibility(false);
        PortalSound->Stop();
        
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosPortal: Portal desativado após fade out"));
    }
}

void AAURACRONPCGChaosPortal::OnPlayerEnterPortalRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                                      UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, 
                                                      bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se é um jogador
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!Character || !Character->IsPlayerControlled())
    {
        return;
    }
    
    // Aplicar efeito inicial ao jogador
    // Implementação específica depende do design do jogo
    
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Jogador %s entrou no raio do portal"), 
           *OtherActor->GetName());
}

void AAURACRONPCGChaosPortal::OnPlayerExitPortalRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                                     UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    // Verificar se é um jogador
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!Character || !Character->IsPlayerControlled())
    {
        return;
    }
    
    // Remover efeito do jogador
    // Implementação específica depende do design do jogo
    
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Jogador %s saiu do raio do portal"), 
           *OtherActor->GetName());
}