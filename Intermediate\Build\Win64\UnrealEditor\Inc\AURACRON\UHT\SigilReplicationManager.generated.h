// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Multiplayer/SigilReplicationManager.h"

#ifdef AURACRON_SigilReplicationManager_generated_h
#error "SigilReplicationManager.generated.h already included, missing '#pragma once' in SigilReplicationManager.h"
#endif
#define AURACRON_SigilReplicationManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ASigilItem;
class USigilManagerComponent;
struct FSigilFusionReplicationData;
struct FSigilReplicationData;
struct FSigilReplicationStats;

// ********** Begin ScriptStruct FSigilReplicationData *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_24_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilReplicationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilReplicationData;
// ********** End ScriptStruct FSigilReplicationData ***********************************************

// ********** Begin ScriptStruct FSigilReplicationStats ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_75_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilReplicationStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilReplicationStats;
// ********** End ScriptStruct FSigilReplicationStats **********************************************

// ********** Begin ScriptStruct FSigilFusionReplicationData ***************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_106_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilFusionReplicationData;
// ********** End ScriptStruct FSigilFusionReplicationData *****************************************

// ********** Begin ScriptStruct FSigilPlayerDataEntry *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_137_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializerItem Super;


struct FSigilPlayerDataEntry;
// ********** End ScriptStruct FSigilPlayerDataEntry ***********************************************

// ********** Begin ScriptStruct FSigilPlayerDataArray *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_164_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializer Super; \
	UE_NET_DECLARE_FASTARRAY(FSigilPlayerDataArray, Items, );


struct FSigilPlayerDataArray;
// ********** End ScriptStruct FSigilPlayerDataArray ***********************************************

// ********** Begin ScriptStruct FSigilPlayerStatsEntry ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_225_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializerItem Super;


struct FSigilPlayerStatsEntry;
// ********** End ScriptStruct FSigilPlayerStatsEntry **********************************************

// ********** Begin ScriptStruct FSigilPlayerStatsArray ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_252_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializer Super; \
	UE_NET_DECLARE_FASTARRAY(FSigilPlayerStatsArray, Items, );


struct FSigilPlayerStatsArray;
// ********** End ScriptStruct FSigilPlayerStatsArray **********************************************

// ********** Begin ScriptStruct FSigilActiveFusionsEntry ******************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_313_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializerItem Super;


struct FSigilActiveFusionsEntry;
// ********** End ScriptStruct FSigilActiveFusionsEntry ********************************************

// ********** Begin ScriptStruct FSigilActiveFusionsArray ******************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_349_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializer Super; \
	UE_NET_DECLARE_FASTARRAY(FSigilActiveFusionsArray, Items, );


struct FSigilActiveFusionsArray;
// ********** End ScriptStruct FSigilActiveFusionsArray ********************************************

// ********** Begin Delegate FOnSigilEquipped ******************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_395_DELEGATE \
AURACRON_API void FOnSigilEquipped_DelegateWrapper(const FMulticastScriptDelegate& OnSigilEquipped, int32 PlayerID, FSigilReplicationData const& SigilData);


// ********** End Delegate FOnSigilEquipped ********************************************************

// ********** Begin Delegate FOnSigilUnequipped ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_396_DELEGATE \
AURACRON_API void FOnSigilUnequipped_DelegateWrapper(const FMulticastScriptDelegate& OnSigilUnequipped, int32 PlayerID, int32 SlotIndex);


// ********** End Delegate FOnSigilUnequipped ******************************************************

// ********** Begin Delegate FOnSigilReplicationFusionStarted **************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_397_DELEGATE \
AURACRON_API void FOnSigilReplicationFusionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilReplicationFusionStarted, int32 PlayerID, FSigilFusionReplicationData const& FusionData);


// ********** End Delegate FOnSigilReplicationFusionStarted ****************************************

// ********** Begin Delegate FOnSigilReplicationFusionCompleted ************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_398_DELEGATE \
AURACRON_API void FOnSigilReplicationFusionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilReplicationFusionCompleted, int32 PlayerID, FSigilReplicationData const& NewSigilData);


// ********** End Delegate FOnSigilReplicationFusionCompleted **************************************

// ********** Begin Delegate FOnSigilSystemStatsUpdated ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_399_DELEGATE \
AURACRON_API void FOnSigilSystemStatsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnSigilSystemStatsUpdated, FSigilReplicationStats const& Stats);


// ********** End Delegate FOnSigilSystemStatsUpdated **********************************************

// ********** Begin Class USigilReplicationManager *************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_408_RPC_WRAPPERS_NO_PURE_DECLS \
	virtual void MulticastNotifyFusionComplete_Implementation(int32 PlayerID, FSigilReplicationData const& NewSigilData); \
	virtual void MulticastNotifyFusionStart_Implementation(int32 PlayerID, FSigilFusionReplicationData const& FusionData); \
	virtual void MulticastNotifyUnequip_Implementation(int32 PlayerID, int32 SlotIndex); \
	virtual void MulticastNotifyEquip_Implementation(int32 PlayerID, FSigilReplicationData const& SigilData); \
	virtual bool ServerReforge_Validate(int32 ); \
	virtual void ServerReforge_Implementation(int32 PlayerID); \
	virtual bool ServerForceFusion_Validate(int32 , int32 ); \
	virtual void ServerForceFusion_Implementation(int32 PlayerID, int32 SigilID); \
	virtual bool ServerStartFusion_Validate(int32 , int32 ); \
	virtual void ServerStartFusion_Implementation(int32 PlayerID, int32 SigilID); \
	virtual bool ServerUnequipSigil_Validate(int32 , int32 ); \
	virtual void ServerUnequipSigil_Implementation(int32 PlayerID, int32 SlotIndex); \
	virtual bool ServerEquipSigil_Validate(int32 , int32 , int32 ); \
	virtual void ServerEquipSigil_Implementation(int32 PlayerID, int32 SigilID, int32 SlotIndex); \
	DECLARE_FUNCTION(execOnRep_ActiveFusions); \
	DECLARE_FUNCTION(execOnRep_PlayerSystemStats); \
	DECLARE_FUNCTION(execOnRep_PlayerSigilData); \
	DECLARE_FUNCTION(execDebugForceFullReplication); \
	DECLARE_FUNCTION(execDebugSimulateNetworkLag); \
	DECLARE_FUNCTION(execDebugPrintReplicationStats); \
	DECLARE_FUNCTION(execMulticastNotifyFusionComplete); \
	DECLARE_FUNCTION(execMulticastNotifyFusionStart); \
	DECLARE_FUNCTION(execMulticastNotifyUnequip); \
	DECLARE_FUNCTION(execMulticastNotifyEquip); \
	DECLARE_FUNCTION(execServerReforge); \
	DECLARE_FUNCTION(execServerForceFusion); \
	DECLARE_FUNCTION(execServerStartFusion); \
	DECLARE_FUNCTION(execServerUnequipSigil); \
	DECLARE_FUNCTION(execServerEquipSigil); \
	DECLARE_FUNCTION(execEnableMOBAOptimizations); \
	DECLARE_FUNCTION(execOptimizeReplicationForDistance); \
	DECLARE_FUNCTION(execSetReplicationPriority); \
	DECLARE_FUNCTION(execGetRegisteredPlayers); \
	DECLARE_FUNCTION(execIsPlayerRegistered); \
	DECLARE_FUNCTION(execGetPlayerActiveFusions); \
	DECLARE_FUNCTION(execGetPlayerStats); \
	DECLARE_FUNCTION(execGetPlayerSigils); \
	DECLARE_FUNCTION(execUpdatePlayerStats); \
	DECLARE_FUNCTION(execReplicateFusionComplete); \
	DECLARE_FUNCTION(execReplicateFusionStart); \
	DECLARE_FUNCTION(execReplicateSigilUnequip); \
	DECLARE_FUNCTION(execReplicateSigilEquip); \
	DECLARE_FUNCTION(execUnregisterPlayer); \
	DECLARE_FUNCTION(execRegisterPlayer);


#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_408_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_USigilReplicationManager_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_408_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilReplicationManager(); \
	friend struct Z_Construct_UClass_USigilReplicationManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilReplicationManager_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilReplicationManager, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilReplicationManager_NoRegister) \
	DECLARE_SERIALIZER(USigilReplicationManager) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		PlayerSigilDataArray=NETFIELD_REP_START, \
		PlayerSystemStatsArray, \
		ActiveFusionsArray, \
		NETFIELD_REP_END=ActiveFusionsArray	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_408_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilReplicationManager(USigilReplicationManager&&) = delete; \
	USigilReplicationManager(const USigilReplicationManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilReplicationManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilReplicationManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilReplicationManager) \
	NO_API virtual ~USigilReplicationManager();


#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_405_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_408_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_408_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_408_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_408_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_408_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilReplicationManager;

// ********** End Class USigilReplicationManager ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
