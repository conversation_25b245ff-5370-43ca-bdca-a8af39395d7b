// AURACRONPCGArsenalIsland.cpp
// Implementação da classe AArsenalIsland para o sistema Prismal Flow

#include "PCG/AURACRONPCGArsenalIsland.h"
#include "GAS/AURACRONAttributeSet.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"

AArsenalIsland::AArsenalIsland()
{
    // Configuração padrão
    PrimaryActorTick.bCanEverTick = true;
    
    // Inicializar propriedades
    WeaponBonusDuration = 30.0f;
    AbilityBoostMultiplier = 1.5f;
    
    // Configurar componentes específicos da Arsenal Island
    
    // Plataforma de armas central
    WeaponPlatform = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("WeaponPlatform"));
    WeaponPlatform->SetupAttachment(RootComponent);
    WeaponPlatform->SetRelativeLocation(FVector(0.0f, 0.0f, 150.0f));
    WeaponPlatform->SetRelativeScale3D(FVector(2.0f, 2.0f, 0.5f));
    WeaponPlatform->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Efeito de energia da plataforma
    PlatformEnergyEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("PlatformEnergyEffect"));
    PlatformEnergyEffect->SetupAttachment(WeaponPlatform);
    PlatformEnergyEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f));
    
    // Depósitos de munição
    for (int32 i = 0; i < 4; ++i)
    {
        FString ComponentName = FString::Printf(TEXT("AmmoDeposit_%d"), i);
        UStaticMeshComponent* AmmoDeposit = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
        AmmoDeposit->SetupAttachment(RootComponent);
        
        // Posicionar em torno da plataforma central
        float Angle = (float)i / 4.0f * 2.0f * PI;
        float Distance = 200.0f;
        FVector Position;
        Position.X = Distance * FMath::Cos(Angle);
        Position.Y = Distance * FMath::Sin(Angle);
        Position.Z = 100.0f;
        
        AmmoDeposit->SetRelativeLocation(Position);
        AmmoDeposit->SetRelativeScale3D(FVector(0.8f, 0.8f, 1.2f));
        AmmoDeposit->SetCollisionProfileName(TEXT("BlockAll"));
        
        AmmoDeposits.Add(AmmoDeposit);
    }
    
    // Rampas táticas
    for (int32 i = 0; i < 2; ++i)
    {
        FString ComponentName = FString::Printf(TEXT("TacticalRamp_%d"), i);
        UStaticMeshComponent* TacticalRamp = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
        TacticalRamp->SetupAttachment(RootComponent);
        
        // Posicionar em lados opostos
        float Angle = (float)i / 2.0f * PI;
        float Distance = 300.0f;
        FVector Position;
        Position.X = Distance * FMath::Cos(Angle);
        Position.Y = Distance * FMath::Sin(Angle);
        Position.Z = 50.0f;
        
        TacticalRamp->SetRelativeLocation(Position);
        TacticalRamp->SetRelativeScale3D(FVector(3.0f, 1.0f, 0.5f));
        TacticalRamp->SetRelativeRotation(FRotator(30.0f, Angle * 180.0f / PI, 0.0f));
        TacticalRamp->SetCollisionProfileName(TEXT("BlockAll"));
        
        TacticalRamps.Add(TacticalRamp);
    }
    
    // Definir o tipo de ilha como Arsenal
    IslandType = EPrismalFlowIslandType::Arsenal;
}

void AArsenalIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Implementar rotação da plataforma de armas
    if (WeaponPlatform && bIsActive)
    {
        // Rotação lenta da plataforma
        FRotator CurrentRotation = WeaponPlatform->GetRelativeRotation();
        CurrentRotation.Yaw += DeltaTime * 10.0f; // 10 graus por segundo
        WeaponPlatform->SetRelativeRotation(CurrentRotation);
        
        // Pulsar efeitos de energia
        float Time = GetGameTimeSinceCreation();
        float PulseValue = 0.5f + 0.5f * FMath::Sin(Time * 2.0f);
        
        if (PlatformEnergyEffect)
        {
            PlatformEnergyEffect->SetFloatParameter(FName("Intensity"), PulseValue * 2.0f);
        }
    }
}

void AArsenalIsland::ApplyIslandEffect(AActor* OverlappingActor)
{
    // Verificar se a ilha está ativa
    if (!bIsActive || !OverlappingActor)
    {
        return;
    }
    
    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar efeito visual de feedback
    if (PlatformEnergyEffect)
    {
        PlatformEnergyEffect->SetFloatParameter(FName("EffectIntensity"), 3.0f); // Intensificar efeito
        
        // Retornar à intensidade normal após um curto período
        FTimerHandle TimerHandle;
        GetWorldTimerManager().SetTimer(TimerHandle, [this]()
        {
            if (PlatformEnergyEffect)
            {
                PlatformEnergyEffect->SetFloatParameter(FName("EffectIntensity"), 1.0f);
            }
        }, 0.5f, false);
    }
    
    // Conceder bônus de armas ao jogador
    GrantWeaponBonus(OverlappingActor);
}

void AArsenalIsland::GrantWeaponBonus(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito de bônus de armas
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para bônus de armas do Arsenal Island
    UGameplayEffect* WeaponBonusEffect = NewObject<UGameplayEffect>(this, FName(TEXT("GE_ArsenalIslandWeaponBonus")));
    if (WeaponBonusEffect)
    {
        // Configurar duração do efeito (45 segundos)
        WeaponBonusEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
        WeaponBonusEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(45.0f));
        
        // Configurar modificadores específicos do Arsenal Island
        FGameplayModifierInfo WeaponDamageModifier;
        WeaponDamageModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
        WeaponDamageModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(1.75f)); // +75% dano
        
        FGameplayModifierInfo WeaponRangeModifier;
        WeaponRangeModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
        WeaponRangeModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(1.5f)); // +50% alcance
        
        FGameplayModifierInfo WeaponSpeedModifier;
         WeaponSpeedModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
         WeaponSpeedModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(1.3f)); // +30% velocidade de ataque
         
         // Adicionar todos os modificadores ao efeito
         WeaponBonusEffect->Modifiers.Add(WeaponDamageModifier);
         WeaponBonusEffect->Modifiers.Add(WeaponRangeModifier);
         WeaponBonusEffect->Modifiers.Add(WeaponSpeedModifier);
         
         // Aplicar o efeito
         FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(WeaponBonusEffect->GetClass(), 1.0f, EffectContext);
         if (SpecHandle.IsValid())
         {
             AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
             UE_LOG(LogTemp, Display, TEXT("Arsenal Island: Aplicado bônus de armas específico (+75% dano, +50% alcance, +30% velocidade) para %s"), *TargetActor->GetName());
         }
        }
    }
    
    if (WeaponBonusEffectClass)
    {
        // Aplicar bônus de armas
        FGameplayEffectSpecHandle EffectSpec = AbilityComponent->MakeOutgoingSpec(WeaponBonusEffectClass, 1.0f, EffectContext);
        if (EffectSpec.IsValid())
        {
            FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
            
            if (ActiveEffect.IsValid())
            {
                UE_LOG(LogTemp, Log, TEXT("Arsenal Island: Bônus de armas concedido para %s"), *TargetActor->GetName());
            }
        }
    }
    
    // Criar feedback visual de bônus de armas
    UNiagaraSystem* WeaponBonusVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_ArsenalIslandWeaponBonus"));
    if (WeaponBonusVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            WeaponBonusVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

void AArsenalIsland::GrantSpecialAmmo(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        UE_LOG(LogTemp, Warning, TEXT("Arsenal Island: Ator %s não implementa AbilitySystemInterface para munição especial"), *TargetActor->GetName());
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("Arsenal Island: Ator %s não possui AbilitySystemComponent para munição especial"), *TargetActor->GetName());
        return;
    }
    
    // Aplicar efeito de munição especial
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para munição especial do Arsenal Island
    UGameplayEffect* SpecialAmmoEffect = NewObject<UGameplayEffect>(this, FName(TEXT("GE_ArsenalIslandSpecialAmmo")));
    if (SpecialAmmoEffect)
    {
        // Configurar duração do efeito (60 segundos)
        SpecialAmmoEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
        SpecialAmmoEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(60.0f));
        
        // Configurar modificadores específicos para munição especial
        FGameplayModifierInfo AmmoCapacityModifier;
        AmmoCapacityModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
        AmmoCapacityModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(2.0f)); // +100% capacidade
        
        FGameplayModifierInfo AmmoPenetrationModifier;
        AmmoPenetrationModifier.ModifierOp = EGameplayModOp::Additive;
        AmmoPenetrationModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(50.0f)); // +50 penetração
        
        FGameplayModifierInfo AmmoExplosiveModifier;
        AmmoExplosiveModifier.ModifierOp = EGameplayModOp::Additive;
        AmmoExplosiveModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(25.0f)); // +25 dano explosivo
        
        // Adicionar modificadores ao efeito
        SpecialAmmoEffect->Modifiers.Add(AmmoCapacityModifier);
        SpecialAmmoEffect->Modifiers.Add(AmmoPenetrationModifier);
        SpecialAmmoEffect->Modifiers.Add(AmmoExplosiveModifier);
        
        // Aplicar o efeito
        FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(SpecialAmmoEffect->GetClass(), 1.0f, EffectContext);
        if (SpecHandle.IsValid())
        {
            AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
            UE_LOG(LogTemp, Display, TEXT("Arsenal Island: Aplicada munição especial (+100% capacidade, +50 penetração, +25 explosivo) para %s"), *TargetActor->GetName());
            TempAmmoEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(45.0f)); // 45 segundos
            // Configurar modificador de munição especial (exemplo: munição infinita ou explosiva)
            FGameplayModifierInfo AmmoModifier;
            // Usar atributo de dano de ataque para representar munição especial
            AmmoModifier.Attribute = UAURACRONAttributeSet::GetAttackDamageAttribute();
            AmmoModifier.ModifierOp = EGameplayModOp::Override;
            AmmoModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(1.0f)); // Ativar munição especial
            TempAmmoEffect->Modifiers.Add(AmmoModifier);
            SpecialAmmoEffectClass = TempAmmoEffect->GetClass();
        }
    }
    
    // Aplicar o efeito de munição especial
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(SpecialAmmoEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOG(LogTemp, Log, TEXT("Arsenal Island: Munição especial concedida para %s"), *TargetActor->GetName());
        }
    }
    
    // Criar feedback visual de munição especial
    UNiagaraSystem* SpecialAmmoVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_ArsenalIslandSpecialAmmo"));
    if (SpecialAmmoVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            SpecialAmmoVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(0.8f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

void AArsenalIsland::GrantAbilityBoost(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito de potencialização de habilidades
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para potencialização de habilidades
    UGameplayEffect* AbilityBoostEffect = NewObject<UGameplayEffect>(this, FName("GE_ArsenalIslandAbilityBoost"));
    AbilityBoostEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
    AbilityBoostEffect->DurationMagnitude = FScalableFloat(60.0f); // 60 segundos
    
    // Modificador para redução de cooldown (-50%)
    FGameplayModifierInfo CooldownModifier;
    CooldownModifier.ModifierMagnitude = FScalableFloat(0.5f);
    CooldownModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
    // Usar atributo de redução de cooldown
    CooldownModifier.Attribute = UAURACRONAttributeSet::GetCooldownReductionAttribute();
    AbilityBoostEffect->Modifiers.Add(CooldownModifier);
    
    // Modificador para aumento de poder de habilidades (+25%)
    FGameplayModifierInfo PowerModifier;
    PowerModifier.ModifierMagnitude = FScalableFloat(1.25f);
    PowerModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
    // Usar atributo de poder de habilidade
    PowerModifier.Attribute = UAURACRONAttributeSet::GetAbilityPowerAttribute();
    AbilityBoostEffect->Modifiers.Add(PowerModifier);
    
    // Aplicar o efeito de potencialização
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(AbilityBoostEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOG(LogTemp, Log, TEXT("Arsenal Island: Potencialização de habilidades concedida para %s (Cooldown -50%, Poder +25%)"), *TargetActor->GetName());
        }
    }
    
    // Criar feedback visual de potencialização
    UNiagaraSystem* AbilityBoostVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_ArsenalIslandAbilityBoost"));
    if (AbilityBoostVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            AbilityBoostVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.1f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}