// AURACRONPCGPhaseManager.h
// Sistema de Gerenciamento das 4 Fases Evolutivas do Mapa AURACRON - UE 5.6
// Awakening → Convergence → Intensification → Resolution
// CONSOLIDADO: Combina funcionalidades de AURACRONMapPhaseManager

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGLaneSystem.h"
#include "PCG/AURACRONPCGJungleSystem.h"
#include "PCG/AURACRONPCGObjectiveSystem.h"
#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "PCG/AURACRONPCGUtility.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "AURACRONPCGPhaseManager.generated.h"

// Forward declarations
class AAURACRONPCGEnvironment;
class AAURACRONPCGTrail;
class AAURACRONPCGIsland;
class AAURACRONPCGPrismalFlow;

/**
 * Configurações consolidadas de uma fase do mapa
 * CONSOLIDADO: Combina FAURACRONMapPhaseConfig e FAURACRONPhaseSettings
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPhaseSettings
{
    GENERATED_BODY()

    /** Fase do mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONMapPhase Phase;

    /** Nome da fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString PhaseName;

    /** Descrição da fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString Description;

    /** Duração da fase em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "30.0", ClampMax = "600.0"))
    float PhaseDuration;

    /** Tempo de início da fase (desde o início da partida) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float PhaseStartTime;

    /** Fator de escala do mapa (1.0 = tamanho normal, 0.8 = 20% menor) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float MapScaleFactor;

    /** Multiplicador de velocidade dos ambientes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float EnvironmentSpeedMultiplier;

    /** Intensidade global da fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float PhaseIntensity;

    /** Escala de atividade dos ambientes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float EnvironmentActivityScale;

    /** Velocidade das transições */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float TransitionSpeed;

    /** Cor dominante da fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor PhaseColor;
    
    /** Multiplicador de respawn dos jungle camps */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float JungleRespawnMultiplier;

    /** Multiplicador de respawn dos objetivos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float ObjectiveRespawnMultiplier;

    /** Multiplicador de HP dos objetivos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float ObjectiveHealthMultiplier;

    /** Multiplicador de recompensas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float RewardMultiplier;

    /** Efeitos especiais da fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> SpecialEffects;

    /** Eventos únicos da fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> UniqueEvents;

    /** Efeitos especiais ativos nesta fase (do MapPhaseManager) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> ActiveEffects;

    /** Se objetivos especiais estão ativos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bSpecialObjectivesActive;

    /** Se Chaos Islands estão ativas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bChaosIslandsActive;

    /** Configurações para dispositivos de entrada (baixo desempenho) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsEntryDevice;
    
    /** Trilhos a 50% de poder na Fase 1 */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float TrailPowerPercentage;
    
    /** Todas as ilhas totalmente emergidas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bAllIslandsFullyEmerged;
    
    /** Deformação de terreno opcional */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bTerrainDeformationEnabled;
    
    FAURACRONPhaseSettings()
        : Phase(EAURACRONMapPhase::Awakening)
        , PhaseName(TEXT("Awakening"))
        , Description(TEXT("Fase inicial do mapa"))
        , PhaseDuration(900.0f) // 15 minutos (0-15 minutos)
        , PhaseStartTime(0.0f)
        , MapScaleFactor(1.0f)
        , EnvironmentSpeedMultiplier(1.0f)
        , PhaseIntensity(1.0f)
        , EnvironmentActivityScale(1.0f)
        , TransitionSpeed(1.0f)
        , PhaseColor(FLinearColor::White)
        , JungleRespawnMultiplier(1.0f)
        , ObjectiveRespawnMultiplier(1.0f)
        , ObjectiveHealthMultiplier(1.0f)
        , RewardMultiplier(1.0f)
        , bSpecialObjectivesActive(false)
        , bChaosIslandsActive(false)
        , bIsEntryDevice(false)
        , TrailPowerPercentage(0.5f) // 50% de poder
        , bAllIslandsFullyEmerged(true)
        , bTerrainDeformationEnabled(false)
    {
    }
};

/**
 * Delegates para notificação de mudanças de fase
 * CONSOLIDADO: Combina delegates de ambos os sistemas
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMapPhaseChanged, EAURACRONMapPhase, OldPhase, EAURACRONMapPhase, NewPhase);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPhaseTransitionProgress, EAURACRONMapPhase, CurrentPhase, float, TransitionProgress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMapContraction, float, ContractionFactor, float, ContractedRadius);

/**
 * Gerenciador consolidado das 4 fases evolutivas do mapa AURACRON
 * CONSOLIDADO: Combina AURACRONPCGPhaseManager e AURACRONMapPhaseManager
 * Controla progressão automática, transições suaves e efeitos de cada fase
 */
UCLASS()
class AURACRON_API AAURACRONPCGPhaseManager : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGPhaseManager();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    // ========================================
    // FUNÇÕES PÚBLICAS CONSOLIDADAS
    // ========================================

    /** Inicializar sistema de fases */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void InitializePhaseSystem();
    
    /** Configurar o sistema para dispositivo de entrada (baixo desempenho) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void ConfigureForEntryDevice(bool bIsEntryDevice);

    /** Iniciar progressão automática das fases */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void StartPhaseProgression();

    /** Iniciar o ciclo de fases do mapa (do MapPhaseManager) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void StartMapPhases();

    /** Parar progressão automática */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void StopPhaseProgression();

    /** Forçar transição para uma fase específica */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void ForcePhaseTransition(EAURACRONMapPhase TargetPhase);

    /** Pausar/Despausar o sistema de fases */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void SetPhaseSystemPaused(bool bPaused);
    
    /** Forçar transição para fase específica */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void ForceTransitionToPhase(EAURACRONMapPhase TargetPhase);
    
    /** Obter fase atualmente ativa */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PhaseManager")
    EAURACRONMapPhase GetCurrentPhase() const { return CurrentPhase; }
    
    /** Obter próxima fase na progressão */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PhaseManager")
    EAURACRONMapPhase GetNextPhase() const;
    
    /** Obter tempo restante na fase atual */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PhaseManager")
    float GetTimeRemainingInCurrentPhase() const;
    
    /** Obter tempo total decorrido desde o início */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PhaseManager")
    float GetTotalElapsedTime() const { return TotalElapsedTime; }
    
    /** Obter configurações de uma fase */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PhaseManager")
    FAURACRONPhaseSettings GetPhaseSettings(EAURACRONMapPhase Phase) const;
    
    /** Verificar se está em transição */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PhaseManager")
    bool IsInTransition() const { return bIsInTransition; }
    
    /** Obter progresso da transição atual (0.0 - 1.0) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PhaseManager")
    float GetTransitionProgress() const { return TransitionProgress; }
    
    /** Aplicar efeitos especiais temporários */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void ApplyTemporaryPhaseEffect(const FString& EffectName, float Duration);
    
    /** Aplicar configurações específicas da Fase 1: Despertar */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void ApplyAwakeningPhaseSettings();
    
    /** Integrar todos os sistemas para Fase 1: Despertar */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void IntegrateSystemsForAwakeningPhase(bool bIsEntryDevice);
    
    /** Aplicar configurações específicas da Fase 2: Convergência */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void ApplyConvergencePhaseSettings();
    
    /** Integrar todos os sistemas para Fase 2: Convergência */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void IntegrateSystemsForConvergencePhase(bool bIsEntryDevice);
    
    /** Configurar visibilidade dos ambientes com base no tipo de dispositivo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void ConfigureEnvironmentsForDeviceType(bool bIsEntryDevice);
    
    /** Verificar conformidade com a documentação */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    bool VerifyDocumentationCompliance(EAURACRONMapPhase Phase, bool bIsEntryDevice);
    
    /** Corrigir problemas de conformidade com a documentação */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void FixDocumentationComplianceIssues(EAURACRONMapPhase Phase, bool bIsEntryDevice);
    
    /** Acelerar progressão das fases (para testes) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PhaseManager")
    void AcceleratePhaseProgression(float SpeedMultiplier);

    // ========================================
    // EVENTOS CONSOLIDADOS
    // ========================================

    /** Evento disparado quando a fase muda */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON|PhaseManager")
    FOnMapPhaseChanged OnMapPhaseChanged;

    /** Evento disparado durante progresso de transição */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON|PhaseManager")
    FOnPhaseTransitionProgress OnPhaseTransitionProgress;
    
    /** Evento disparado quando ocorre contração do mapa */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON|PhaseManager")
    FOnMapContraction OnMapContraction;

protected:
    // ========================================
    // CONFIGURAÇÕES
    // ========================================
    
    /** Configurações das 4 fases */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PhaseManager")
    TMap<EAURACRONMapPhase, FAURACRONPhaseSettings> PhaseSettings;
    
    /** Validação de integração entre sistemas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PhaseManager")
    bool bValidateSystemIntegration;

    /** Configurações de fase (compatibilidade com MapPhaseManager) */
    UPROPERTY()
    TMap<EAURACRONMapPhase, FAURACRONPhaseSettings> PhaseConfigurations;
    
    /** Referências aos sistemas integrados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PhaseManager")
    AAURACRONPCGLaneSystem* LaneSystem;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PhaseManager")
    AAURACRONPCGJungleSystem* JungleSystem;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PhaseManager")
    AAURACRONPCGObjectiveSystem* ObjectiveSystem;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PhaseManager")
    AAURACRONPCGEnvironmentManager* EnvironmentManager;
    
    /** Se deve iniciar progressão automaticamente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PhaseManager")
    bool bAutoStartProgression;
    
    /** Se a progressão está ativa */
    UPROPERTY(BlueprintReadOnly, Category = "AURACRON|PhaseManager")
    bool bProgressionActive;

private:
    // ========================================
    // ESTADO INTERNO
    // ========================================
    
    /** Fase atualmente ativa */
    UPROPERTY()
    EAURACRONMapPhase CurrentPhase;
    
    /** Fase de destino durante transição */
    UPROPERTY()
    EAURACRONMapPhase TargetPhase;

    /** Próxima fase (compatibilidade com MapPhaseManager) */
    UPROPERTY()
    EAURACRONMapPhase NextPhase;

    /** Se está em transição */
    UPROPERTY()
    bool bIsInTransition;
    
    /** Tempo total decorrido desde o início */
    UPROPERTY()
    float TotalElapsedTime;
    
    /** Tempo restante na fase atual */
    UPROPERTY()
    float TimeRemainingInPhase;
    
    /** Duração da transição atual */
    UPROPERTY()
    float CurrentTransitionDuration;
    
    /** Progresso da transição atual (0.0 - 1.0) */
    UPROPERTY()
    float TransitionProgress;
    
    /** Multiplicador de velocidade da progressão */
    UPROPERTY()
    float ProgressionSpeedMultiplier;
    
    /** Timer para progressão de fases */
    UPROPERTY()
    FTimerHandle PhaseProgressionTimer;
    
    /** Efeitos temporários ativos */
    UPROPERTY()
    TMap<FString, float> ActiveTemporaryEffects;

    // ========================================
    // PROPRIEDADES CONSOLIDADAS DO MAPPHASEMANAGER
    // ========================================

    /** Tempo da fase atual (compatibilidade) */
    UPROPERTY()
    float CurrentPhaseTime;

    /** Se o sistema está pausado */
    UPROPERTY()
    bool bIsPaused;

    /** Timer de fase (compatibilidade) */
    UPROPERTY()
    FTimerHandle PhaseTimer;

    /** Duração da transição */
    UPROPERTY()
    float TransitionDuration;

    /** Referências aos atores PCG (usando utility class) */
    UPROPERTY()
    FAURACRONPCGActorReferences PCGActorReferences;

    // ========================================
    // FUNÇÕES INTERNAS
    // ========================================
    
    /** Inicializar configurações das fases */
    void InitializePhaseSettings();
    
    /** Spawnar pulsos de energia */
    void SpawnEnergyPulses(float Intensity);
    
    /** Spawnar portais de caos */
    void SpawnChaosPortals(float Intensity);

    /** Inicializar configurações padrão das fases (compatibilidade) */
    void InitializePhaseConfigurations();

    /** Configurar fase específica */
    void SetupPhase(EAURACRONMapPhase Phase);

    /** Encontrar todos os atores PCG na cena (usando utility class) */
    void FindPCGActors();

    /** Iniciar transição para próxima fase */
    void StartTransitionToNextPhase();

    /** Executar transição para próxima fase (compatibilidade) */
    void ExecutePhaseTransition();

    /** Executar transição suave */
    void ExecuteTransition();

    /** Atualizar transição suave (compatibilidade) */
    void UpdateTransition(float DeltaTime);

    /** Finalizar transição */
    void CompleteTransition();

    /** Aplicar efeitos da fase */
    void ApplyPhaseEffects(EAURACRONMapPhase Phase);

    /** Aplicar efeitos da fase aos sistemas PCG (compatibilidade) */
    void ApplyPhaseEffects(EAURACRONMapPhase Phase, float Intensity);

    /** Notificar sistemas integrados sobre mudança de fase */
    void NotifySystemsOfPhaseChange(EAURACRONMapPhase NewPhase);

    /** Notificar mudança de fase (compatibilidade) */
    void NotifyPhaseChange(EAURACRONMapPhase OldPhase, EAURACRONMapPhase NewPhase);

    /** Obter próxima fase na sequência */
    EAURACRONMapPhase GetNextPhaseInSequence(EAURACRONMapPhase Current) const;

    /** Calcular próxima fase na sequência (compatibilidade) */
    EAURACRONMapPhase CalculateNextPhase(EAURACRONMapPhase CurrentPhase);

    /** Aplicar contração do mapa (fase Resolution) */
    void ApplyMapContraction(float ContractionFactor);
    
    /** Configurar dano para jogadores fora da zona segura */
    void SetupOutOfBoundsDamage(float SafeRadius);
    
    /** Atualizar sistema de navegação para refletir a nova área jogável */
    void UpdateNavigationSystem(float NewRadius);
    
    /** Notificar sistemas relacionados sobre a contração do mapa */
    void NotifySystemsOfMapContraction(float ContractionFactor);

    /** Atualizar efeitos temporários */
    void UpdateTemporaryEffects(float DeltaTime);

    /** Aplicar configuração de fase específica (compatibilidade) */
    void ApplyPhaseConfiguration(const FAURACRONPhaseSettings& Config, float BlendFactor);

    /** Atualizar efeitos visuais globais (compatibilidade) */
    void UpdateGlobalVisualEffects(EAURACRONMapPhase Phase, float Intensity);

    /** Callback para timer de fase (compatibilidade) */
    UFUNCTION()
    void OnPhaseTimerExpired();

    /** Validar configurações de fase (compatibilidade) */
    bool ValidatePhaseConfiguration(const FAURACRONPhaseSettings& Config);

    /** Interpolar entre configurações de fases */
    FAURACRONPhaseSettings InterpolatePhaseSettings(
        const FAURACRONPhaseSettings& From,
        const FAURACRONPhaseSettings& To,
        float Alpha
    ) const;
    
    /** Ativar eventos únicos da fase */
    void ActivatePhaseUniqueEvents(EAURACRONMapPhase Phase);
    
    /** Calcular tempo de início da fase baseado na duração */
    void CalculatePhaseStartTimes();
};
