// SigilFusionSystem.cpp
#include "Fusion/SigilFusionSystem.h"
#include "Sigils/SigilItem.h"
#include "VFX/SigilVFXManager.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Blueprint/UserWidget.h"
#include "Engine/Engine.h"
#include "GameplayTagsManager.h"
#include "Kismet/GameplayStatics.h"
#include "Math/UnrealMathUtility.h"

// Constantes do sistema
const float USigilFusionSystem::DEFAULT_FUSION_TIME = 360.0f; // 6 minutos
const int32 USigilFusionSystem::MAX_FUSION_INSTANCES = 5;
const float USigilFusionSystem::FUSION_PROGRESS_UPDATE_RATE = 0.1f; // 10 vezes por segundo
const float USigilFusionSystem::NOTIFICATION_DISPLAY_TIME = 5.0f;

USigilFusionSystem::USigilFusionSystem()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = FUSION_PROGRESS_UPDATE_RATE;
    
    // Configurações padrão
    bEnableAutomaticFusion = true;
    DefaultFusionTime = DEFAULT_FUSION_TIME;
    MaxSimultaneousFusions = MAX_FUSION_INSTANCES;
    bShowNotifications = true;
    bPlaySounds = true;
    bShowVFX = true;
    
    // Configurações de depuração
    bDebugMode = false;
    bLogFusionEvents = false;
    
    // Inicializar arrays
    ActiveFusions.Empty();
    FusionRecipes.Empty();
    ActiveNotificationWidgets.Empty();
}

void USigilFusionSystem::BeginPlay()
{
    Super::BeginPlay();
    
    // Carregar receitas padrão
    LoadDefaultRecipes();
    
    // Encontrar VFXManager se não foi definido
    if (!VFXManager)
    {
        VFXManager = GetOwner()->FindComponentByClass<USigilVFXManager>();
    }
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Sistema iniciado para %s"), *GetOwner()->GetName());
    }
}

void USigilFusionSystem::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    if (bEnableAutomaticFusion)
    {
        UpdateFusionProgress(DeltaTime);
    }
}

// Funções principais de fusão
bool USigilFusionSystem::StartAutomaticFusion(const TArray<ASigilItem*>& InputSigils, AActor* Owner)
{
    if (!bEnableAutomaticFusion)
    {
        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Fusão automática está desabilitada"));
        }
        return false;
    }
    
    if (!CanStartFusion(InputSigils))
    {
        return false;
    }
    
    // Encontrar a melhor receita
    FSigilFusionRecipe BestRecipe = FindBestRecipe(InputSigils);
    if (BestRecipe.RecipeID.IsValid())
    {
        return StartManualFusion(BestRecipe, InputSigils, Owner);
    }
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Nenhuma receita encontrada para os sígilos fornecidos"));
    }
    
    return false;
}

bool USigilFusionSystem::StartManualFusion(const FSigilFusionRecipe& Recipe, const TArray<ASigilItem*>& InputSigils, AActor* Owner)
{
    if (!CanStartFusion(InputSigils))
    {
        return false;
    }
    
    if (!ValidateFusionInputs(InputSigils, Recipe))
    {
        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Entradas de fusão inválidas"));
        }
        return false;
    }
    
    // Verificar limite de fusões simultâneas
    if (GetActiveFusionCount(Owner) >= MaxSimultaneousFusions)
    {
        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Limite de fusões simultâneas atingido"));
        }
        return false;
    }
    
    // Criar nova instância de fusão
    FSigilFusionInstance NewFusion;
    NewFusion.FusionID = FGuid::NewGuid();
    NewFusion.Recipe = Recipe;
    NewFusion.InputSigils = InputSigils;
    NewFusion.CurrentState = ESigilFusionState::Preparing;
    NewFusion.StartTime = GetWorld()->GetTimeSeconds();
    NewFusion.RemainingTime = Recipe.FusionTime;
    NewFusion.OwnerActor = Owner;
    NewFusion.bIsAutomatic = true;
    
    // Adicionar à lista de fusões ativas
    ActiveFusions.Add(NewFusion);
    
    // Configurar timer para conclusão
    FTimerHandle TimerHandle;
    FTimerDelegate TimerDelegate;
    TimerDelegate.BindUFunction(this, FName("OnFusionTimerComplete"), NewFusion.FusionID);
    
    GetWorld()->GetTimerManager().SetTimer(
        TimerHandle,
        TimerDelegate,
        Recipe.FusionTime,
        false
    );
    
    FusionTimers.Add(NewFusion.FusionID, TimerHandle);
    
    // Consumir sígilos de entrada
    ConsumeFusionInputs(InputSigils);
    
    // Atualizar estado para em progresso
    for (FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.FusionID == NewFusion.FusionID)
        {
            Fusion.CurrentState = ESigilFusionState::InProgress;
            break;
        }
    }
    
    // Reproduzir efeitos
    PlayFusionVFX(NewFusion, ESigilFusionState::InProgress);
    PlayFusionSound(ESigilFusionState::InProgress);
    
    // Mostrar notificação
    if (bShowNotifications)
    {
        FSigilFusionNotification Notification = CreateNotificationForState(ESigilFusionState::InProgress, NewFusion);
        ShowFusionNotification(Notification, Owner);
    }
    
    // Disparar evento
    OnFusionStarted.Broadcast(NewFusion, Owner);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Fusão iniciada - ID: %s, Tempo: %.1f segundos"), 
               *NewFusion.FusionID.ToString(), Recipe.FusionTime);
    }
    
    return true;
}

bool USigilFusionSystem::CancelFusion(const FGuid& FusionID)
{
    for (int32 i = ActiveFusions.Num() - 1; i >= 0; i--)
    {
        if (ActiveFusions[i].FusionID == FusionID)
        {
            FSigilFusionInstance& Fusion = ActiveFusions[i];
            
            // Limpar timer
            ClearFusionTimer(FusionID);
            
            // Atualizar estado
            Fusion.CurrentState = ESigilFusionState::Cancelled;
            
            // Reproduzir efeitos de cancelamento
            PlayFusionVFX(Fusion, ESigilFusionState::Cancelled);
            
            // Mostrar notificação
            if (bShowNotifications)
            {
                FSigilFusionNotification Notification = CreateNotificationForState(ESigilFusionState::Cancelled, Fusion);
                ShowFusionNotification(Notification, Fusion.OwnerActor);
            }
            
            // Disparar evento
            OnFusionCancelled.Broadcast(Fusion, Fusion.OwnerActor);
            
            // Remover da lista
            ActiveFusions.RemoveAt(i);
            
            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Fusão cancelada - ID: %s"), *FusionID.ToString());
            }
            
            return true;
        }
    }
    
    return false;
}

void USigilFusionSystem::CancelAllFusions(AActor* Owner)
{
    TArray<FGuid> FusionsToCancel;
    
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (!Owner || Fusion.OwnerActor == Owner)
        {
            FusionsToCancel.Add(Fusion.FusionID);
        }
    }
    
    for (const FGuid& FusionID : FusionsToCancel)
    {
        CancelFusion(FusionID);
    }
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: %d fusões canceladas"), FusionsToCancel.Num());
    }
}

// Funções de consulta
bool USigilFusionSystem::CanStartFusion(const TArray<ASigilItem*>& InputSigils) const
{
    if (InputSigils.Num() < 2)
    {
        return false;
    }
    
    // Verificar se todos os sígilos são válidos
    for (ASigilItem* Sigil : InputSigils)
    {
        if (!IsValid(Sigil))
        {
            return false;
        }
    }
    
    // Verificar se existe uma receita válida
    FSigilFusionRecipe Recipe = FindBestRecipe(InputSigils);
    return Recipe.RecipeID.IsValid();
}

FSigilFusionRecipe USigilFusionSystem::FindBestRecipe(const TArray<ASigilItem*>& InputSigils) const
{
    FSigilFusionRecipe BestRecipe;
    float BestScore = 0.0f;
    
    for (const FSigilFusionRecipe& Recipe : FusionRecipes)
    {
        if (Recipe.RequiredSigilTypes.Num() != InputSigils.Num())
        {
            continue;
        }
        
        // Verificar se os tipos de sígilos correspondem
        TArray<FGameplayTag> InputTypes;
        for (ASigilItem* Sigil : InputSigils)
        {
            if (IsValid(Sigil))
            {
                // Convert ESigilType to FGameplayTag
                ESigilType SigilType = Sigil->GetSigilType();
                FGameplayTag TypeTag;
                
                switch (SigilType)
                {
                    case ESigilType::Tank:
                        TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank"));
                        break;
                    case ESigilType::Damage:
                        TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage"));
                        break;
                    case ESigilType::Utility:
                        TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility"));
                        break;
                    default:
                        TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.None"));
                        break;
                }
                
                InputTypes.Add(TypeTag);
            }
        }
        
        bool bMatches = true;
        for (const FGameplayTag& RequiredType : Recipe.RequiredSigilTypes)
        {
            if (!InputTypes.Contains(RequiredType))
            {
                bMatches = false;
                break;
            }
        }
        
        if (bMatches)
        {
            float Score = Recipe.SuccessRate;
            if (Score > BestScore)
            {
                BestScore = Score;
                BestRecipe = Recipe;
            }
        }
    }
    
    return BestRecipe;
}

TArray<FSigilFusionInstance> USigilFusionSystem::GetActiveFusions(AActor* Owner) const
{
    if (!Owner)
    {
        return ActiveFusions;
    }
    
    TArray<FSigilFusionInstance> OwnerFusions;
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.OwnerActor == Owner)
        {
            OwnerFusions.Add(Fusion);
        }
    }
    
    return OwnerFusions;
}

FSigilFusionInstance USigilFusionSystem::GetFusionByID(const FGuid& FusionID) const
{
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.FusionID == FusionID)
        {
            return Fusion;
        }
    }
    
    return FSigilFusionInstance();
}

float USigilFusionSystem::GetFusionProgress(const FGuid& FusionID) const
{
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.FusionID == FusionID)
        {
            float ElapsedTime = GetWorld()->GetTimeSeconds() - Fusion.StartTime;
            float TotalTime = Fusion.Recipe.FusionTime;
            return FMath::Clamp(ElapsedTime / TotalTime, 0.0f, 1.0f);
        }
    }
    
    return 0.0f;
}

int32 USigilFusionSystem::GetActiveFusionCount(AActor* Owner) const
{
    if (!Owner)
    {
        return ActiveFusions.Num();
    }
    
    int32 Count = 0;
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.OwnerActor == Owner)
        {
            Count++;
        }
    }
    
    return Count;
}

// Funções de receitas
void USigilFusionSystem::AddFusionRecipe(const FSigilFusionRecipe& Recipe)
{
    // Verificar se a receita já existe
    for (FSigilFusionRecipe& ExistingRecipe : FusionRecipes)
    {
        if (ExistingRecipe.RecipeID == Recipe.RecipeID)
        {
            ExistingRecipe = Recipe; // Atualizar receita existente
            return;
        }
    }
    
    FusionRecipes.Add(Recipe);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Receita adicionada - %s"), *Recipe.RecipeID.ToString());
    }
}

bool USigilFusionSystem::RemoveFusionRecipe(const FGameplayTag& RecipeID)
{
    for (int32 i = FusionRecipes.Num() - 1; i >= 0; i--)
    {
        if (FusionRecipes[i].RecipeID == RecipeID)
        {
            FusionRecipes.RemoveAt(i);
            
            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Receita removida - %s"), *RecipeID.ToString());
            }
            
            return true;
        }
    }
    
    return false;
}

void USigilFusionSystem::LoadDefaultRecipes()
{
    FusionRecipes.Empty();
    
    // Receita básica: Dois sígilos comuns -> Sígilo raro
    FSigilFusionRecipe BasicRecipe;
    BasicRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.Basic"));
    BasicRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Common")));
    BasicRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Common")));
    BasicRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Rare"));
    BasicRecipe.FusionType = ESigilFusionType::Basic;
    BasicRecipe.SuccessRate = 0.8f;
    BasicRecipe.FusionTime = DEFAULT_FUSION_TIME;
    BasicRecipe.RequiredLevel = 1;
    AddFusionRecipe(BasicRecipe);
    
    // Receita avançada: Dois sígilos raros -> Sígilo épico
    FSigilFusionRecipe AdvancedRecipe;
    AdvancedRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.Advanced"));
    AdvancedRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Rare")));
    AdvancedRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Rare")));
    AdvancedRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Epic"));
    AdvancedRecipe.FusionType = ESigilFusionType::Advanced;
    AdvancedRecipe.SuccessRate = 0.6f;
    AdvancedRecipe.FusionTime = DEFAULT_FUSION_TIME * 1.5f;
    AdvancedRecipe.RequiredLevel = 5;
    AddFusionRecipe(AdvancedRecipe);
    
    // Receita lendária: Dois sígilos épicos -> Sígilo lendário
    FSigilFusionRecipe LegendaryRecipe;
    LegendaryRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.Legendary"));
    LegendaryRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Epic")));
    LegendaryRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Epic")));
    LegendaryRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary"));
    LegendaryRecipe.FusionType = ESigilFusionType::Legendary;
    LegendaryRecipe.SuccessRate = 0.4f;
    LegendaryRecipe.FusionTime = DEFAULT_FUSION_TIME * 2.0f;
    LegendaryRecipe.RequiredLevel = 10;
    AddFusionRecipe(LegendaryRecipe);
    
    // Receita espectral: Dois sígilos lendários -> Sígilo espectral
    FSigilFusionRecipe SpectralRecipe;
    SpectralRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.Spectral"));
    SpectralRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary")));
    SpectralRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary")));
    SpectralRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Spectral"));
    SpectralRecipe.FusionType = ESigilFusionType::Spectral;
    SpectralRecipe.SuccessRate = 0.2f;
    SpectralRecipe.FusionTime = DEFAULT_FUSION_TIME * 3.0f;
    SpectralRecipe.RequiredLevel = 15;
    AddFusionRecipe(SpectralRecipe);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: %d receitas padrão carregadas"), FusionRecipes.Num());
    }
}

TArray<FSigilFusionRecipe> USigilFusionSystem::GetAvailableRecipes(const TArray<ASigilItem*>& InputSigils) const
{
    TArray<FSigilFusionRecipe> AvailableRecipes;
    
    for (const FSigilFusionRecipe& Recipe : FusionRecipes)
    {
        if (ValidateFusionInputs(InputSigils, Recipe))
        {
            AvailableRecipes.Add(Recipe);
        }
    }
    
    return AvailableRecipes;
}

// Funções de notificação
void USigilFusionSystem::ShowFusionNotification(const FSigilFusionNotification& Notification, AActor* Owner)
{
    if (!bShowNotifications || !NotificationWidgetClass)
    {
        return;
    }
    
    // Criar widget de notificação
    CreateNotificationWidget(Notification);
    
    // Disparar evento de notificação
    OnFusionNotification.Broadcast(Notification, Owner);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Notificação mostrada - %s"), *Notification.Title.ToString());
    }
}

void USigilFusionSystem::HideAllNotifications()
{
    for (UUserWidget* Widget : ActiveNotificationWidgets)
    {
        if (IsValid(Widget))
        {
            Widget->RemoveFromParent();
        }
    }
    
    ActiveNotificationWidgets.Empty();
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Todas as notificações foram ocultadas"));
    }
}

// Funções de configuração
void USigilFusionSystem::SetAutomaticFusionEnabled(bool bEnabled)
{
    bEnableAutomaticFusion = bEnabled;
    
    if (!bEnabled)
    {
        // Cancelar todas as fusões automáticas ativas
        TArray<FGuid> AutomaticFusions;
        for (const FSigilFusionInstance& Fusion : ActiveFusions)
        {
            if (Fusion.bIsAutomatic)
            {
                AutomaticFusions.Add(Fusion.FusionID);
            }
        }
        
        for (const FGuid& FusionID : AutomaticFusions)
        {
            CancelFusion(FusionID);
        }
    }
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Fusão automática %s"), 
               bEnabled ? TEXT("habilitada") : TEXT("desabilitada"));
    }
}

void USigilFusionSystem::SetDefaultFusionTime(float NewTime)
{
    DefaultFusionTime = FMath::Max(NewTime, 1.0f);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Tempo padrão de fusão definido para %.1f segundos"), DefaultFusionTime);
    }
}

void USigilFusionSystem::SetMaxSimultaneousFusions(int32 NewMax)
{
    MaxSimultaneousFusions = FMath::Max(NewMax, 1);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Máximo de fusões simultâneas definido para %d"), MaxSimultaneousFusions);
    }
}

// Funções internas de fusão
void USigilFusionSystem::ProcessFusionCompletion(const FGuid& FusionID)
{
    for (int32 i = ActiveFusions.Num() - 1; i >= 0; i--)
    {
        if (ActiveFusions[i].FusionID == FusionID)
        {
            FSigilFusionInstance& Fusion = ActiveFusions[i];
            
            // Determinar resultado da fusão
            float RandomValue = FMath::RandRange(0.0f, 1.0f);
            ESigilFusionResult Result;
            
            if (RandomValue <= Fusion.Recipe.SuccessRate * 0.1f) // 10% da taxa de sucesso para crítico
            {
                Result = ESigilFusionResult::CriticalSuccess;
            }
            else if (RandomValue <= Fusion.Recipe.SuccessRate)
            {
                Result = ESigilFusionResult::Success;
            }
            else
            {
                Result = ESigilFusionResult::Failed;
            }
            
            // Atualizar estado
            Fusion.CurrentState = (Result != ESigilFusionResult::Failed) ? 
                ESigilFusionState::Completed : ESigilFusionState::Failed;
            
            // Criar sígilo resultado se bem-sucedido
            ASigilItem* ResultSigil = nullptr;
            if (Result != ESigilFusionResult::Failed)
            {
                ResultSigil = CreateResultSigil(Fusion.Recipe, Fusion.OwnerActor);
            }
            
            // Reproduzir efeitos
            PlayFusionVFX(Fusion, Fusion.CurrentState);
            PlayFusionSound(Fusion.CurrentState);
            
            // Mostrar notificação
            if (bShowNotifications)
            {
                FSigilFusionNotification Notification = CreateNotificationForState(Fusion.CurrentState, Fusion);
                ShowFusionNotification(Notification, Fusion.OwnerActor);
            }
            
            // Disparar evento
            OnFusionCompleted.Broadcast(Fusion, Result, ResultSigil);
            
            // Limpar timer
            ClearFusionTimer(FusionID);
            
            // Remover da lista
            ActiveFusions.RemoveAt(i);
            
            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Fusão concluída - ID: %s, Resultado: %d"), 
                       *FusionID.ToString(), (int32)Result);
            }
            
            break;
        }
    }
}

void USigilFusionSystem::UpdateFusionProgress(float DeltaTime)
{
    for (FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.CurrentState == ESigilFusionState::InProgress)
        {
            float Progress = GetFusionProgress(Fusion.FusionID);
            OnFusionProgress.Broadcast(Fusion, Progress);
        }
    }
}

ASigilItem* USigilFusionSystem::CreateResultSigil(const FSigilFusionRecipe& Recipe, AActor* Owner)
{
    if (!IsValid(Owner) || !GetWorld())
    {
        return nullptr;
    }
    
    // Criar novo sígilo baseado na receita
    FActorSpawnParameters SpawnParams;
    SpawnParams.Owner = Owner;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    
    ASigilItem* NewSigil = GetWorld()->SpawnActor<ASigilItem>(ASigilItem::StaticClass(), SpawnParams);
    
    if (IsValid(NewSigil))
    {
        // Configurar propriedades do novo sígilo
        // Convert FGameplayTag to ESigilType
        FGameplayTag ResultTag = Recipe.ResultSigilType;
        ESigilType ResultType = ESigilType::None;
        
        if (ResultTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank")))
        {
            ResultType = ESigilType::Tank;
        }
        else if (ResultTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage")))
        {
            ResultType = ESigilType::Damage;
        }
        else if (ResultTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility")))
        {
            ResultType = ESigilType::Utility;
        }
        
        NewSigil->SigilData.SigilType = ResultType;
        
        // Adicionar ao inventário do proprietário se possível
        // Isso seria implementado dependendo do sistema de inventário
        
        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Sígilo resultado criado - Tipo: %s"), 
                   *Recipe.ResultSigilType.ToString());
        }
    }
    
    return NewSigil;
}

bool USigilFusionSystem::ValidateFusionInputs(const TArray<ASigilItem*>& InputSigils, const FSigilFusionRecipe& Recipe) const
{
    if (InputSigils.Num() != Recipe.RequiredSigilTypes.Num())
    {
        return false;
    }
    
    // Verificar se todos os sígilos são válidos
    for (ASigilItem* Sigil : InputSigils)
    {
        if (!IsValid(Sigil))
        {
            return false;
        }
    }
    
    // Verificar tipos de sígilos
    TArray<FGameplayTag> InputTypes;
    for (ASigilItem* Sigil : InputSigils)
    {
        // Convert ESigilType to FGameplayTag
        ESigilType SigilType = Sigil->GetSigilType();
        FGameplayTag TypeTag;
        
        switch (SigilType)
        {
            case ESigilType::Tank:
                TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank"));
                break;
            case ESigilType::Damage:
                TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage"));
                break;
            case ESigilType::Utility:
                TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility"));
                break;
            default:
                TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.None"));
                break;
        }
        
        InputTypes.Add(TypeTag);
    }
    
    for (const FGameplayTag& RequiredType : Recipe.RequiredSigilTypes)
    {
        if (!InputTypes.Contains(RequiredType))
        {
            return false;
        }
    }
    
    return true;
}

void USigilFusionSystem::ConsumeFusionInputs(const TArray<ASigilItem*>& InputSigils)
{
    for (ASigilItem* Sigil : InputSigils)
    {
        if (IsValid(Sigil))
        {
            // Remover sígilo do inventário e destruir
            // Isso seria implementado dependendo do sistema de inventário
            Sigil->Destroy();
        }
    }
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: %d sígilos de entrada consumidos"), InputSigils.Num());
    }
}

// Funções de efeitos
void USigilFusionSystem::PlayFusionVFX(const FSigilFusionInstance& FusionInstance, ESigilFusionState State)
{
    if (!bShowVFX || !VFXManager)
    {
        return;
    }
    
    FVector Location = FusionInstance.OwnerActor ? FusionInstance.OwnerActor->GetActorLocation() : FVector::ZeroVector;
    
    switch (State)
    {
        case ESigilFusionState::InProgress:
            if (FusionInstance.InputSigils.Num() > 0 && IsValid(FusionInstance.InputSigils[0]))
            {
                VFXManager->PlaySigilFusionVFX(FusionInstance.InputSigils[0], FusionInstance.OwnerActor, false);
            }
            break;
        case ESigilFusionState::Completed:
            if (FusionInstance.InputSigils.Num() > 0 && IsValid(FusionInstance.InputSigils[0]))
            {
                VFXManager->PlaySigilFusionVFX(FusionInstance.InputSigils[0], FusionInstance.OwnerActor, true);
            }
            break;
        case ESigilFusionState::Failed:
            // VFX de falha seria implementado no VFXManager
            break;
        case ESigilFusionState::Cancelled:
            // VFX de cancelamento seria implementado no VFXManager
            break;
        default:
            break;
    }
}

void USigilFusionSystem::PlayFusionSound(ESigilFusionState State)
{
    if (!bPlaySounds)
    {
        return;
    }
    
    USoundBase* SoundToPlay = nullptr;
    
    switch (State)
    {
        case ESigilFusionState::InProgress:
            SoundToPlay = FusionStartSound;
            break;
        case ESigilFusionState::Completed:
            SoundToPlay = FusionCompleteSound;
            break;
        case ESigilFusionState::Failed:
        case ESigilFusionState::Cancelled:
            SoundToPlay = FusionFailSound;
            break;
        default:
            break;
    }
    
    if (SoundToPlay && GetWorld())
    {
        UGameplayStatics::PlaySound2D(GetWorld(), SoundToPlay);
    }
}

// Funções de notificação internas
FSigilFusionNotification USigilFusionSystem::CreateNotificationForState(ESigilFusionState State, const FSigilFusionInstance& FusionInstance) const
{
    FSigilFusionNotification Notification;
    Notification.FusionState = State;
    Notification.DisplayDuration = NOTIFICATION_DISPLAY_TIME;
    
    switch (State)
    {
        case ESigilFusionState::InProgress:
            Notification.Title = FText::FromString(TEXT("Fusão Iniciada"));
            Notification.Message = FText::FromString(TEXT("Fusão de sígilos em progresso..."));
            Notification.NotificationColor = FLinearColor::Yellow;
            break;
        case ESigilFusionState::Completed:
            Notification.Title = FText::FromString(TEXT("Fusão Concluída"));
            Notification.Message = FText::FromString(TEXT("Fusão de sígilos bem-sucedida!"));
            Notification.NotificationColor = FLinearColor::Green;
            break;
        case ESigilFusionState::Failed:
            Notification.Title = FText::FromString(TEXT("Fusão Falhou"));
            Notification.Message = FText::FromString(TEXT("A fusão de sígilos falhou."));
            Notification.NotificationColor = FLinearColor::Red;
            break;
        case ESigilFusionState::Cancelled:
            Notification.Title = FText::FromString(TEXT("Fusão Cancelada"));
            Notification.Message = FText::FromString(TEXT("A fusão de sígilos foi cancelada."));
            Notification.NotificationColor = FLinearColor::Gray;
            break;
        default:
            Notification.Title = FText::FromString(TEXT("Fusão"));
            Notification.Message = FText::FromString(TEXT("Estado de fusão desconhecido."));
            Notification.NotificationColor = FLinearColor::White;
            break;
    }
    
    return Notification;
}

void USigilFusionSystem::CreateNotificationWidget(const FSigilFusionNotification& Notification)
{
    if (!NotificationWidgetClass || !GetWorld())
    {
        return;
    }
    
    UUserWidget* NotificationWidget = CreateWidget<UUserWidget>(GetWorld(), NotificationWidgetClass);
    if (IsValid(NotificationWidget))
    {
        // Configurar propriedades do widget baseado na notificação
        // Isso seria implementado dependendo da estrutura do widget
        
        NotificationWidget->AddToViewport();
        ActiveNotificationWidgets.Add(NotificationWidget);
        
        // Configurar timer para remover a notificação
        FTimerHandle NotificationTimer;
        FTimerDelegate NotificationDelegate;
        NotificationDelegate.BindUFunction(this, FName("RemoveNotificationWidget"), NotificationWidget);
        
        GetWorld()->GetTimerManager().SetTimer(
            NotificationTimer,
            NotificationDelegate,
            Notification.DisplayDuration,
            false
        );
    }
}

void USigilFusionSystem::RemoveNotificationWidget(UUserWidget* Widget)
{
    if (IsValid(Widget))
    {
        Widget->RemoveFromParent();
        ActiveNotificationWidgets.Remove(Widget);
    }
}

// Funções de timer
void USigilFusionSystem::OnFusionTimerComplete(FGuid FusionID)
{
    ProcessFusionCompletion(FusionID);
}

void USigilFusionSystem::ClearFusionTimer(const FGuid& FusionID)
{
    if (FTimerHandle* TimerHandle = FusionTimers.Find(FusionID))
    {
        if (GetWorld() && GetWorld()->GetTimerManager().IsTimerActive(*TimerHandle))
        {
            GetWorld()->GetTimerManager().ClearTimer(*TimerHandle);
        }
        FusionTimers.Remove(FusionID);
    }
}

// Funções de depuração
void USigilFusionSystem::DebugPrintActiveFusions()
{
    UE_LOG(LogTemp, Warning, TEXT("=== FUSÕES ATIVAS ==="));
    UE_LOG(LogTemp, Warning, TEXT("Total: %d fusões"), ActiveFusions.Num());
    
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        float Progress = GetFusionProgress(Fusion.FusionID);
        UE_LOG(LogTemp, Warning, TEXT("ID: %s | Estado: %d | Progresso: %.1f%% | Proprietário: %s"),
               *Fusion.FusionID.ToString(),
               (int32)Fusion.CurrentState,
               Progress * 100.0f,
               Fusion.OwnerActor ? *Fusion.OwnerActor->GetName() : TEXT("Nenhum"));
    }
    
    UE_LOG(LogTemp, Warning, TEXT("====================="));
}

void USigilFusionSystem::DebugStartTestFusion()
{
    if (!GetOwner())
    {
        UE_LOG(LogTemp, Error, TEXT("SigilFusionSystem: Nenhum proprietário para teste de fusão"));
        return;
    }
    
    // Criar sígilos de teste
    TArray<ASigilItem*> TestSigils;
    
    for (int32 i = 0; i < 2; i++)
    {
        FActorSpawnParameters SpawnParams;
        SpawnParams.Owner = GetOwner();
        
        ASigilItem* TestSigil = GetWorld()->SpawnActor<ASigilItem>(ASigilItem::StaticClass(), SpawnParams);
        if (IsValid(TestSigil))
        {
            TestSigil->SigilData.SigilType = ESigilType::None;
            TestSigils.Add(TestSigil);
        }
    }
    
    if (TestSigils.Num() >= 2)
    {
        StartAutomaticFusion(TestSigils, GetOwner());
        UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Fusão de teste iniciada"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SigilFusionSystem: Falha ao criar sígilos de teste"));
    }
}

void USigilFusionSystem::DebugCancelAllFusions()
{
    int32 CancelledCount = ActiveFusions.Num();
    CancelAllFusions();
    
    UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: %d fusões canceladas via debug"), CancelledCount);
}

void USigilFusionSystem::SetDebugMode(bool bEnabled)
{
    bDebugMode = bEnabled;
    bLogFusionEvents = bEnabled;
    
    UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Modo de depuração %s"), 
           bEnabled ? TEXT("habilitado") : TEXT("desabilitado"));
}