// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Sígilos MOBA 5x5
// Arquivo: SigilNetworkConfig.h
// Descrição: Configurações de rede otimizadas para MOBA 5x5

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "GameplayTagContainer.h"
#include "SigilNetworkConfig.generated.h"

// Enum para tipos de otimização de rede
UENUM(BlueprintType)
enum class ESigilNetworkOptimization : uint8
{
    None            UMETA(DisplayName = "No Optimization"),
    Basic           UMETA(DisplayName = "Basic Optimization"),
    MOBA            UMETA(DisplayName = "MOBA Optimized"),
    Competitive     UMETA(DisplayName = "Competitive Mode"),
    Custom          UMETA(DisplayName = "Custom Settings")
};

// Estrutura para configurações de prioridade de replicação
USTRUCT(BlueprintType)
struct AURACRON_API FSigilReplicationPriority
{
    GENERATED_BODY()

    // Prioridade para sígilos equipados (0.0 - 10.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float EquippedSigilPriority = 5.0f;

    // Prioridade para fusões ativas (0.0 - 10.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float FusionPriority = 8.0f;

    // Prioridade para estatísticas do sistema (0.0 - 10.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float SystemStatsPriority = 3.0f;

    // Prioridade para efeitos visuais (0.0 - 10.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float VFXPriority = 2.0f;

    // Prioridade para jogadores da mesma equipe (0.0 - 10.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float TeammatePriority = 6.0f;

    // Prioridade para jogadores inimigos (0.0 - 10.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float EnemyPriority = 4.0f;

    FSigilReplicationPriority()
    {
        EquippedSigilPriority = 5.0f;
        FusionPriority = 8.0f;
        SystemStatsPriority = 3.0f;
        VFXPriority = 2.0f;
        TeammatePriority = 6.0f;
        EnemyPriority = 4.0f;
    }
};

// Estrutura para configurações de distância
USTRUCT(BlueprintType)
struct AURACRON_API FSigilDistanceConfig
{
    GENERATED_BODY()

    // Distância máxima para replicação completa (Unreal Units)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1000.0", ClampMax = "20000.0"))
    float MaxReplicationDistance = 5000.0f;

    // Distância para replicação de prioridade média (Unreal Units)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "500.0", ClampMax = "15000.0"))
    float MediumPriorityDistance = 3000.0f;

    // Distância para replicação de baixa prioridade (Unreal Units)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "100.0", ClampMax = "10000.0"))
    float LowPriorityDistance = 1500.0f;

    // Distância para culling completo (Unreal Units)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "5000.0", ClampMax = "50000.0"))
    float CullingDistance = 10000.0f;

    FSigilDistanceConfig()
    {
        MaxReplicationDistance = 5000.0f;
        MediumPriorityDistance = 3000.0f;
        LowPriorityDistance = 1500.0f;
        CullingDistance = 10000.0f;
    }
};

// Estrutura para configurações de frequência
USTRUCT(BlueprintType)
struct AURACRON_API FSigilFrequencyConfig
{
    GENERATED_BODY()

    // Frequência base de replicação (Hz)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1.0", ClampMax = "60.0"))
    float BaseReplicationFrequency = 20.0f;

    // Frequência para dados críticos (Hz)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "5.0", ClampMax = "120.0"))
    float CriticalDataFrequency = 60.0f;

    // Frequência para dados de baixa prioridade (Hz)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.1", ClampMax = "30.0"))
    float LowPriorityFrequency = 5.0f;

    // Intervalo de otimização automática (segundos)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.5", ClampMax = "10.0"))
    float OptimizationInterval = 1.0f;

    FSigilFrequencyConfig()
    {
        BaseReplicationFrequency = 20.0f;
        CriticalDataFrequency = 60.0f;
        LowPriorityFrequency = 5.0f;
        OptimizationInterval = 1.0f;
    }
};

// Estrutura para configurações de bandwidth
USTRUCT(BlueprintType)
struct AURACRON_API FSigilBandwidthConfig
{
    GENERATED_BODY()

    // Limite máximo de bandwidth por jogador (KB/s)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1.0", ClampMax = "1000.0"))
    float MaxBandwidthPerPlayer = 50.0f;

    // Limite de replicações por frame
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1", ClampMax = "200"))
    int32 MaxReplicationsPerFrame = 50;

    // Tamanho máximo de pacote (bytes)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "512", ClampMax = "8192"))
    int32 MaxPacketSize = 1400;

    // Usar compressão de dados
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUseCompression = true;

    // Usar delta compression
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUseDeltaCompression = true;

    FSigilBandwidthConfig()
    {
        MaxBandwidthPerPlayer = 50.0f;
        MaxReplicationsPerFrame = 50;
        MaxPacketSize = 1400;
        bUseCompression = true;
        bUseDeltaCompression = true;
    }
};

/**
 * Configurações de rede para o sistema de sígilos
 * Otimizado para ambiente MOBA 5x5 com 10 jogadores
 */
UCLASS(Config = Game, DefaultConfig, meta = (DisplayName = "Sigil Network Settings"))
class AURACRON_API USigilNetworkConfig : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    USigilNetworkConfig();

    // Configurações gerais
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "General")
    ESigilNetworkOptimization OptimizationType = ESigilNetworkOptimization::MOBA;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "General")
    int32 MaxPlayers = 10;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "General")
    bool bEnableNetworkOptimizations = true;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "General")
    bool bEnableDebugLogging = false;

    // Configurações de prioridade
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Priority")
    FSigilReplicationPriority ReplicationPriorities;

    // Configurações de distância
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Distance")
    FSigilDistanceConfig DistanceSettings;

    // Configurações de frequência
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Frequency")
    FSigilFrequencyConfig FrequencySettings;

    // Configurações de bandwidth
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Bandwidth")
    FSigilBandwidthConfig BandwidthSettings;

    // Configurações específicas para MOBA
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "MOBA")
    bool bPrioritizeTeammates = true;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "MOBA")
    bool bReduceEnemyDetails = true;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "MOBA")
    bool bOptimizeForObjectives = true;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "MOBA")
    float ObjectiveReplicationRadius = 2000.0f;

    // Tags para otimização
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Tags")
    FGameplayTagContainer HighPriorityTags;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Tags")
    FGameplayTagContainer LowPriorityTags;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Tags")
    FGameplayTagContainer CriticalTags;

    // Funções de configuração
    UFUNCTION(BlueprintCallable, Category = "Network Config")
    void ApplyOptimizationType(ESigilNetworkOptimization NewType);

    UFUNCTION(BlueprintCallable, Category = "Network Config")
    void SetMOBAOptimizations(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "Network Config")
    void SetMaxPlayers(int32 NewMaxPlayers);

    UFUNCTION(BlueprintCallable, Category = "Network Config")
    void ResetToDefaults();

    // Funções de consulta
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Network Config")
    float GetPriorityForTag(FGameplayTag Tag) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Network Config")
    float GetDistanceMultiplier(float Distance) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Network Config")
    float GetFrequencyForPriority(float Priority) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Network Config")
    bool ShouldReplicateAtDistance(float Distance) const;

    // Validação
    UFUNCTION(BlueprintCallable, Category = "Network Config")
    bool ValidateConfiguration() const;

    UFUNCTION(BlueprintCallable, Category = "Network Config")
    TArray<FString> GetConfigurationWarnings() const;

    // Estatísticas
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Network Config")
    float GetEstimatedBandwidthUsage() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Network Config")
    int32 GetEstimatedReplicationsPerSecond() const;

protected:
    // Configurações predefinidas
    void ApplyBasicOptimizations();
    void ApplyMOBAOptimizations();
    void ApplyCompetitiveOptimizations();
    void ApplyNoOptimizations();

    // Validação interna
    bool ValidatePriorities() const;
    bool ValidateDistances() const;
    bool ValidateFrequencies() const;
    bool ValidateBandwidth() const;

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Network Config")
    static USigilNetworkConfig* GetSigilNetworkConfig();

    // Constantes
    static const float MIN_REPLICATION_FREQUENCY;
    static const float MAX_REPLICATION_FREQUENCY;
    static const float MIN_DISTANCE;
    static const float MAX_DISTANCE;
    static const int32 MIN_PLAYERS;
    static const int32 MAX_PLAYERS;
};

// Estrutura para estatísticas de rede em tempo real
USTRUCT(BlueprintType)
struct AURACRON_API FSigilNetworkStats
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    float CurrentBandwidthUsage = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    int32 ReplicationsPerSecond = 0;

    UPROPERTY(BlueprintReadOnly)
    int32 ActiveConnections = 0;

    UPROPERTY(BlueprintReadOnly)
    float AverageLatency = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    float PacketLossPercentage = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    int32 DroppedReplications = 0;

    UPROPERTY(BlueprintReadOnly)
    float CompressionRatio = 0.0f;

    FSigilNetworkStats()
    {
        CurrentBandwidthUsage = 0.0f;
        ReplicationsPerSecond = 0;
        ActiveConnections = 0;
        AverageLatency = 0.0f;
        PacketLossPercentage = 0.0f;
        DroppedReplications = 0;
        CompressionRatio = 0.0f;
    }
};

// Delegados para eventos de rede
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnNetworkOptimizationChanged, ESigilNetworkOptimization, NewOptimization);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBandwidthLimitReached, float, CurrentUsage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnNetworkStatsUpdated, const FSigilNetworkStats&, Stats);