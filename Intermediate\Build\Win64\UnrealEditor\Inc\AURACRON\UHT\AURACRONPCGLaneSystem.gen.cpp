// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGLaneSystem.h"
#include "Engine/TimerHandle.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGLaneSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGLaneSystem();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGLaneSystem_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONDefensiveStructure();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONDeviceType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONLaneType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONActorArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONComponentArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONJungleCamp();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONLaneInfo();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONVectorArray();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAURACRONLaneType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONLaneType;
static UEnum* EAURACRONLaneType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONLaneType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONLaneType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONLaneType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONLaneType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONLaneType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONLaneType>()
{
	return EAURACRONLaneType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONLaneType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "BotLane.Comment", "// Diagonal inferior esquerda para superior direita (centro)\n" },
		{ "BotLane.DisplayName", "Bot Lane" },
		{ "BotLane.Name", "EAURACRONLaneType::BotLane" },
		{ "BotLane.ToolTip", "Diagonal inferior esquerda para superior direita (centro)" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de lanes baseados exatamente no layout do Summoner's Rift (LoL)\n */" },
#endif
		{ "Jungle.Comment", "// Alias para PrismalFlow (compatibilidade)\n" },
		{ "Jungle.DisplayName", "Jungle" },
		{ "Jungle.Name", "EAURACRONLaneType::Jungle" },
		{ "Jungle.ToolTip", "Alias para PrismalFlow (compatibilidade)" },
		{ "MidLane.Comment", "// Diagonal superior esquerda para inferior direita\n" },
		{ "MidLane.DisplayName", "Mid Lane" },
		{ "MidLane.Name", "EAURACRONLaneType::MidLane" },
		{ "MidLane.ToolTip", "Diagonal superior esquerda para inferior direita" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
		{ "PrismalFlow.Comment", "// Diagonal inferior direita para superior esquerda\n" },
		{ "PrismalFlow.DisplayName", "Prismal Flow" },
		{ "PrismalFlow.Name", "EAURACRONLaneType::PrismalFlow" },
		{ "PrismalFlow.ToolTip", "Diagonal inferior direita para superior esquerda" },
		{ "River.Comment", "// River equivalent - conecta objetivos principais\n" },
		{ "River.DisplayName", "River" },
		{ "River.Name", "EAURACRONLaneType::River" },
		{ "River.ToolTip", "River equivalent - conecta objetivos principais" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de lanes baseados exatamente no layout do Summoner's Rift (LoL)" },
#endif
		{ "TopLane.DisplayName", "Top Lane" },
		{ "TopLane.Name", "EAURACRONLaneType::TopLane" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONLaneType::TopLane", (int64)EAURACRONLaneType::TopLane },
		{ "EAURACRONLaneType::MidLane", (int64)EAURACRONLaneType::MidLane },
		{ "EAURACRONLaneType::BotLane", (int64)EAURACRONLaneType::BotLane },
		{ "EAURACRONLaneType::PrismalFlow", (int64)EAURACRONLaneType::PrismalFlow },
		{ "EAURACRONLaneType::River", (int64)EAURACRONLaneType::River },
		{ "EAURACRONLaneType::Jungle", (int64)EAURACRONLaneType::Jungle },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONLaneType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONLaneType",
	"EAURACRONLaneType",
	Z_Construct_UEnum_AURACRON_EAURACRONLaneType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONLaneType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONLaneType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONLaneType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONLaneType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONLaneType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONLaneType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONLaneType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONLaneType.InnerSingleton;
}
// ********** End Enum EAURACRONLaneType ***********************************************************

// ********** Begin Enum EAURACRONDefensiveStructure ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONDefensiveStructure;
static UEnum* EAURACRONDefensiveStructure_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONDefensiveStructure.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONDefensiveStructure.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONDefensiveStructure, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONDefensiveStructure"));
	}
	return Z_Registration_Info_UEnum_EAURACRONDefensiveStructure.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONDefensiveStructure>()
{
	return EAURACRONDefensiveStructure_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONDefensiveStructure_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de estruturas defensivas\n */" },
#endif
		{ "Inhibitor.DisplayName", "Inhibitor" },
		{ "Inhibitor.Name", "EAURACRONDefensiveStructure::Inhibitor" },
		{ "InhibitorTower.DisplayName", "Inhibitor Tower" },
		{ "InhibitorTower.Name", "EAURACRONDefensiveStructure::InhibitorTower" },
		{ "InnerTower.DisplayName", "Inner Tower" },
		{ "InnerTower.Name", "EAURACRONDefensiveStructure::InnerTower" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
		{ "Nexus.DisplayName", "Nexus" },
		{ "Nexus.Name", "EAURACRONDefensiveStructure::Nexus" },
		{ "OuterTower.DisplayName", "Outer Tower" },
		{ "OuterTower.Name", "EAURACRONDefensiveStructure::OuterTower" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de estruturas defensivas" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONDefensiveStructure::OuterTower", (int64)EAURACRONDefensiveStructure::OuterTower },
		{ "EAURACRONDefensiveStructure::InnerTower", (int64)EAURACRONDefensiveStructure::InnerTower },
		{ "EAURACRONDefensiveStructure::InhibitorTower", (int64)EAURACRONDefensiveStructure::InhibitorTower },
		{ "EAURACRONDefensiveStructure::Inhibitor", (int64)EAURACRONDefensiveStructure::Inhibitor },
		{ "EAURACRONDefensiveStructure::Nexus", (int64)EAURACRONDefensiveStructure::Nexus },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONDefensiveStructure_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONDefensiveStructure",
	"EAURACRONDefensiveStructure",
	Z_Construct_UEnum_AURACRON_EAURACRONDefensiveStructure_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONDefensiveStructure_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONDefensiveStructure_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONDefensiveStructure_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONDefensiveStructure()
{
	if (!Z_Registration_Info_UEnum_EAURACRONDefensiveStructure.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONDefensiveStructure.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONDefensiveStructure_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONDefensiveStructure.InnerSingleton;
}
// ********** End Enum EAURACRONDefensiveStructure *************************************************

// ********** Begin ScriptStruct FAURACRONVectorArray **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONVectorArray;
class UScriptStruct* FAURACRONVectorArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONVectorArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONVectorArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONVectorArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONVectorArray"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONVectorArray.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura wrapper para array de vetores (resolve problema UHT com TMap<Enum, TArray<Type>>)\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura wrapper para array de vetores (resolve problema UHT com TMap<Enum, TArray<Type>>)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vectors_MetaData[] = {
		{ "Category", "AURACRONVectorArray" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Vectors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Vectors;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONVectorArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::NewProp_Vectors_Inner = { "Vectors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::NewProp_Vectors = { "Vectors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONVectorArray, Vectors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vectors_MetaData), NewProp_Vectors_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::NewProp_Vectors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::NewProp_Vectors,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONVectorArray",
	Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::PropPointers),
	sizeof(FAURACRONVectorArray),
	alignof(FAURACRONVectorArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONVectorArray()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONVectorArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONVectorArray.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONVectorArray.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONVectorArray ************************************************

// ********** Begin ScriptStruct FAURACRONActorArray ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONActorArray;
class UScriptStruct* FAURACRONActorArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONActorArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONActorArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONActorArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONActorArray"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONActorArray.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONActorArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura wrapper para array de atores (resolve problema UHT com TMap<Enum, TArray<Type>>)\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura wrapper para array de atores (resolve problema UHT com TMap<Enum, TArray<Type>>)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Actors_MetaData[] = {
		{ "Category", "AURACRONActorArray" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Actors;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONActorArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::NewProp_Actors_Inner = { "Actors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::NewProp_Actors = { "Actors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONActorArray, Actors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Actors_MetaData), NewProp_Actors_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::NewProp_Actors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::NewProp_Actors,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONActorArray",
	Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::PropPointers),
	sizeof(FAURACRONActorArray),
	alignof(FAURACRONActorArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONActorArray()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONActorArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONActorArray.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONActorArray.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONActorArray *************************************************

// ********** Begin ScriptStruct FAURACRONComponentArray *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONComponentArray;
class UScriptStruct* FAURACRONComponentArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONComponentArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONComponentArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONComponentArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONComponentArray"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONComponentArray.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura wrapper para array de componentes (resolve problema UHT com TMap<Enum, TArray<Type>>)\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura wrapper para array de componentes (resolve problema UHT com TMap<Enum, TArray<Type>>)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Components_MetaData[] = {
		{ "Category", "AURACRONComponentArray" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Components_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Components;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONComponentArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::NewProp_Components_Inner = { "Components", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UActorComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::NewProp_Components = { "Components", nullptr, (EPropertyFlags)0x001000800000000d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONComponentArray, Components), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Components_MetaData), NewProp_Components_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::NewProp_Components_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::NewProp_Components,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONComponentArray",
	Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::PropPointers),
	sizeof(FAURACRONComponentArray),
	alignof(FAURACRONComponentArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONComponentArray()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONComponentArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONComponentArray.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONComponentArray.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONComponentArray *********************************************

// ********** Begin ScriptStruct FAURACRONLaneInfo *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONLaneInfo;
class UScriptStruct* FAURACRONLaneInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONLaneInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONLaneInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONLaneInfo, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONLaneInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONLaneInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Informa\xc3\xa7\xc3\xb5""es de uma lane adaptada para os 3 ambientes din\xc3\xa2micos\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de uma lane adaptada para os 3 ambientes din\xc3\xa2micos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneType_MetaData[] = {
		{ "Category", "AURACRONLaneInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo da lane */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo da lane" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LanePointsByEnvironment_MetaData[] = {
		{ "Category", "AURACRONLaneInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos que definem o caminho da lane para cada ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos que definem o caminho da lane para cada ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneWidth_MetaData[] = {
		{ "Category", "AURACRONLaneInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Largura da lane em cent\xc3\xadmetros (baseada no LoL) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Largura da lane em cent\xc3\xadmetros (baseada no LoL)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerPositionsByEnvironment_MetaData[] = {
		{ "Category", "AURACRONLaneInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xb5""es das torres nesta lane para cada ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xb5""es das torres nesta lane para cada ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinionSpawnPointsByEnvironment_MetaData[] = {
		{ "Category", "AURACRONLaneInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xb5""es dos minions spawn para cada ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xb5""es dos minions spawn para cada ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "AURACRONLaneInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se a lane est\xc3\xa1 ativa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se a lane est\xc3\xa1 ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEnvironment_MetaData[] = {
		{ "Category", "AURACRONLaneInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente atualmente ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente atualmente ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentCharacteristics_MetaData[] = {
		{ "Category", "AURACRONLaneInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Caracter\xc3\xadsticas espec\xc3\xad""ficas por ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Caracter\xc3\xadsticas espec\xc3\xad""ficas por ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LanePointsByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LanePointsByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LanePointsByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LanePointsByEnvironment;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LaneWidth;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TowerPositionsByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TowerPositionsByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TowerPositionsByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TowerPositionsByEnvironment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MinionSpawnPointsByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinionSpawnPointsByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinionSpawnPointsByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_MinionSpawnPointsByEnvironment;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEnvironment;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnvironmentCharacteristics_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentCharacteristics_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentCharacteristics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EnvironmentCharacteristics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONLaneInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONLaneInfo, LaneType), Z_Construct_UEnum_AURACRON_EAURACRONLaneType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneType_MetaData), NewProp_LaneType_MetaData) }; // 2142461620
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LanePointsByEnvironment_ValueProp = { "LanePointsByEnvironment", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONVectorArray, METADATA_PARAMS(0, nullptr) }; // 1198915140
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LanePointsByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LanePointsByEnvironment_Key_KeyProp = { "LanePointsByEnvironment_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 14946084
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LanePointsByEnvironment = { "LanePointsByEnvironment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONLaneInfo, LanePointsByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LanePointsByEnvironment_MetaData), NewProp_LanePointsByEnvironment_MetaData) }; // 14946084 1198915140
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LaneWidth = { "LaneWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONLaneInfo, LaneWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneWidth_MetaData), NewProp_LaneWidth_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_TowerPositionsByEnvironment_ValueProp = { "TowerPositionsByEnvironment", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONVectorArray, METADATA_PARAMS(0, nullptr) }; // 1198915140
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_TowerPositionsByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_TowerPositionsByEnvironment_Key_KeyProp = { "TowerPositionsByEnvironment_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 14946084
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_TowerPositionsByEnvironment = { "TowerPositionsByEnvironment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONLaneInfo, TowerPositionsByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerPositionsByEnvironment_MetaData), NewProp_TowerPositionsByEnvironment_MetaData) }; // 14946084 1198915140
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_MinionSpawnPointsByEnvironment_ValueProp = { "MinionSpawnPointsByEnvironment", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONVectorArray, METADATA_PARAMS(0, nullptr) }; // 1198915140
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_MinionSpawnPointsByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_MinionSpawnPointsByEnvironment_Key_KeyProp = { "MinionSpawnPointsByEnvironment_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 14946084
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_MinionSpawnPointsByEnvironment = { "MinionSpawnPointsByEnvironment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONLaneInfo, MinionSpawnPointsByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinionSpawnPointsByEnvironment_MetaData), NewProp_MinionSpawnPointsByEnvironment_MetaData) }; // 14946084 1198915140
void Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONLaneInfo*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONLaneInfo), &Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_CurrentEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_CurrentEnvironment = { "CurrentEnvironment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONLaneInfo, CurrentEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEnvironment_MetaData), NewProp_CurrentEnvironment_MetaData) }; // 14946084
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_EnvironmentCharacteristics_ValueProp = { "EnvironmentCharacteristics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_EnvironmentCharacteristics_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_EnvironmentCharacteristics_Key_KeyProp = { "EnvironmentCharacteristics_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 14946084
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_EnvironmentCharacteristics = { "EnvironmentCharacteristics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONLaneInfo, EnvironmentCharacteristics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentCharacteristics_MetaData), NewProp_EnvironmentCharacteristics_MetaData) }; // 14946084
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LanePointsByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LanePointsByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LanePointsByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LanePointsByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_LaneWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_TowerPositionsByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_TowerPositionsByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_TowerPositionsByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_TowerPositionsByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_MinionSpawnPointsByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_MinionSpawnPointsByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_MinionSpawnPointsByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_MinionSpawnPointsByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_CurrentEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_CurrentEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_EnvironmentCharacteristics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_EnvironmentCharacteristics_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_EnvironmentCharacteristics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewProp_EnvironmentCharacteristics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONLaneInfo",
	Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::PropPointers),
	sizeof(FAURACRONLaneInfo),
	alignof(FAURACRONLaneInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONLaneInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONLaneInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONLaneInfo.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONLaneInfo.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONLaneInfo ***************************************************

// ********** Begin ScriptStruct FAURACRONJungleCamp ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONJungleCamp;
class UScriptStruct* FAURACRONJungleCamp::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONJungleCamp.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONJungleCamp.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONJungleCamp, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONJungleCamp"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONJungleCamp.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Informa\xc3\xa7\xc3\xb5""es de um camp da jungle\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de um camp da jungle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "AURACRONJungleCamp" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o do camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o do camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "AURACRONJungleCamp" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonsterType_MetaData[] = {
		{ "Category", "AURACRONJungleCamp" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de monstros no camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de monstros no camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "AURACRONJungleCamp" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de respawn em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de respawn em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsBuffCamp_MetaData[] = {
		{ "Category", "AURACRONJungleCamp" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se \xc3\xa9 um camp de buff */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se \xc3\xa9 um camp de buff" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DifficultyLevel_MetaData[] = {
		{ "Category", "AURACRONJungleCamp" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel de dificuldade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel de dificuldade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MonsterType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static void NewProp_bIsBuffCamp_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsBuffCamp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DifficultyLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONJungleCamp>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCamp, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCamp, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_MonsterType = { "MonsterType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCamp, MonsterType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonsterType_MetaData), NewProp_MonsterType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCamp, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_bIsBuffCamp_SetBit(void* Obj)
{
	((FAURACRONJungleCamp*)Obj)->bIsBuffCamp = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_bIsBuffCamp = { "bIsBuffCamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONJungleCamp), &Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_bIsBuffCamp_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsBuffCamp_MetaData), NewProp_bIsBuffCamp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_DifficultyLevel = { "DifficultyLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCamp, DifficultyLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DifficultyLevel_MetaData), NewProp_DifficultyLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_MonsterType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_bIsBuffCamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewProp_DifficultyLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONJungleCamp",
	Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::PropPointers),
	sizeof(FAURACRONJungleCamp),
	alignof(FAURACRONJungleCamp),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONJungleCamp()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONJungleCamp.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONJungleCamp.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONJungleCamp.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONJungleCamp *************************************************

// ********** Begin Class AAURACRONPCGLaneSystem Function CalculateLaneIntersectionPoints **********
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics
{
	struct AURACRONPCGLaneSystem_eventCalculateLaneIntersectionPoints_Parms
	{
		EAURACRONDeviceType DeviceType;
		float QualityMultiplier;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular pontos de intersec\xc3\xa7\xc3\xa3o baseados no tipo de dispositivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular pontos de intersec\xc3\xa7\xc3\xa3o baseados no tipo de dispositivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_DeviceType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DeviceType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::NewProp_DeviceType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::NewProp_DeviceType = { "DeviceType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventCalculateLaneIntersectionPoints_Parms, DeviceType), Z_Construct_UEnum_AURACRON_EAURACRONDeviceType, METADATA_PARAMS(0, nullptr) }; // 4180088235
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::NewProp_QualityMultiplier = { "QualityMultiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventCalculateLaneIntersectionPoints_Parms, QualityMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventCalculateLaneIntersectionPoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::NewProp_DeviceType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::NewProp_DeviceType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::NewProp_QualityMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "CalculateLaneIntersectionPoints", Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::AURACRONPCGLaneSystem_eventCalculateLaneIntersectionPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::AURACRONPCGLaneSystem_eventCalculateLaneIntersectionPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execCalculateLaneIntersectionPoints)
{
	P_GET_ENUM(EAURACRONDeviceType,Z_Param_DeviceType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_QualityMultiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->CalculateLaneIntersectionPoints(EAURACRONDeviceType(Z_Param_DeviceType),Z_Param_QualityMultiplier);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function CalculateLaneIntersectionPoints ************

// ********** Begin Class AAURACRONPCGLaneSystem Function GenerateDefensiveStructures **************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateDefensiveStructures_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar estruturas defensivas para todos os ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar estruturas defensivas para todos os ambientes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateDefensiveStructures_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "GenerateDefensiveStructures", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateDefensiveStructures_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateDefensiveStructures_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateDefensiveStructures()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateDefensiveStructures_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execGenerateDefensiveStructures)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateDefensiveStructures();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function GenerateDefensiveStructures ****************

// ********** Begin Class AAURACRONPCGLaneSystem Function GenerateJungleCamps **********************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateJungleCamps_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar jungle camps para ambiente espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar jungle camps para ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateJungleCamps_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "GenerateJungleCamps", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateJungleCamps_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateJungleCamps_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateJungleCamps()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateJungleCamps_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execGenerateJungleCamps)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateJungleCamps();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function GenerateJungleCamps ************************

// ********** Begin Class AAURACRONPCGLaneSystem Function GenerateLaneIntersectionsBasedOnRenderingCapacity 
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneIntersectionsBasedOnRenderingCapacity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar intersec\xc3\xa7\xc3\xb5""es de trilhos baseadas na capacidade de renderiza\xc3\xa7\xc3\xa3o do dispositivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar intersec\xc3\xa7\xc3\xb5""es de trilhos baseadas na capacidade de renderiza\xc3\xa7\xc3\xa3o do dispositivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneIntersectionsBasedOnRenderingCapacity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "GenerateLaneIntersectionsBasedOnRenderingCapacity", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneIntersectionsBasedOnRenderingCapacity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneIntersectionsBasedOnRenderingCapacity_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneIntersectionsBasedOnRenderingCapacity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneIntersectionsBasedOnRenderingCapacity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execGenerateLaneIntersectionsBasedOnRenderingCapacity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateLaneIntersectionsBasedOnRenderingCapacity();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function GenerateLaneIntersectionsBasedOnRenderingCapacity 

// ********** Begin Class AAURACRONPCGLaneSystem Function GenerateLanesForEnvironment **************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics
{
	struct AURACRONPCGLaneSystem_eventGenerateLanesForEnvironment_Parms
	{
		EAURACRONEnvironmentType Environment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar lanes espec\xc3\xad""ficas para um ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar lanes espec\xc3\xad""ficas para um ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventGenerateLanesForEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 14946084
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "GenerateLanesForEnvironment", Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::AURACRONPCGLaneSystem_eventGenerateLanesForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::AURACRONPCGLaneSystem_eventGenerateLanesForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execGenerateLanesForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateLanesForEnvironment(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function GenerateLanesForEnvironment ****************

// ********** Begin Class AAURACRONPCGLaneSystem Function GenerateLaneSystem ***********************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar sistema completo de lanes para todos os 3 ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar sistema completo de lanes para todos os 3 ambientes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "GenerateLaneSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execGenerateLaneSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateLaneSystem();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function GenerateLaneSystem *************************

// ********** Begin Class AAURACRONPCGLaneSystem Function GetClosestLane ***************************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics
{
	struct AURACRONPCGLaneSystem_eventGetClosestLane_Parms
	{
		FVector Position;
		EAURACRONLaneType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter lane mais pr\xc3\xb3xima de uma posi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter lane mais pr\xc3\xb3xima de uma posi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventGetClosestLane_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventGetClosestLane_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONLaneType, METADATA_PARAMS(0, nullptr) }; // 2142461620
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "GetClosestLane", Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::AURACRONPCGLaneSystem_eventGetClosestLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::AURACRONPCGLaneSystem_eventGetClosestLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execGetClosestLane)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONLaneType*)Z_Param__Result=P_THIS->GetClosestLane(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function GetClosestLane *****************************

// ********** Begin Class AAURACRONPCGLaneSystem Function GetCurrentEnvironment ********************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics
{
	struct AURACRONPCGLaneSystem_eventGetCurrentEnvironment_Parms
	{
		EAURACRONEnvironmentType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter ambiente atualmente ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter ambiente atualmente ativo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventGetCurrentEnvironment_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 14946084
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "GetCurrentEnvironment", Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::AURACRONPCGLaneSystem_eventGetCurrentEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::AURACRONPCGLaneSystem_eventGetCurrentEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execGetCurrentEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONEnvironmentType*)Z_Param__Result=P_THIS->GetCurrentEnvironment();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function GetCurrentEnvironment **********************

// ********** Begin Class AAURACRONPCGLaneSystem Function GetJungleCamps ***************************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics
{
	struct AURACRONPCGLaneSystem_eventGetJungleCamps_Parms
	{
		TArray<FAURACRONJungleCamp> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter todos os camps da jungle */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todos os camps da jungle" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONJungleCamp, METADATA_PARAMS(0, nullptr) }; // 2374512381
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventGetJungleCamps_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2374512381
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "GetJungleCamps", Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::AURACRONPCGLaneSystem_eventGetJungleCamps_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::AURACRONPCGLaneSystem_eventGetJungleCamps_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execGetJungleCamps)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONJungleCamp>*)Z_Param__Result=P_THIS->GetJungleCamps();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function GetJungleCamps *****************************

// ********** Begin Class AAURACRONPCGLaneSystem Function GetLaneInfo ******************************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics
{
	struct AURACRONPCGLaneSystem_eventGetLaneInfo_Parms
	{
		EAURACRONLaneType LaneType;
		FAURACRONLaneInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter informa\xc3\xa7\xc3\xb5""es de uma lane espec\xc3\xad""fica */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter informa\xc3\xa7\xc3\xb5""es de uma lane espec\xc3\xad""fica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventGetLaneInfo_Parms, LaneType), Z_Construct_UEnum_AURACRON_EAURACRONLaneType, METADATA_PARAMS(0, nullptr) }; // 2142461620
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventGetLaneInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONLaneInfo, METADATA_PARAMS(0, nullptr) }; // 4234791048
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "GetLaneInfo", Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::AURACRONPCGLaneSystem_eventGetLaneInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::AURACRONPCGLaneSystem_eventGetLaneInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execGetLaneInfo)
{
	P_GET_ENUM(EAURACRONLaneType,Z_Param_LaneType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONLaneInfo*)Z_Param__Result=P_THIS->GetLaneInfo(EAURACRONLaneType(Z_Param_LaneType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function GetLaneInfo ********************************

// ********** Begin Class AAURACRONPCGLaneSystem Function GetLanePointsForEnvironment **************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics
{
	struct AURACRONPCGLaneSystem_eventGetLanePointsForEnvironment_Parms
	{
		EAURACRONLaneType LaneType;
		EAURACRONEnvironmentType Environment;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter pontos da lane para ambiente espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter pontos da lane para ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventGetLanePointsForEnvironment_Parms, LaneType), Z_Construct_UEnum_AURACRON_EAURACRONLaneType, METADATA_PARAMS(0, nullptr) }; // 2142461620
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventGetLanePointsForEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 14946084
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventGetLanePointsForEnvironment_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_Environment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "GetLanePointsForEnvironment", Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::AURACRONPCGLaneSystem_eventGetLanePointsForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::AURACRONPCGLaneSystem_eventGetLanePointsForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execGetLanePointsForEnvironment)
{
	P_GET_ENUM(EAURACRONLaneType,Z_Param_LaneType);
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GetLanePointsForEnvironment(EAURACRONLaneType(Z_Param_LaneType),EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function GetLanePointsForEnvironment ****************

// ********** Begin Class AAURACRONPCGLaneSystem Function IsPositionInLane *************************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics
{
	struct AURACRONPCGLaneSystem_eventIsPositionInLane_Parms
	{
		FVector Position;
		EAURACRONLaneType LaneType;
		float Tolerance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se posi\xc3\xa7\xc3\xa3o est\xc3\xa1 em uma lane (considerando ambiente atual) */" },
#endif
		{ "CPP_Default_Tolerance", "400.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se posi\xc3\xa7\xc3\xa3o est\xc3\xa1 em uma lane (considerando ambiente atual)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventIsPositionInLane_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventIsPositionInLane_Parms, LaneType), Z_Construct_UEnum_AURACRON_EAURACRONLaneType, METADATA_PARAMS(0, nullptr) }; // 2142461620
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventIsPositionInLane_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGLaneSystem_eventIsPositionInLane_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGLaneSystem_eventIsPositionInLane_Parms), &Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "IsPositionInLane", Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::AURACRONPCGLaneSystem_eventIsPositionInLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::AURACRONPCGLaneSystem_eventIsPositionInLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execIsPositionInLane)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_ENUM(EAURACRONLaneType,Z_Param_LaneType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionInLane(Z_Param_Out_Position,EAURACRONLaneType(Z_Param_LaneType),Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function IsPositionInLane ***************************

// ********** Begin Class AAURACRONPCGLaneSystem Function TransitionToEnvironment ******************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics
{
	struct AURACRONPCGLaneSystem_eventTransitionToEnvironment_Parms
	{
		EAURACRONEnvironmentType NewEnvironment;
		float TransitionDuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transicionar para novo ambiente */" },
#endif
		{ "CPP_Default_TransitionDuration", "30.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transicionar para novo ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewEnvironment;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::NewProp_NewEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::NewProp_NewEnvironment = { "NewEnvironment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventTransitionToEnvironment_Parms, NewEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 14946084
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::NewProp_TransitionDuration = { "TransitionDuration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventTransitionToEnvironment_Parms, TransitionDuration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::NewProp_NewEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::NewProp_NewEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::NewProp_TransitionDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "TransitionToEnvironment", Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::AURACRONPCGLaneSystem_eventTransitionToEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::AURACRONPCGLaneSystem_eventTransitionToEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execTransitionToEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_NewEnvironment);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TransitionDuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TransitionToEnvironment(EAURACRONEnvironmentType(Z_Param_NewEnvironment),Z_Param_TransitionDuration);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function TransitionToEnvironment ********************

// ********** Begin Class AAURACRONPCGLaneSystem Function UpdateForMapPhase ************************
struct Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics
{
	struct AURACRONPCGLaneSystem_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGLaneSystem_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 3682643231
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGLaneSystem, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::AURACRONPCGLaneSystem_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::AURACRONPCGLaneSystem_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGLaneSystem::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGLaneSystem Function UpdateForMapPhase **************************

// ********** Begin Class AAURACRONPCGLaneSystem ***************************************************
void AAURACRONPCGLaneSystem::StaticRegisterNativesAAURACRONPCGLaneSystem()
{
	UClass* Class = AAURACRONPCGLaneSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateLaneIntersectionPoints", &AAURACRONPCGLaneSystem::execCalculateLaneIntersectionPoints },
		{ "GenerateDefensiveStructures", &AAURACRONPCGLaneSystem::execGenerateDefensiveStructures },
		{ "GenerateJungleCamps", &AAURACRONPCGLaneSystem::execGenerateJungleCamps },
		{ "GenerateLaneIntersectionsBasedOnRenderingCapacity", &AAURACRONPCGLaneSystem::execGenerateLaneIntersectionsBasedOnRenderingCapacity },
		{ "GenerateLanesForEnvironment", &AAURACRONPCGLaneSystem::execGenerateLanesForEnvironment },
		{ "GenerateLaneSystem", &AAURACRONPCGLaneSystem::execGenerateLaneSystem },
		{ "GetClosestLane", &AAURACRONPCGLaneSystem::execGetClosestLane },
		{ "GetCurrentEnvironment", &AAURACRONPCGLaneSystem::execGetCurrentEnvironment },
		{ "GetJungleCamps", &AAURACRONPCGLaneSystem::execGetJungleCamps },
		{ "GetLaneInfo", &AAURACRONPCGLaneSystem::execGetLaneInfo },
		{ "GetLanePointsForEnvironment", &AAURACRONPCGLaneSystem::execGetLanePointsForEnvironment },
		{ "IsPositionInLane", &AAURACRONPCGLaneSystem::execIsPositionInLane },
		{ "TransitionToEnvironment", &AAURACRONPCGLaneSystem::execTransitionToEnvironment },
		{ "UpdateForMapPhase", &AAURACRONPCGLaneSystem::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGLaneSystem;
UClass* AAURACRONPCGLaneSystem::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGLaneSystem;
	if (!Z_Registration_Info_UClass_AAURACRONPCGLaneSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGLaneSystem"),
			Z_Registration_Info_UClass_AAURACRONPCGLaneSystem.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGLaneSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGLaneSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGLaneSystem_NoRegister()
{
	return AAURACRONPCGLaneSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Sistema de lanes e jungle para AURACRON\n * Implementa layout baseado em LoL/Dota 2 com adapta\xc3\xa7\xc3\xb5""es para 3 ambientes\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGLaneSystem.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de lanes e jungle para AURACRON\nImplementa layout baseado em LoL/Dota 2 com adapta\xc3\xa7\xc3\xb5""es para 3 ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneSplines_MetaData[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Splines das lanes */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Splines das lanes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneMeshComponents_MetaData[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de mesh para visualiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de mesh para visualiza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneInfos_MetaData[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Informa\xc3\xa7\xc3\xb5""es das lanes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es das lanes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JungleCamps_MetaData[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camps da jungle */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camps da jungle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerate_MetaData[] = {
		{ "Category", "AURACRON|LaneSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve gerar automaticamente no BeginPlay */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve gerar automaticamente no BeginPlay" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEnvironment_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente atualmente ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente atualmente ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefensiveStructuresByEnvironment_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estruturas defensivas geradas por ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas defensivas geradas por ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedComponentsByEnvironment_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes gerados dinamicamente por ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes gerados dinamicamente por ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentTransitionTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para transi\xc3\xa7\xc3\xa3o entre ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para transi\xc3\xa7\xc3\xa3o entre ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTransitionDuration_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o da transi\xc3\xa7\xc3\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o da transi\xc3\xa7\xc3\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetEnvironment_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente de destino durante transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGLaneSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente de destino durante transi\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LaneSplines_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneSplines_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneSplines_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LaneSplines;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LaneMeshComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LaneMeshComponents;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LaneInfos_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneInfos_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneInfos_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LaneInfos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_JungleCamps_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_JungleCamps;
	static void NewProp_bAutoGenerate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerate;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEnvironment;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefensiveStructuresByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefensiveStructuresByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefensiveStructuresByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_DefensiveStructuresByEnvironment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GeneratedComponentsByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_GeneratedComponentsByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GeneratedComponentsByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_GeneratedComponentsByEnvironment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EnvironmentTransitionTimer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentTransitionDuration;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetEnvironment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_CalculateLaneIntersectionPoints, "CalculateLaneIntersectionPoints" }, // 3550629651
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateDefensiveStructures, "GenerateDefensiveStructures" }, // 1819308431
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateJungleCamps, "GenerateJungleCamps" }, // 467631198
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneIntersectionsBasedOnRenderingCapacity, "GenerateLaneIntersectionsBasedOnRenderingCapacity" }, // 336408782
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLanesForEnvironment, "GenerateLanesForEnvironment" }, // 926155986
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_GenerateLaneSystem, "GenerateLaneSystem" }, // 2268868634
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetClosestLane, "GetClosestLane" }, // 1807868733
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetCurrentEnvironment, "GetCurrentEnvironment" }, // 3612308775
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetJungleCamps, "GetJungleCamps" }, // 3674538302
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLaneInfo, "GetLaneInfo" }, // 3844634574
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_GetLanePointsForEnvironment, "GetLanePointsForEnvironment" }, // 1428377942
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_IsPositionInLane, "IsPositionInLane" }, // 2270375116
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_TransitionToEnvironment, "TransitionToEnvironment" }, // 1734718774
		{ &Z_Construct_UFunction_AAURACRONPCGLaneSystem_UpdateForMapPhase, "UpdateForMapPhase" }, // 1355953116
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGLaneSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneSplines_ValueProp = { "LaneSplines", nullptr, (EPropertyFlags)0x00000000000a0009, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneSplines_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneSplines_Key_KeyProp = { "LaneSplines_Key", nullptr, (EPropertyFlags)0x00000000000a0009, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONLaneType, METADATA_PARAMS(0, nullptr) }; // 2142461620
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneSplines = { "LaneSplines", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, LaneSplines), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneSplines_MetaData), NewProp_LaneSplines_MetaData) }; // 2142461620
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneMeshComponents_Inner = { "LaneMeshComponents", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneMeshComponents = { "LaneMeshComponents", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, LaneMeshComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneMeshComponents_MetaData), NewProp_LaneMeshComponents_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneInfos_ValueProp = { "LaneInfos", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONLaneInfo, METADATA_PARAMS(0, nullptr) }; // 4234791048
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneInfos_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneInfos_Key_KeyProp = { "LaneInfos_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONLaneType, METADATA_PARAMS(0, nullptr) }; // 2142461620
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneInfos = { "LaneInfos", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, LaneInfos), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneInfos_MetaData), NewProp_LaneInfos_MetaData) }; // 2142461620 4234791048
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_JungleCamps_Inner = { "JungleCamps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONJungleCamp, METADATA_PARAMS(0, nullptr) }; // 2374512381
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_JungleCamps = { "JungleCamps", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, JungleCamps), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JungleCamps_MetaData), NewProp_JungleCamps_MetaData) }; // 2374512381
void Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_bAutoGenerate_SetBit(void* Obj)
{
	((AAURACRONPCGLaneSystem*)Obj)->bAutoGenerate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_bAutoGenerate = { "bAutoGenerate", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGLaneSystem), &Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_bAutoGenerate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerate_MetaData), NewProp_bAutoGenerate_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_CurrentEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_CurrentEnvironment = { "CurrentEnvironment", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, CurrentEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEnvironment_MetaData), NewProp_CurrentEnvironment_MetaData) }; // 14946084
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 3682643231
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_DefensiveStructuresByEnvironment_ValueProp = { "DefensiveStructuresByEnvironment", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONActorArray, METADATA_PARAMS(0, nullptr) }; // 814360523
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_DefensiveStructuresByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_DefensiveStructuresByEnvironment_Key_KeyProp = { "DefensiveStructuresByEnvironment_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 14946084
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_DefensiveStructuresByEnvironment = { "DefensiveStructuresByEnvironment", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, DefensiveStructuresByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefensiveStructuresByEnvironment_MetaData), NewProp_DefensiveStructuresByEnvironment_MetaData) }; // 14946084 814360523
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_GeneratedComponentsByEnvironment_ValueProp = { "GeneratedComponentsByEnvironment", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONComponentArray, METADATA_PARAMS(0, nullptr) }; // 2958404771
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_GeneratedComponentsByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_GeneratedComponentsByEnvironment_Key_KeyProp = { "GeneratedComponentsByEnvironment_Key", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 14946084
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_GeneratedComponentsByEnvironment = { "GeneratedComponentsByEnvironment", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, GeneratedComponentsByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedComponentsByEnvironment_MetaData), NewProp_GeneratedComponentsByEnvironment_MetaData) }; // 14946084 2958404771
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_EnvironmentTransitionTimer = { "EnvironmentTransitionTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, EnvironmentTransitionTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentTransitionTimer_MetaData), NewProp_EnvironmentTransitionTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_CurrentTransitionDuration = { "CurrentTransitionDuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, CurrentTransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTransitionDuration_MetaData), NewProp_CurrentTransitionDuration_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_TargetEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_TargetEnvironment = { "TargetEnvironment", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGLaneSystem, TargetEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetEnvironment_MetaData), NewProp_TargetEnvironment_MetaData) }; // 14946084
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneSplines_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneSplines_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneSplines_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneSplines,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneMeshComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneMeshComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneInfos_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneInfos_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneInfos_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_LaneInfos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_JungleCamps_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_JungleCamps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_bAutoGenerate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_CurrentEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_CurrentEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_DefensiveStructuresByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_DefensiveStructuresByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_DefensiveStructuresByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_DefensiveStructuresByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_GeneratedComponentsByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_GeneratedComponentsByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_GeneratedComponentsByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_GeneratedComponentsByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_EnvironmentTransitionTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_CurrentTransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_TargetEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::NewProp_TargetEnvironment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::ClassParams = {
	&AAURACRONPCGLaneSystem::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGLaneSystem()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGLaneSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGLaneSystem.OuterSingleton, Z_Construct_UClass_AAURACRONPCGLaneSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGLaneSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGLaneSystem);
AAURACRONPCGLaneSystem::~AAURACRONPCGLaneSystem() {}
// ********** End Class AAURACRONPCGLaneSystem *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONLaneType_StaticEnum, TEXT("EAURACRONLaneType"), &Z_Registration_Info_UEnum_EAURACRONLaneType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2142461620U) },
		{ EAURACRONDefensiveStructure_StaticEnum, TEXT("EAURACRONDefensiveStructure"), &Z_Registration_Info_UEnum_EAURACRONDefensiveStructure, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4214337204U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONVectorArray::StaticStruct, Z_Construct_UScriptStruct_FAURACRONVectorArray_Statics::NewStructOps, TEXT("AURACRONVectorArray"), &Z_Registration_Info_UScriptStruct_FAURACRONVectorArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONVectorArray), 1198915140U) },
		{ FAURACRONActorArray::StaticStruct, Z_Construct_UScriptStruct_FAURACRONActorArray_Statics::NewStructOps, TEXT("AURACRONActorArray"), &Z_Registration_Info_UScriptStruct_FAURACRONActorArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONActorArray), 814360523U) },
		{ FAURACRONComponentArray::StaticStruct, Z_Construct_UScriptStruct_FAURACRONComponentArray_Statics::NewStructOps, TEXT("AURACRONComponentArray"), &Z_Registration_Info_UScriptStruct_FAURACRONComponentArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONComponentArray), 2958404771U) },
		{ FAURACRONLaneInfo::StaticStruct, Z_Construct_UScriptStruct_FAURACRONLaneInfo_Statics::NewStructOps, TEXT("AURACRONLaneInfo"), &Z_Registration_Info_UScriptStruct_FAURACRONLaneInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONLaneInfo), 4234791048U) },
		{ FAURACRONJungleCamp::StaticStruct, Z_Construct_UScriptStruct_FAURACRONJungleCamp_Statics::NewStructOps, TEXT("AURACRONJungleCamp"), &Z_Registration_Info_UScriptStruct_FAURACRONJungleCamp, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONJungleCamp), 2374512381U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGLaneSystem, AAURACRONPCGLaneSystem::StaticClass, TEXT("AAURACRONPCGLaneSystem"), &Z_Registration_Info_UClass_AAURACRONPCGLaneSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGLaneSystem), 281334589U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h__Script_AURACRON_3481689791(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGLaneSystem_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
