// AURACRONMovementComponent.h
// Sistema de Sígilos AURACRON - Componente de Movimento Customizado UE 5.6
// Implementação robusta para movimento específico do AURACRON

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Data/AURACRONEnums.h"
#include "AURACRONMovementComponent.generated.h"

// Forward Declarations
class UAURACRONSigilComponent;

/**
 * Estrutura para modificadores temporários de velocidade
 */
USTRUCT()
struct AURACRON_API FSpeedModifier
{
    GENERATED_BODY()

    float Multiplier;
    float TimeRemaining;
    FName Name;

    FSpeedModifier()
    {
        Multiplier = 1.0f;
        TimeRemaining = 0.0f;
        Name = NAME_None;
    }

    FSpeedModifier(float InMultiplier, float InDuration, FName InName)
    {
        Multiplier = InMultiplier;
        TimeRemaining = InDuration;
        Name = InName;
    }
};

/**
 * Estados especiais de movimento do AURACRON
 */
UENUM(BlueprintType)
enum class EAURACRONMovementState : uint8
{
    Normal          UMETA(DisplayName = "Normal"),          // Movimento padrão
    PrismalFlow     UMETA(DisplayName = "Prismal Flow"),   // Movimento no fluxo prismático
    SigilDash       UMETA(DisplayName = "Sigil Dash"),     // Dash de Sígilo
    EnvironmentBoost UMETA(DisplayName = "Environment Boost"), // Boost de ambiente
    Stunned         UMETA(DisplayName = "Stunned"),        // Atordoado
    Rooted          UMETA(DisplayName = "Rooted")          // Enraizado
};

/**
 * Configurações de movimento para diferentes ambientes
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONEnvironmentMovementConfig
{
    GENERATED_BODY()

    /** Multiplicador de velocidade para este ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movimento")
    float SpeedMultiplier;

    /** Multiplicador de aceleração */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movimento")
    float AccelerationMultiplier;

    /** Multiplicador de força de pulo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movimento")
    float JumpForceMultiplier;

    /** Se permite voo neste ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movimento")
    bool bAllowsFlight;

    /** Construtor padrão */
    FAURACRONEnvironmentMovementConfig()
    {
        SpeedMultiplier = 1.0f;
        AccelerationMultiplier = 1.0f;
        JumpForceMultiplier = 1.0f;
        bAllowsFlight = false;
    }
};

/**
 * Componente de movimento customizado para personagens AURACRON
 * Implementa mecânicas específicas como movimento no Fluxo Prismal e efeitos de Sígilos
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AURACRON_API UAURACRONMovementComponent : public UCharacterMovementComponent
{
    GENERATED_BODY()

public:
    UAURACRONMovementComponent();

    // UCharacterMovementComponent interface
    virtual void BeginPlay() override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
    virtual float GetMaxSpeed() const override;
    virtual float GetMaxAcceleration() const override;
    virtual float GetMaxBrakingDeceleration() const override;

    // Network Replication
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    // Estados de Movimento
    /** Define o estado atual de movimento */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Movimento", Server, Reliable)
    void SetMovementState(EAURACRONMovementState NewState);

    /** Obtém o estado atual de movimento */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Movimento")
    EAURACRONMovementState GetMovementState() const;

    // Ambiente
    /** Define o ambiente atual */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Movimento", Server, Reliable)
    void SetCurrentEnvironment(EAURACRONEnvironmentType Environment);

    /** Obtém o ambiente atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Movimento")
    EAURACRONEnvironmentType GetCurrentEnvironment() const;

    // Fluxo Prismal
    /** Entra no fluxo prismático */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Movimento|Fluxo", Server, Reliable)
    void EnterPrismalFlow(FVector FlowDirection, float FlowSpeed = 1.0f);

    /** Sai do fluxo prismático */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Movimento|Fluxo", Server, Reliable)
    void ExitPrismalFlow();

    /** Verifica se está no fluxo prismático */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Movimento|Fluxo")
    bool IsInPrismalFlow() const;

    // Dash de Sígilo
    /** Executa um dash de Sígilo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Movimento|Dash", Server, Reliable)
    void PerformSigilDash(FVector Direction, float Distance = 800.0f, float Duration = 0.3f);

    /** Verifica se pode executar dash */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Movimento|Dash")
    bool CanPerformDash() const;

    // Modificadores de Velocidade
    /** Aplica um modificador temporário de velocidade */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Movimento|Modificadores", Server, Reliable)
    void ApplySpeedModifier(float Multiplier, float Duration, FName ModifierName);

    /** Remove um modificador de velocidade */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Movimento|Modificadores", Server, Reliable)
    void RemoveSpeedModifier(FName ModifierName);

    /** Obtém a velocidade modificada atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Movimento|Modificadores")
    float GetModifiedSpeed() const;

    // Eventos Blueprint
    /** Evento chamado quando o estado de movimento muda */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Eventos")
    void OnMovementStateChanged(EAURACRONMovementState OldState, EAURACRONMovementState NewState);

    /** Evento chamado quando entra no fluxo prismático */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Eventos")
    void OnEnteredPrismalFlow(FVector FlowDirection, float FlowSpeed);

    /** Evento chamado quando sai do fluxo prismático */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Eventos")
    void OnExitedPrismalFlow();

    /** Evento chamado quando executa dash */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Eventos")
    void OnSigilDashPerformed(FVector Direction, float Distance);

protected:
    /** Estado atual de movimento */
    UPROPERTY(ReplicatedUsing = OnRep_MovementState, BlueprintReadOnly, Category = "AURACRON|Movimento")
    EAURACRONMovementState CurrentMovementState;

    /** Ambiente atual */
    UPROPERTY(ReplicatedUsing = OnRep_CurrentEnvironment, BlueprintReadOnly, Category = "AURACRON|Movimento")
    EAURACRONEnvironmentType CurrentEnvironment;

    /** Configurações de movimento por ambiente */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Movimento|Configuração")
    TMap<EAURACRONEnvironmentType, FAURACRONEnvironmentMovementConfig> EnvironmentConfigs;

    // Fluxo Prismal
    /** Se está atualmente no fluxo prismático */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Movimento|Fluxo")
    bool bIsInPrismalFlow;

    /** Direção do fluxo prismático */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Movimento|Fluxo")
    FVector PrismalFlowDirection;

    /** Velocidade do fluxo prismático */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Movimento|Fluxo")
    float PrismalFlowSpeed;

    /** Multiplicador de velocidade no fluxo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Movimento|Fluxo")
    float FlowSpeedMultiplier;

    // Dash
    /** Se está executando dash */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Movimento|Dash")
    bool bIsDashing;

    /** Tempo restante do dash */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Movimento|Dash")
    float DashTimeRemaining;

    /** Direção do dash */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Movimento|Dash")
    FVector DashDirection;

    /** Velocidade do dash */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Movimento|Dash")
    float DashSpeed;

    /** Cooldown do dash */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Movimento|Dash")
    float DashCooldown;

    /** Tempo restante do cooldown do dash */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Movimento|Dash")
    float DashCooldownRemaining;

    // Modificadores de Velocidade

    /** Modificadores ativos de velocidade */
    UPROPERTY()
    TArray<FSpeedModifier> ActiveSpeedModifiers;

    // Funções Internas
    /** Atualiza o movimento baseado no estado atual */
    void UpdateMovementForState(float DeltaTime);

    /** Atualiza modificadores de velocidade */
    void UpdateSpeedModifiers(float DeltaTime);

    /** Calcula o multiplicador total de velocidade */
    float CalculateTotalSpeedMultiplier() const;

    /** Aplica configurações do ambiente atual */
    void ApplyEnvironmentSettings();

    // Funções de Replicação
    UFUNCTION()
    void OnRep_MovementState();

    UFUNCTION()
    void OnRep_CurrentEnvironment();

private:
    /** Referência ao componente de Sígilos */
    UPROPERTY()
    TObjectPtr<UAURACRONSigilComponent> SigilComponent;

    /** Velocidade base original */
    float BaseMaxWalkSpeed;

    /** Aceleração base original */
    float BaseMaxAcceleration;
};
